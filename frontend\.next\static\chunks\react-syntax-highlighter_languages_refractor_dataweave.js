"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dataweave"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dataweave.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dataweave.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dataweave\ndataweave.displayName = 'dataweave'\ndataweave.aliases = []\nfunction dataweave(Prism) {\n  ;(function (Prism) {\n    Prism.languages.dataweave = {\n      url: /\\b[A-Za-z]+:\\/\\/[\\w/:.?=&-]+|\\burn:[\\w:.?=&-]+/,\n      property: {\n        pattern: /(?:\\b\\w+#)?(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|\\b\\w+)(?=\\s*[:@])/,\n        greedy: true\n      },\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      'mime-type':\n        /\\b(?:application|audio|image|multipart|text|video)\\/[\\w+-]+/,\n      date: {\n        pattern: /\\|[\\w:+-]+\\|/,\n        greedy: true\n      },\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      regex: {\n        pattern: /\\/(?:[^\\\\\\/\\r\\n]|\\\\[^\\r\\n])+\\//,\n        greedy: true\n      },\n      keyword:\n        /\\b(?:and|as|at|case|do|else|fun|if|input|is|match|not|ns|null|or|output|type|unless|update|using|var)\\b/,\n      function: /\\b[A-Z_]\\w*(?=\\s*\\()/i,\n      number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n      punctuation: /[{}[\\];(),.:@]/,\n      operator: /<<|>>|->|[<>~=]=?|!=|--?-?|\\+\\+?|!|\\?/,\n      boolean: /\\b(?:false|true)\\b/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dataweave.js\n"));

/***/ })

}]);