"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_pascal"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascal.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascal.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = pascal\npascal.displayName = 'pascal'\npascal.aliases = ['objectpascal']\nfunction pascal(Prism) {\n  // Based on Free Pascal\n  /* TODO\nSupport inline asm ?\n*/\n  Prism.languages.pascal = {\n    directive: {\n      pattern: /\\{\\$[\\s\\S]*?\\}/,\n      greedy: true,\n      alias: ['marco', 'property']\n    },\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)|\\{[\\s\\S]*?\\}|\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:'(?:''|[^'\\r\\n])*'(?!')|#[&$%]?[a-f\\d]+)+|\\^[a-z]/i,\n      greedy: true\n    },\n    asm: {\n      pattern: /(\\basm\\b)[\\s\\S]+?(?=\\bend\\s*[;[])/i,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n    },\n    keyword: [\n      {\n        // Turbo Pascal\n        pattern:\n          /(^|[^&])\\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Free Pascal\n        pattern: /(^|[^&])\\b(?:dispose|exit|false|new|true)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Object Pascal\n        pattern:\n          /(^|[^&])\\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Modifiers\n        pattern:\n          /(^|[^&])\\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\\b/i,\n        lookbehind: true\n      }\n    ],\n    number: [\n      // Hexadecimal, octal and binary\n      /(?:[&%]\\d+|\\$[a-f\\d]+)/i, // Decimal\n      /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?/i\n    ],\n    operator: [\n      /\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=]/,\n      {\n        pattern:\n          /(^|[^&])\\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\\b/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/\n  }\n  Prism.languages.pascal.asm.inside = Prism.languages.extend('pascal', {\n    asm: undefined,\n    keyword: undefined,\n    operator: undefined\n  })\n  Prism.languages.objectpascal = Prism.languages.pascal\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascal.js\n"));

/***/ })

}]);