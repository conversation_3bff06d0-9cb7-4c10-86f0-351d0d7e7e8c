"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_autoit"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/autoit.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/autoit.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = autoit\nautoit.displayName = 'autoit'\nautoit.aliases = []\nfunction autoit(Prism) {\n  Prism.languages.autoit = {\n    comment: [\n      /;.*/,\n      {\n        // The multi-line comments delimiters can actually be commented out with \";\"\n        pattern:\n          /(^[\\t ]*)#(?:comments-start|cs)[\\s\\S]*?^[ \\t]*#(?:ce|comments-end)/m,\n        lookbehind: true\n      }\n    ],\n    url: {\n      pattern: /(^[\\t ]*#include\\s+)(?:<[^\\r\\n>]+>|\"[^\\r\\n\"]+\")/m,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\1\\1|(?!\\1)[^\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        variable: /([%$@])\\w+\\1/\n      }\n    },\n    directive: {\n      pattern: /(^[\\t ]*)#[\\w-]+/m,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    function: /\\b\\w+(?=\\()/,\n    // Variables and macros\n    variable: /[$@]\\w+/,\n    keyword:\n      /\\b(?:Case|Const|Continue(?:Case|Loop)|Default|Dim|Do|Else(?:If)?|End(?:Func|If|Select|Switch|With)|Enum|Exit(?:Loop)?|For|Func|Global|If|In|Local|Next|Null|ReDim|Select|Static|Step|Switch|Then|To|Until|Volatile|WEnd|While|With)\\b/i,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    operator: /<[=>]?|[-+*\\/=&>]=?|[?^]|\\b(?:And|Not|Or)\\b/i,\n    punctuation: /[\\[\\]().,:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/autoit.js\n"));

/***/ })

}]);