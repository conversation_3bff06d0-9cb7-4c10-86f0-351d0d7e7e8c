"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_xquery"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xquery.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xquery.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = xquery\nxquery.displayName = 'xquery'\nxquery.aliases = []\nfunction xquery(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xquery = Prism.languages.extend('markup', {\n      'xquery-comment': {\n        pattern: /\\(:[\\s\\S]*?:\\)/,\n        greedy: true,\n        alias: 'comment'\n      },\n      string: {\n        pattern: /([\"'])(?:\\1\\1|(?!\\1)[\\s\\S])*\\1/,\n        greedy: true\n      },\n      extension: {\n        pattern: /\\(#.+?#\\)/,\n        alias: 'symbol'\n      },\n      variable: /\\$[-\\w:]+/,\n      axis: {\n        pattern:\n          /(^|[^-])(?:ancestor(?:-or-self)?|attribute|child|descendant(?:-or-self)?|following(?:-sibling)?|parent|preceding(?:-sibling)?|self)(?=::)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      'keyword-operator': {\n        pattern:\n          /(^|[^:-])\\b(?:and|castable as|div|eq|except|ge|gt|idiv|instance of|intersect|is|le|lt|mod|ne|or|union)\\b(?=$|[^:-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      keyword: {\n        pattern:\n          /(^|[^:-])\\b(?:as|ascending|at|base-uri|boundary-space|case|cast as|collation|construction|copy-namespaces|declare|default|descending|else|empty (?:greatest|least)|encoding|every|external|for|function|if|import|in|inherit|lax|let|map|module|namespace|no-inherit|no-preserve|option|order(?: by|ed|ing)?|preserve|return|satisfies|schema|some|stable|strict|strip|then|to|treat as|typeswitch|unordered|validate|variable|version|where|xquery)\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      function: /[\\w-]+(?::[\\w-]+)*(?=\\s*\\()/,\n      'xquery-element': {\n        pattern: /(element\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'tag'\n      },\n      'xquery-attribute': {\n        pattern: /(attribute\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'attr-name'\n      },\n      builtin: {\n        pattern:\n          /(^|[^:-])\\b(?:attribute|comment|document|element|processing-instruction|text|xs:(?:ENTITIES|ENTITY|ID|IDREFS?|NCName|NMTOKENS?|NOTATION|Name|QName|anyAtomicType|anyType|anyURI|base64Binary|boolean|byte|date|dateTime|dayTimeDuration|decimal|double|duration|float|gDay|gMonth|gMonthDay|gYear|gYearMonth|hexBinary|int|integer|language|long|negativeInteger|nonNegativeInteger|nonPositiveInteger|normalizedString|positiveInteger|short|string|time|token|unsigned(?:Byte|Int|Long|Short)|untyped(?:Atomic)?|yearMonthDuration))\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\.\\d+)?(?:E[+-]?\\d+)?/,\n      operator: [\n        /[+*=?|@]|\\.\\.?|:=|!=|<[=<]?|>[=>]?/,\n        {\n          pattern: /(\\s)-(?=\\s)/,\n          lookbehind: true\n        }\n      ],\n      punctuation: /[[\\](){},;:/]/\n    })\n    Prism.languages.xquery.tag.pattern =\n      /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/\n    Prism.languages.xquery['tag'].inside['attr-value'].pattern =\n      /=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['punctuation'] =\n      /^=\"|\"$/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['expression'] = {\n      // Allow for two levels of nesting\n      pattern: /\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}/,\n      inside: Prism.languages.xquery,\n      alias: 'language-xquery'\n    } // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (typeof token === 'string') {\n        return token\n      }\n      if (typeof token.content === 'string') {\n        return token.content\n      }\n      return token.content.map(stringifyToken).join('')\n    }\n    var walkTokens = function (tokens) {\n      var openedTags = []\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i]\n        var notTagNorBrace = false\n        if (typeof token !== 'string') {\n          if (\n            token.type === 'tag' &&\n            token.content[0] &&\n            token.content[0].type === 'tag'\n          ) {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (\n                openedTags.length > 0 &&\n                openedTags[openedTags.length - 1].tagName ===\n                  stringifyToken(token.content[0].content[1])\n              ) {\n                // Pop matching opening tag\n                openedTags.pop()\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                })\n              }\n            }\n          } else if (\n            openedTags.length > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '{' && // Ignore `{{`\n            (!tokens[i + 1] ||\n              tokens[i + 1].type !== 'punctuation' ||\n              tokens[i + 1].content !== '{') &&\n            (!tokens[i - 1] ||\n              tokens[i - 1].type !== 'plain-text' ||\n              tokens[i - 1].content !== '{')\n          ) {\n            // Here we might have entered an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces++\n          } else if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '}'\n          ) {\n            // Here we might have left an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces--\n          } else if (token.type !== 'comment') {\n            notTagNorBrace = true\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces === 0\n          ) {\n            // Here we are inside a tag, and not inside an XQuery expression.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token) // And merge text with adjacent text\n            if (\n              i < tokens.length - 1 &&\n              (typeof tokens[i + 1] === 'string' ||\n                tokens[i + 1].type === 'plain-text')\n            ) {\n              plainText += stringifyToken(tokens[i + 1])\n              tokens.splice(i + 1, 1)\n            }\n            if (\n              i > 0 &&\n              (typeof tokens[i - 1] === 'string' ||\n                tokens[i - 1].type === 'plain-text')\n            ) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText\n              tokens.splice(i - 1, 1)\n              i--\n            }\n            if (/^\\s+$/.test(plainText)) {\n              tokens[i] = plainText\n            } else {\n              tokens[i] = new Prism.Token(\n                'plain-text',\n                plainText,\n                null,\n                plainText\n              )\n            }\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content)\n        }\n      }\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'xquery') {\n        return\n      }\n      walkTokens(env.tokens)\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xquery.js\n"));

/***/ })

}]);