"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_hcl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/hcl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/hcl.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = hcl\nhcl.displayName = 'hcl'\nhcl.aliases = []\nfunction hcl(Prism) {\n  Prism.languages.hcl = {\n    comment: /(?:\\/\\/|#).*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    heredoc: {\n      pattern: /<<-?(\\w+\\b)[\\s\\S]*?^[ \\t]*\\1/m,\n      greedy: true,\n      alias: 'string'\n    },\n    keyword: [\n      {\n        pattern:\n          /(?:data|resource)\\s+(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")(?=\\s+\"[\\w-]+\"\\s+\\{)/i,\n        inside: {\n          type: {\n            pattern: /(resource|data|\\s+)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      {\n        pattern:\n          /(?:backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+(?=\\{)/i,\n        inside: {\n          type: {\n            pattern:\n              /(backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      /[\\w-]+(?=\\s+\\{)/\n    ],\n    property: [/[-\\w\\.]+(?=\\s*=(?!=))/, /\"(?:\\\\[\\s\\S]|[^\\\\\"])+\"(?=\\s*[:=])/],\n    string: {\n      pattern:\n        /\"(?:[^\\\\$\"]|\\\\[\\s\\S]|\\$(?:(?=\")|\\$+(?!\\$)|[^\"${])|\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\})*\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^$])\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\}/,\n          lookbehind: true,\n          inside: {\n            type: {\n              pattern:\n                /(\\b(?:count|data|local|module|path|self|terraform|var)\\b\\.)[\\w\\*]+/i,\n              lookbehind: true,\n              alias: 'variable'\n            },\n            keyword: /\\b(?:count|data|local|module|path|self|terraform|var)\\b/i,\n            function: /\\w+(?=\\()/,\n            string: {\n              pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n              greedy: true\n            },\n            number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n            punctuation: /[!\\$#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~?:]/\n          }\n        }\n      }\n    },\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    punctuation: /[=\\[\\]{}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/hcl.js\n"));

/***/ })

}]);