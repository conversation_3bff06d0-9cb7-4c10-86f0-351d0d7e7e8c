"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_abap"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/abap.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/abap.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = abap\nabap.displayName = 'abap'\nabap.aliases = []\nfunction abap(Prism) {\n  Prism.languages.abap = {\n    comment: /^\\*.*/m,\n    string: /(`|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    'string-template': {\n      pattern: /([|}])(?:\\\\.|[^\\\\|{\\r\\n])*(?=[|{])/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    /* End Of Line comments should not interfere with strings when the\nquote character occurs within them. We assume a string being highlighted\ninside an EOL comment is more acceptable than the opposite.\n*/\n    'eol-comment': {\n      pattern: /(^|\\s)\".*/m,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    keyword: {\n      pattern:\n        /(\\s|\\.|^)(?:SCIENTIFIC_WITH_LEADING_ZERO|SCALE_PRESERVING_SCIENTIFIC|RMC_COMMUNICATION_FAILURE|END-ENHANCEMENT-SECTION|MULTIPLY-CORRESPONDING|SUBTRACT-CORRESPONDING|VERIFICATION-MESSAGE|DIVIDE-CORRESPONDING|ENHANCEMENT-SECTION|CURRENCY_CONVERSION|RMC_SYSTEM_FAILURE|START-OF-SELECTION|MOVE-CORRESPONDING|RMC_INVALID_STATUS|CUSTOMER-FUNCTION|END-OF-DEFINITION|ENHANCEMENT-POINT|SYSTEM-EXCEPTIONS|ADD-CORRESPONDING|SCALE_PRESERVING|SELECTION-SCREEN|CURSOR-SELECTION|END-OF-SELECTION|LOAD-OF-PROGRAM|SCROLL-BOUNDARY|SELECTION-TABLE|EXCEPTION-TABLE|IMPLEMENTATIONS|PARAMETER-TABLE|RIGHT-JUSTIFIED|UNIT_CONVERSION|AUTHORITY-CHECK|LIST-PROCESSING|SIGN_AS_POSTFIX|COL_BACKGROUND|IMPLEMENTATION|INTERFACE-POOL|TRANSFORMATION|IDENTIFICATION|ENDENHANCEMENT|LINE-SELECTION|INITIALIZATION|LEFT-JUSTIFIED|SELECT-OPTIONS|SELECTION-SETS|COMMUNICATION|CORRESPONDING|DECIMAL_SHIFT|PRINT-CONTROL|VALUE-REQUEST|CHAIN-REQUEST|FUNCTION-POOL|FIELD-SYMBOLS|FUNCTIONALITY|INVERTED-DATE|SELECTION-SET|CLASS-METHODS|OUTPUT-LENGTH|CLASS-CODING|COL_NEGATIVE|ERRORMESSAGE|FIELD-GROUPS|HELP-REQUEST|NO-EXTENSION|NO-TOPOFPAGE|REDEFINITION|DISPLAY-MODE|ENDINTERFACE|EXIT-COMMAND|FIELD-SYMBOL|NO-SCROLLING|SHORTDUMP-ID|ACCESSPOLICY|CLASS-EVENTS|COL_POSITIVE|DECLARATIONS|ENHANCEMENTS|FILTER-TABLE|SWITCHSTATES|SYNTAX-CHECK|TRANSPORTING|ASYNCHRONOUS|SYNTAX-TRACE|TOKENIZATION|USER-COMMAND|WITH-HEADING|ABAP-SOURCE|BREAK-POINT|CHAIN-INPUT|COMPRESSION|FIXED-POINT|NEW-SECTION|NON-UNICODE|OCCURRENCES|RESPONSIBLE|SYSTEM-CALL|TRACE-TABLE|ABBREVIATED|CHAR-TO-HEX|END-OF-FILE|ENDFUNCTION|ENVIRONMENT|ASSOCIATION|COL_HEADING|EDITOR-CALL|END-OF-PAGE|ENGINEERING|IMPLEMENTED|INTENSIFIED|RADIOBUTTON|SYSTEM-EXIT|TOP-OF-PAGE|TRANSACTION|APPLICATION|CONCATENATE|DESTINATION|ENHANCEMENT|IMMEDIATELY|NO-GROUPING|PRECOMPILED|REPLACEMENT|TITLE-LINES|ACTIVATION|BYTE-ORDER|CLASS-POOL|CONNECTION|CONVERSION|DEFINITION|DEPARTMENT|EXPIRATION|INHERITING|MESSAGE-ID|NO-HEADING|PERFORMING|QUEUE-ONLY|RIGHTSPACE|SCIENTIFIC|STATUSINFO|STRUCTURES|SYNCPOINTS|WITH-TITLE|ATTRIBUTES|BOUNDARIES|CLASS-DATA|COL_NORMAL|DD\\/MM\\/YYYY|DESCENDING|INTERFACES|LINE-COUNT|MM\\/DD\\/YYYY|NON-UNIQUE|PRESERVING|SELECTIONS|STATEMENTS|SUBROUTINE|TRUNCATION|TYPE-POOLS|ARITHMETIC|BACKGROUND|ENDPROVIDE|EXCEPTIONS|IDENTIFIER|INDEX-LINE|OBLIGATORY|PARAMETERS|PERCENTAGE|PUSHBUTTON|RESOLUTION|COMPONENTS|DEALLOCATE|DISCONNECT|DUPLICATES|FIRST-LINE|HEAD-LINES|NO-DISPLAY|OCCURRENCE|RESPECTING|RETURNCODE|SUBMATCHES|TRACE-FILE|ASCENDING|BYPASSING|ENDMODULE|EXCEPTION|EXCLUDING|EXPORTING|INCREMENT|MATCHCODE|PARAMETER|PARTIALLY|PREFERRED|REFERENCE|REPLACING|RETURNING|SELECTION|SEPARATED|SPECIFIED|STATEMENT|TIMESTAMP|TYPE-POOL|ACCEPTING|APPENDAGE|ASSIGNING|COL_GROUP|COMPARING|CONSTANTS|DANGEROUS|IMPORTING|INSTANCES|LEFTSPACE|LOG-POINT|QUICKINFO|READ-ONLY|SCROLLING|SQLSCRIPT|STEP-LOOP|TOP-LINES|TRANSLATE|APPENDING|AUTHORITY|CHARACTER|COMPONENT|CONDITION|DIRECTORY|DUPLICATE|MESSAGING|RECEIVING|SUBSCREEN|ACCORDING|COL_TOTAL|END-LINES|ENDMETHOD|ENDSELECT|EXPANDING|EXTENSION|INCLUDING|INFOTYPES|INTERFACE|INTERVALS|LINE-SIZE|PF-STATUS|PROCEDURE|PROTECTED|REQUESTED|RESUMABLE|RIGHTPLUS|SAP-SPOOL|SECONDARY|STRUCTURE|SUBSTRING|TABLEVIEW|NUMOFCHAR|ADJACENT|ANALYSIS|ASSIGNED|BACKWARD|CHANNELS|CHECKBOX|CONTINUE|CRITICAL|DATAINFO|DD\\/MM\\/YY|DURATION|ENCODING|ENDCLASS|FUNCTION|LEFTPLUS|LINEFEED|MM\\/DD\\/YY|OVERFLOW|RECEIVED|SKIPPING|SORTABLE|STANDARD|SUBTRACT|SUPPRESS|TABSTRIP|TITLEBAR|TRUNCATE|UNASSIGN|WHENEVER|ANALYZER|COALESCE|COMMENTS|CONDENSE|DECIMALS|DEFERRED|ENDWHILE|EXPLICIT|KEYWORDS|MESSAGES|POSITION|PRIORITY|RECEIVER|RENAMING|TIMEZONE|TRAILING|ALLOCATE|CENTERED|CIRCULAR|CONTROLS|CURRENCY|DELETING|DESCRIBE|DISTANCE|ENDCATCH|EXPONENT|EXTENDED|GENERATE|IGNORING|INCLUDES|INTERNAL|MAJOR-ID|MODIFIER|NEW-LINE|OPTIONAL|PROPERTY|ROLLBACK|STARTING|SUPPLIED|ABSTRACT|CHANGING|CONTEXTS|CREATING|CUSTOMER|DATABASE|DAYLIGHT|DEFINING|DISTINCT|DIVISION|ENABLING|ENDCHAIN|ESCAPING|HARMLESS|IMPLICIT|INACTIVE|LANGUAGE|MINOR-ID|MULTIPLY|NEW-PAGE|NO-TITLE|POS_HIGH|SEPARATE|TEXTPOOL|TRANSFER|SELECTOR|DBMAXLEN|ITERATOR|ARCHIVE|BIT-XOR|BYTE-CO|COLLECT|COMMENT|CURRENT|DEFAULT|DISPLAY|ENDFORM|EXTRACT|LEADING|LISTBOX|LOCATOR|MEMBERS|METHODS|NESTING|POS_LOW|PROCESS|PROVIDE|RAISING|RESERVE|SECONDS|SUMMARY|VISIBLE|BETWEEN|BIT-AND|BYTE-CS|CLEANUP|COMPUTE|CONTROL|CONVERT|DATASET|ENDCASE|FORWARD|HEADERS|HOTSPOT|INCLUDE|INVERSE|KEEPING|NO-ZERO|OBJECTS|OVERLAY|PADDING|PATTERN|PROGRAM|REFRESH|SECTION|SUMMING|TESTING|VERSION|WINDOWS|WITHOUT|BIT-NOT|BYTE-CA|BYTE-NA|CASTING|CONTEXT|COUNTRY|DYNAMIC|ENABLED|ENDLOOP|EXECUTE|FRIENDS|HANDLER|HEADING|INITIAL|\\*-INPUT|LOGFILE|MAXIMUM|MINIMUM|NO-GAPS|NO-SIGN|PRAGMAS|PRIMARY|PRIVATE|REDUCED|REPLACE|REQUEST|RESULTS|UNICODE|WARNING|ALIASES|BYTE-CN|BYTE-NS|CALLING|COL_KEY|COLUMNS|CONNECT|ENDEXEC|ENTRIES|EXCLUDE|FILTERS|FURTHER|HELP-ID|LOGICAL|MAPPING|MESSAGE|NAMETAB|OPTIONS|PACKAGE|PERFORM|RECEIVE|STATICS|VARYING|BINDING|CHARLEN|GREATER|XSTRLEN|ACCEPT|APPEND|DETAIL|ELSEIF|ENDING|ENDTRY|FORMAT|FRAMES|GIVING|HASHED|HEADER|IMPORT|INSERT|MARGIN|MODULE|NATIVE|OBJECT|OFFSET|REMOTE|RESUME|SAVING|SIMPLE|SUBMIT|TABBED|TOKENS|UNIQUE|UNPACK|UPDATE|WINDOW|YELLOW|ACTUAL|ASPECT|CENTER|CURSOR|DELETE|DIALOG|DIVIDE|DURING|ERRORS|EVENTS|EXTEND|FILTER|HANDLE|HAVING|IGNORE|LITTLE|MEMORY|NO-GAP|OCCURS|OPTION|PERSON|PLACES|PUBLIC|REDUCE|REPORT|RESULT|SINGLE|SORTED|SWITCH|SYNTAX|TARGET|VALUES|WRITER|ASSERT|BLOCKS|BOUNDS|BUFFER|CHANGE|COLUMN|COMMIT|CONCAT|COPIES|CREATE|DDMMYY|DEFINE|ENDIAN|ESCAPE|EXPAND|KERNEL|LAYOUT|LEGACY|LEVELS|MMDDYY|NUMBER|OUTPUT|RANGES|READER|RETURN|SCREEN|SEARCH|SELECT|SHARED|SOURCE|STABLE|STATIC|SUBKEY|SUFFIX|TABLES|UNWIND|YYMMDD|ASSIGN|BACKUP|BEFORE|BINARY|BIT-OR|BLANKS|CLIENT|CODING|COMMON|DEMAND|DYNPRO|EXCEPT|EXISTS|EXPORT|FIELDS|GLOBAL|GROUPS|LENGTH|LOCALE|MEDIUM|METHOD|MODIFY|NESTED|OTHERS|REJECT|SCROLL|SUPPLY|SYMBOL|ENDFOR|STRLEN|ALIGN|BEGIN|BOUND|ENDAT|ENTRY|EVENT|FINAL|FLUSH|GRANT|INNER|SHORT|USING|WRITE|AFTER|BLACK|BLOCK|CLOCK|COLOR|COUNT|DUMMY|EMPTY|ENDDO|ENDON|GREEN|INDEX|INOUT|LEAVE|LEVEL|LINES|MODIF|ORDER|OUTER|RANGE|RESET|RETRY|RIGHT|SMART|SPLIT|STYLE|TABLE|THROW|UNDER|UNTIL|UPPER|UTF-8|WHERE|ALIAS|BLANK|CLEAR|CLOSE|EXACT|FETCH|FIRST|FOUND|GROUP|LLANG|LOCAL|OTHER|REGEX|SPOOL|TITLE|TYPES|VALID|WHILE|ALPHA|BOXED|CATCH|CHAIN|CHECK|CLASS|COVER|ENDIF|EQUIV|FIELD|FLOOR|FRAME|INPUT|LOWER|MATCH|NODES|PAGES|PRINT|RAISE|ROUND|SHIFT|SPACE|SPOTS|STAMP|STATE|TASKS|TIMES|TRMAC|ULINE|UNION|VALUE|WIDTH|EQUAL|LOG10|TRUNC|BLOB|CASE|CEIL|CLOB|COND|EXIT|FILE|GAPS|HOLD|INCL|INTO|KEEP|KEYS|LAST|LINE|LONG|LPAD|MAIL|MODE|OPEN|PINK|READ|ROWS|TEST|THEN|ZERO|AREA|BACK|BADI|BYTE|CAST|EDIT|EXEC|FAIL|FIND|FKEQ|FONT|FREE|GKEQ|HIDE|INIT|ITNO|LATE|LOOP|MAIN|MARK|MOVE|NEXT|NULL|RISK|ROLE|UNIT|WAIT|ZONE|BASE|CALL|CODE|DATA|DATE|FKGE|GKGE|HIGH|KIND|LEFT|LIST|MASK|MESH|NAME|NODE|PACK|PAGE|POOL|SEND|SIGN|SIZE|SOME|STOP|TASK|TEXT|TIME|USER|VARY|WITH|WORD|BLUE|CONV|COPY|DEEP|ELSE|FORM|FROM|HINT|ICON|JOIN|LIKE|LOAD|ONLY|PART|SCAN|SKIP|SORT|TYPE|UNIX|VIEW|WHEN|WORK|ACOS|ASIN|ATAN|COSH|EACH|FRAC|LESS|RTTI|SINH|SQRT|TANH|AVG|BIT|DIV|ISO|LET|OUT|PAD|SQL|ALL|CI_|CPI|END|LOB|LPI|MAX|MIN|NEW|OLE|RUN|SET|\\?TO|YES|ABS|ADD|AND|BIG|FOR|HDB|JOB|LOW|NOT|SAP|TRY|VIA|XML|ANY|GET|IDS|KEY|MOD|OFF|PUT|RAW|RED|REF|SUM|TAB|XSD|CNT|COS|EXP|LOG|SIN|TAN|XOR|AT|CO|CP|DO|GT|ID|IF|NS|OR|BT|CA|CS|GE|NA|NB|EQ|IN|LT|NE|NO|OF|ON|PF|TO|AS|BY|CN|IS|LE|NP|UP|E|I|M|O|Z|C|X)\\b/i,\n      lookbehind: true\n    },\n    /* Numbers can be only integers. Decimal or Hex appear only as strings */\n    number: /\\b\\d+\\b/,\n    /* Operators must always be surrounded by whitespace, they cannot be put\nadjacent to operands.\n*/\n    operator: {\n      pattern: /(\\s)(?:\\*\\*?|<[=>]?|>=?|\\?=|[-+\\/=])(?=\\s)/,\n      lookbehind: true\n    },\n    'string-operator': {\n      pattern: /(\\s)&&?(?=\\s)/,\n      lookbehind: true,\n      /* The official editor highlights */\n      alias: 'keyword'\n    },\n    'token-operator': [\n      {\n        /* Special operators used to access structure components, class methods/attributes, etc. */\n        pattern: /(\\w)(?:->?|=>|[~|{}])(?=\\w)/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      {\n        /* Special tokens used do delimit string templates */\n        pattern: /[|{}]/,\n        alias: 'punctuation'\n      }\n    ],\n    punctuation: /[,.:()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/abap.js\n"));

/***/ })

}]);