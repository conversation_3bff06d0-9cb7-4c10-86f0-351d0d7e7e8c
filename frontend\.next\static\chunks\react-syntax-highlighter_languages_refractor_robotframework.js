"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_robotframework"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/robotframework.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/robotframework.js ***!
  \******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = robotframework\nrobotframework.displayName = 'robotframework'\nrobotframework.aliases = []\nfunction robotframework(Prism) {\n  ;(function (Prism) {\n    var comment = {\n      pattern: /(^[ \\t]*| {2}|\\t)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    var variable = {\n      pattern: /((?:^|[^\\\\])(?:\\\\{2})*)[$@&%]\\{(?:[^{}\\r\\n]|\\{[^{}\\r\\n]*\\})*\\}/,\n      lookbehind: true,\n      inside: {\n        punctuation: /^[$@&%]\\{|\\}$/\n      }\n    }\n    function createSection(name, inside) {\n      var extendecInside = {}\n      extendecInside['section-header'] = {\n        pattern: /^ ?\\*{3}.+?\\*{3}/,\n        alias: 'keyword'\n      } // copy inside tokens\n      for (var token in inside) {\n        extendecInside[token] = inside[token]\n      }\n      extendecInside['tag'] = {\n        pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)\\[[-\\w]+\\]/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\[|\\]/\n        }\n      }\n      extendecInside['variable'] = variable\n      extendecInside['comment'] = comment\n      return {\n        pattern: RegExp(\n          /^ ?\\*{3}[ \\t]*<name>[ \\t]*\\*{3}(?:.|[\\r\\n](?!\\*{3}))*/.source.replace(\n            /<name>/g,\n            function () {\n              return name\n            }\n          ),\n          'im'\n        ),\n        alias: 'section',\n        inside: extendecInside\n      }\n    }\n    var docTag = {\n      pattern:\n        /(\\[Documentation\\](?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n      lookbehind: true,\n      alias: 'string'\n    }\n    var testNameLike = {\n      pattern: /([\\r\\n] ?)(?!#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      alias: 'function',\n      inside: {\n        variable: variable\n      }\n    }\n    var testPropertyLike = {\n      pattern: /([\\r\\n](?: {2}|\\t)[ \\t]*)(?!\\[|\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n      lookbehind: true,\n      inside: {\n        variable: variable\n      }\n    }\n    Prism.languages['robotframework'] = {\n      settings: createSection('Settings', {\n        documentation: {\n          pattern:\n            /([\\r\\n] ?Documentation(?: {2}|\\t)[ \\t]*)(?![ \\t]|#)(?:.|(?:\\r\\n?|\\n)[ \\t]*\\.{3})+/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        property: {\n          pattern: /([\\r\\n] ?)(?!\\.{3}|#)(?:\\S(?:[ \\t]\\S)*)+/,\n          lookbehind: true\n        }\n      }),\n      variables: createSection('Variables'),\n      'test-cases': createSection('Test Cases', {\n        'test-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      keywords: createSection('Keywords', {\n        'keyword-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      tasks: createSection('Tasks', {\n        'task-name': testNameLike,\n        documentation: docTag,\n        property: testPropertyLike\n      }),\n      comment: comment\n    }\n    Prism.languages.robot = Prism.languages['robotframework']\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/robotframework.js\n"));

/***/ })

}]);