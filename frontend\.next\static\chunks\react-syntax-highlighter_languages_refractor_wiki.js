"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_wiki"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wiki.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wiki.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = wiki\nwiki.displayName = 'wiki'\nwiki.aliases = []\nfunction wiki(Prism) {\n  Prism.languages.wiki = Prism.languages.extend('markup', {\n    'block-comment': {\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    heading: {\n      pattern: /^(=+)[^=\\r\\n].*?\\1/m,\n      inside: {\n        punctuation: /^=+|=+$/,\n        important: /.+/\n      }\n    },\n    emphasis: {\n      // TODO Multi-line\n      pattern: /('{2,5}).+?\\1/,\n      inside: {\n        'bold-italic': {\n          pattern: /(''''').+?(?=\\1)/,\n          lookbehind: true,\n          alias: ['bold', 'italic']\n        },\n        bold: {\n          pattern: /(''')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        italic: {\n          pattern: /('')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        punctuation: /^''+|''+$/\n      }\n    },\n    hr: {\n      pattern: /^-{4,}/m,\n      alias: 'punctuation'\n    },\n    url: [\n      /ISBN +(?:97[89][ -]?)?(?:\\d[ -]?){9}[\\dx]\\b|(?:PMID|RFC) +\\d+/i,\n      /\\[\\[.+?\\]\\]|\\[.+?\\]/\n    ],\n    variable: [\n      /__[A-Z]+__/, // FIXME Nested structures should be handled\n      // {{formatnum:{{#expr:{{{3}}}}}}}\n      /\\{{3}.+?\\}{3}/,\n      /\\{\\{.+?\\}\\}/\n    ],\n    symbol: [/^#redirect/im, /~{3,5}/],\n    // Handle table attrs:\n    // {|\n    // ! style=\"text-align:left;\"| Item\n    // |}\n    'table-tag': {\n      pattern: /((?:^|[|!])[|!])[^|\\r\\n]+\\|(?!\\|)/m,\n      lookbehind: true,\n      inside: {\n        'table-bar': {\n          pattern: /\\|$/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.markup['tag'].inside\n      }\n    },\n    punctuation: /^(?:\\{\\||\\|\\}|\\|-|[*#:;!|])|\\|\\||!!/m\n  })\n  Prism.languages.insertBefore('wiki', 'tag', {\n    // Prevent highlighting inside <nowiki>, <source> and <pre> tags\n    nowiki: {\n      pattern: /<(nowiki|pre|source)\\b[^>]*>[\\s\\S]*?<\\/\\1>/i,\n      inside: {\n        tag: {\n          pattern: /<(?:nowiki|pre|source)\\b[^>]*>|<\\/(?:nowiki|pre|source)>/i,\n          inside: Prism.languages.markup['tag'].inside\n        }\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL3dpa2kuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsbUJBQW1CLElBQUk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxtQkFBbUIsR0FBRztBQUN0QjtBQUNBLEtBQUs7QUFDTDtBQUNBLHlDQUF5QyxFQUFFO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxZQUFZLFNBQVM7QUFDaEMsVUFBVSxFQUFFLE1BQU0sRUFBRTtBQUNwQixTQUFTLEVBQUUsS0FBSyxFQUFFO0FBQ2xCO0FBQ0EsZ0NBQWdDLElBQUk7QUFDcEM7QUFDQSxRQUFRO0FBQ1IsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTCx3QkFBd0IsT0FBTyxVQUFVO0FBQ3pDLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXHdpa2kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gd2lraVxud2lraS5kaXNwbGF5TmFtZSA9ICd3aWtpJ1xud2lraS5hbGlhc2VzID0gW11cbmZ1bmN0aW9uIHdpa2koUHJpc20pIHtcbiAgUHJpc20ubGFuZ3VhZ2VzLndpa2kgPSBQcmlzbS5sYW5ndWFnZXMuZXh0ZW5kKCdtYXJrdXAnLCB7XG4gICAgJ2Jsb2NrLWNvbW1lbnQnOiB7XG4gICAgICBwYXR0ZXJuOiAvKF58W15cXFxcXSlcXC9cXCpbXFxzXFxTXSo/XFwqXFwvLyxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBhbGlhczogJ2NvbW1lbnQnXG4gICAgfSxcbiAgICBoZWFkaW5nOiB7XG4gICAgICBwYXR0ZXJuOiAvXig9KylbXj1cXHJcXG5dLio/XFwxL20sXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgcHVuY3R1YXRpb246IC9ePSt8PSskLyxcbiAgICAgICAgaW1wb3J0YW50OiAvLisvXG4gICAgICB9XG4gICAgfSxcbiAgICBlbXBoYXNpczoge1xuICAgICAgLy8gVE9ETyBNdWx0aS1saW5lXG4gICAgICBwYXR0ZXJuOiAvKCd7Miw1fSkuKz9cXDEvLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgICdib2xkLWl0YWxpYyc6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvKCcnJycnKS4rPyg/PVxcMSkvLFxuICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgICAgYWxpYXM6IFsnYm9sZCcsICdpdGFsaWMnXVxuICAgICAgICB9LFxuICAgICAgICBib2xkOiB7XG4gICAgICAgICAgcGF0dGVybjogLygnJycpW14nXSg/Oi4qP1teJ10pPyg/PVxcMSkvLFxuICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgICAgfSxcbiAgICAgICAgaXRhbGljOiB7XG4gICAgICAgICAgcGF0dGVybjogLygnJylbXiddKD86Lio/W14nXSk/KD89XFwxKS8sXG4gICAgICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgICAgICB9LFxuICAgICAgICBwdW5jdHVhdGlvbjogL14nJyt8JycrJC9cbiAgICAgIH1cbiAgICB9LFxuICAgIGhyOiB7XG4gICAgICBwYXR0ZXJuOiAvXi17NCx9L20sXG4gICAgICBhbGlhczogJ3B1bmN0dWF0aW9uJ1xuICAgIH0sXG4gICAgdXJsOiBbXG4gICAgICAvSVNCTiArKD86OTdbODldWyAtXT8pPyg/OlxcZFsgLV0/KXs5fVtcXGR4XVxcYnwoPzpQTUlEfFJGQykgK1xcZCsvaSxcbiAgICAgIC9cXFtcXFsuKz9cXF1cXF18XFxbLis/XFxdL1xuICAgIF0sXG4gICAgdmFyaWFibGU6IFtcbiAgICAgIC9fX1tBLVpdK19fLywgLy8gRklYTUUgTmVzdGVkIHN0cnVjdHVyZXMgc2hvdWxkIGJlIGhhbmRsZWRcbiAgICAgIC8vIHt7Zm9ybWF0bnVtOnt7I2V4cHI6e3t7M319fX19fX1cbiAgICAgIC9cXHt7M30uKz9cXH17M30vLFxuICAgICAgL1xce1xcey4rP1xcfVxcfS9cbiAgICBdLFxuICAgIHN5bWJvbDogWy9eI3JlZGlyZWN0L2ltLCAvfnszLDV9L10sXG4gICAgLy8gSGFuZGxlIHRhYmxlIGF0dHJzOlxuICAgIC8vIHt8XG4gICAgLy8gISBzdHlsZT1cInRleHQtYWxpZ246bGVmdDtcInwgSXRlbVxuICAgIC8vIHx9XG4gICAgJ3RhYmxlLXRhZyc6IHtcbiAgICAgIHBhdHRlcm46IC8oKD86XnxbfCFdKVt8IV0pW158XFxyXFxuXStcXHwoPyFcXHwpL20sXG4gICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgICd0YWJsZS1iYXInOiB7XG4gICAgICAgICAgcGF0dGVybjogL1xcfCQvLFxuICAgICAgICAgIGFsaWFzOiAncHVuY3R1YXRpb24nXG4gICAgICAgIH0sXG4gICAgICAgIHJlc3Q6IFByaXNtLmxhbmd1YWdlcy5tYXJrdXBbJ3RhZyddLmluc2lkZVxuICAgICAgfVxuICAgIH0sXG4gICAgcHVuY3R1YXRpb246IC9eKD86XFx7XFx8fFxcfFxcfXxcXHwtfFsqIzo7IXxdKXxcXHxcXHx8ISEvbVxuICB9KVxuICBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCd3aWtpJywgJ3RhZycsIHtcbiAgICAvLyBQcmV2ZW50IGhpZ2hsaWdodGluZyBpbnNpZGUgPG5vd2lraT4sIDxzb3VyY2U+IGFuZCA8cHJlPiB0YWdzXG4gICAgbm93aWtpOiB7XG4gICAgICBwYXR0ZXJuOiAvPChub3dpa2l8cHJlfHNvdXJjZSlcXGJbXj5dKj5bXFxzXFxTXSo/PFxcL1xcMT4vaSxcbiAgICAgIGluc2lkZToge1xuICAgICAgICB0YWc6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvPCg/Om5vd2lraXxwcmV8c291cmNlKVxcYltePl0qPnw8XFwvKD86bm93aWtpfHByZXxzb3VyY2UpPi9pLFxuICAgICAgICAgIGluc2lkZTogUHJpc20ubGFuZ3VhZ2VzLm1hcmt1cFsndGFnJ10uaW5zaWRlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wiki.js\n"));

/***/ })

}]);