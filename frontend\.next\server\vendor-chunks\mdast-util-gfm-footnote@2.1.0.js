"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-footnote@2.1.0";
exports.ids = ["vendor-chunks/mdast-util-gfm-footnote@2.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/mdast-util-gfm-footnote@2.1.0/node_modules/mdast-util-gfm-footnote/lib/index.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mdast-util-gfm-footnote@2.1.0/node_modules/mdast-util-gfm-footnote/lib/index.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnoteFromMarkdown: () => (/* binding */ gfmFootnoteFromMarkdown),\n/* harmony export */   gfmFootnoteToMarkdown: () => (/* binding */ gfmFootnoteToMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@2.0.1/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/**\n * @import {\n *   CompileContext,\n *   Extension as FromMarkdownExtension,\n *   Handle as FromMarkdownHandle\n * } from 'mdast-util-from-markdown'\n * @import {ToMarkdownOptions} from 'mdast-util-gfm-footnote'\n * @import {\n *   Handle as ToMarkdownHandle,\n *   Map,\n *   Options as ToMarkdownExtension\n * } from 'mdast-util-to-markdown'\n * @import {FootnoteDefinition, FootnoteReference} from 'mdast'\n */\n\n\n\n\nfootnoteReference.peek = footnoteReferencePeek\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCallString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCall(token) {\n  this.enter({type: 'footnoteReference', identifier: '', label: ''}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinitionLabelString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinition(token) {\n  this.enter(\n    {type: 'footnoteDefinition', identifier: '', label: '', children: []},\n    token\n  )\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCallString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'footnoteReference')\n  node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCall(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinitionLabelString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'footnoteDefinition')\n  node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinition(token) {\n  this.exit(token)\n}\n\n/** @type {ToMarkdownHandle} */\nfunction footnoteReferencePeek() {\n  return '['\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */\nfunction footnoteReference(node, _, state, info) {\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[^')\n  const exit = state.enter('footnoteReference')\n  const subexit = state.enter('reference')\n  value += tracker.move(\n    state.safe(state.associationId(node), {after: ']', before: value})\n  )\n  subexit()\n  exit()\n  value += tracker.move(']')\n  return value\n}\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nfunction gfmFootnoteFromMarkdown() {\n  return {\n    enter: {\n      gfmFootnoteCallString: enterFootnoteCallString,\n      gfmFootnoteCall: enterFootnoteCall,\n      gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: enterFootnoteDefinition\n    },\n    exit: {\n      gfmFootnoteCallString: exitFootnoteCallString,\n      gfmFootnoteCall: exitFootnoteCall,\n      gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: exitFootnoteDefinition\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nfunction gfmFootnoteToMarkdown(options) {\n  // To do: next major: change default.\n  let firstLineBlank = false\n\n  if (options && options.firstLineBlank) {\n    firstLineBlank = true\n  }\n\n  return {\n    handlers: {footnoteDefinition, footnoteReference},\n    // This is on by default already.\n    unsafe: [{character: '[', inConstruct: ['label', 'phrasing', 'reference']}]\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {FootnoteDefinition} node\n   */\n  function footnoteDefinition(node, _, state, info) {\n    const tracker = state.createTracker(info)\n    let value = tracker.move('[^')\n    const exit = state.enter('footnoteDefinition')\n    const subexit = state.enter('label')\n    value += tracker.move(\n      state.safe(state.associationId(node), {before: value, after: ']'})\n    )\n    subexit()\n\n    value += tracker.move(']:')\n\n    if (node.children && node.children.length > 0) {\n      tracker.shift(4)\n\n      value += tracker.move(\n        (firstLineBlank ? '\\n' : ' ') +\n          state.indentLines(\n            state.containerFlow(node, tracker.current()),\n            firstLineBlank ? mapAll : mapExceptFirst\n          )\n      )\n    }\n\n    exit()\n\n    return value\n  }\n}\n\n/** @type {Map} */\nfunction mapExceptFirst(line, index, blank) {\n  return index === 0 ? line : mapAll(line, index, blank)\n}\n\n/** @type {Map} */\nfunction mapAll(line, index, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdast-util-gfm-footnote@2.1.0/node_modules/mdast-util-gfm-footnote/lib/index.js\n");

/***/ })

};
;