"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_wasm"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wasm.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wasm.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = wasm\nwasm.displayName = 'wasm'\nwasm.aliases = []\nfunction wasm(Prism) {\n  Prism.languages.wasm = {\n    comment: [\n      /\\(;[\\s\\S]*?;\\)/,\n      {\n        pattern: /;;.*/,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      greedy: true\n    },\n    keyword: [\n      {\n        pattern: /\\b(?:align|offset)=/,\n        inside: {\n          operator: /=/\n        }\n      },\n      {\n        pattern:\n          /\\b(?:(?:f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))?|memory\\.(?:grow|size))\\b/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      /\\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\\b/\n    ],\n    variable: /\\$[\\w!#$%&'*+\\-./:<=>?@\\\\^`|~]+/,\n    number:\n      /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/,\n    punctuation: /[()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wasm.js\n"));

/***/ })

}]);