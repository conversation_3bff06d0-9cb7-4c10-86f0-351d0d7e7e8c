"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_t4Templating"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/t4-templating.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/t4-templating.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = t4Templating\nt4Templating.displayName = 't4Templating'\nt4Templating.aliases = []\nfunction t4Templating(Prism) {\n  ;(function (Prism) {\n    function createBlock(prefix, inside, contentAlias) {\n      return {\n        pattern: RegExp('<#' + prefix + '[\\\\s\\\\S]*?#>'),\n        alias: 'block',\n        inside: {\n          delimiter: {\n            pattern: RegExp('^<#' + prefix + '|#>$'),\n            alias: 'important'\n          },\n          content: {\n            pattern: /[\\s\\S]+/,\n            inside: inside,\n            alias: contentAlias\n          }\n        }\n      }\n    }\n    function createT4(insideLang) {\n      var grammar = Prism.languages[insideLang]\n      var className = 'language-' + insideLang\n      return {\n        block: {\n          pattern: /<#[\\s\\S]+?#>/,\n          inside: {\n            directive: createBlock('@', {\n              'attr-value': {\n                pattern: /=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/,\n                inside: {\n                  punctuation: /^=|^[\"']|[\"']$/\n                }\n              },\n              keyword: /\\b\\w+(?=\\s)/,\n              'attr-name': /\\b\\w+/\n            }),\n            expression: createBlock('=', grammar, className),\n            'class-feature': createBlock('\\\\+', grammar, className),\n            standard: createBlock('', grammar, className)\n          }\n        }\n      }\n    }\n    Prism.languages['t4-templating'] = Object.defineProperty({}, 'createT4', {\n      value: createT4\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/t4-templating.js\n"));

/***/ })

}]);