"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_opencl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/c.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/c.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = c\nc.displayName = 'c'\nc.aliases = []\nfunction c(Prism) {\n  Prism.languages.c = Prism.languages.extend('clike', {\n    comment: {\n      pattern:\n        /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    string: {\n      // https://en.cppreference.com/w/c/language/string_literal\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number:\n      /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n    operator: />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    char: {\n      // https://en.cppreference.com/w/c/language/character_constant\n      pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    macro: {\n      // allow for multiline macro definitions\n      // spaces after the # character compile fine with gcc\n      pattern:\n        /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property',\n      inside: {\n        string: [\n          {\n            // highlight the path of the include statement as a string\n            pattern: /^(#\\s*include\\s*)<[^>]+>/,\n            lookbehind: true\n          },\n          Prism.languages.c['string']\n        ],\n        char: Prism.languages.c['char'],\n        comment: Prism.languages.c['comment'],\n        'macro-name': [\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n            lookbehind: true\n          },\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n            lookbehind: true,\n            alias: 'function'\n          }\n        ],\n        // highlight macro directives as keywords\n        directive: {\n          pattern: /^(#\\s*)[a-z]+/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        'directive-hash': /^#/,\n        punctuation: /##|\\\\(?=[\\r\\n])/,\n        expression: {\n          pattern: /\\S[\\s\\S]*/,\n          inside: Prism.languages.c\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('c', 'function', {\n    // highlight predefined macros as constants\n    constant:\n      /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n  })\n  delete Prism.languages.c['boolean']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/c.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/opencl.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/opencl.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorC = __webpack_require__(/*! ./c.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/c.js\")\nmodule.exports = opencl\nopencl.displayName = 'opencl'\nopencl.aliases = []\nfunction opencl(Prism) {\n  Prism.register(refractorC)\n  ;(function (Prism) {\n    /* OpenCL kernel language */\n    Prism.languages.opencl = Prism.languages.extend('c', {\n      // Extracted from the official specs (2.0) and http://streamcomputing.eu/downloads/?opencl.lang (opencl-keywords, opencl-types) and http://sourceforge.net/tracker/?func=detail&aid=2957794&group_id=95717&atid=612384 (Words2, partly Words3)\n      keyword:\n        /\\b(?:(?:__)?(?:constant|global|kernel|local|private|read_only|read_write|write_only)|__attribute__|auto|(?:bool|u?(?:char|int|long|short)|half|quad)(?:2|3|4|8|16)?|break|case|complex|const|continue|(?:double|float)(?:16(?:x(?:1|2|4|8|16))?|1x(?:1|2|4|8|16)|2(?:x(?:1|2|4|8|16))?|3|4(?:x(?:1|2|4|8|16))?|8(?:x(?:1|2|4|8|16))?)?|default|do|else|enum|extern|for|goto|if|imaginary|inline|packed|pipe|register|restrict|return|signed|sizeof|static|struct|switch|typedef|uniform|union|unsigned|void|volatile|while)\\b/,\n      // Extracted from http://streamcomputing.eu/downloads/?opencl.lang (opencl-const)\n      // Math Constants: https://www.khronos.org/registry/OpenCL/sdk/2.1/docs/man/xhtml/mathConstants.html\n      // Macros and Limits: https://www.khronos.org/registry/OpenCL/sdk/2.1/docs/man/xhtml/macroLimits.html\n      number:\n        /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[fuhl]{0,4}/i,\n      boolean: /\\b(?:false|true)\\b/,\n      'constant-opencl-kernel': {\n        pattern:\n          /\\b(?:CHAR_(?:BIT|MAX|MIN)|CLK_(?:ADDRESS_(?:CLAMP(?:_TO_EDGE)?|NONE|REPEAT)|FILTER_(?:LINEAR|NEAREST)|(?:GLOBAL|LOCAL)_MEM_FENCE|NORMALIZED_COORDS_(?:FALSE|TRUE))|CL_(?:BGRA|(?:HALF_)?FLOAT|INTENSITY|LUMINANCE|A?R?G?B?[Ax]?|(?:(?:UN)?SIGNED|[US]NORM)_(?:INT(?:8|16|32))|UNORM_(?:INT_101010|SHORT_(?:555|565)))|(?:DBL|FLT|HALF)_(?:DIG|EPSILON|(?:MAX|MIN)(?:(?:_10)?_EXP)?|MANT_DIG)|FLT_RADIX|HUGE_VALF?|(?:INT|LONG|SCHAR|SHRT)_(?:MAX|MIN)|INFINITY|MAXFLOAT|M_(?:[12]_PI|2_SQRTPI|E|LN(?:2|10)|LOG(?:2|10)E?|PI(?:_[24])?|SQRT(?:1_2|2))(?:_F|_H)?|NAN|(?:UCHAR|UINT|ULONG|USHRT)_MAX)\\b/,\n        alias: 'constant'\n      }\n    })\n    Prism.languages.insertBefore('opencl', 'class-name', {\n      // https://www.khronos.org/registry/OpenCL/sdk/2.1/docs/man/xhtml/scalarDataTypes.html\n      // https://www.khronos.org/registry/OpenCL/sdk/2.1/docs/man/xhtml/otherDataTypes.html\n      'builtin-type': {\n        pattern:\n          /\\b(?:_cl_(?:command_queue|context|device_id|event|kernel|mem|platform_id|program|sampler)|cl_(?:image_format|mem_fence_flags)|clk_event_t|event_t|image(?:1d_(?:array_|buffer_)?t|2d_(?:array_(?:depth_|msaa_depth_|msaa_)?|depth_|msaa_depth_|msaa_)?t|3d_t)|intptr_t|ndrange_t|ptrdiff_t|queue_t|reserve_id_t|sampler_t|size_t|uintptr_t)\\b/,\n        alias: 'keyword'\n      }\n    })\n    var attributes = {\n      // Extracted from http://streamcomputing.eu/downloads/?opencl_host.lang (opencl-types and opencl-host)\n      'type-opencl-host': {\n        pattern:\n          /\\b(?:cl_(?:GLenum|GLint|GLuin|addressing_mode|bitfield|bool|buffer_create_type|build_status|channel_(?:order|type)|(?:u?(?:char|int|long|short)|double|float)(?:2|3|4|8|16)?|command_(?:queue(?:_info|_properties)?|type)|context(?:_info|_properties)?|device_(?:exec_capabilities|fp_config|id|info|local_mem_type|mem_cache_type|type)|(?:event|sampler)(?:_info)?|filter_mode|half|image_info|kernel(?:_info|_work_group_info)?|map_flags|mem(?:_flags|_info|_object_type)?|platform_(?:id|info)|profiling_info|program(?:_build_info|_info)?))\\b/,\n        alias: 'keyword'\n      },\n      'boolean-opencl-host': {\n        pattern: /\\bCL_(?:FALSE|TRUE)\\b/,\n        alias: 'boolean'\n      },\n      // Extracted from cl.h (2.0) and http://streamcomputing.eu/downloads/?opencl_host.lang (opencl-const)\n      'constant-opencl-host': {\n        pattern:\n          /\\bCL_(?:A|ABGR|ADDRESS_(?:CLAMP(?:_TO_EDGE)?|MIRRORED_REPEAT|NONE|REPEAT)|ARGB|BGRA|BLOCKING|BUFFER_CREATE_TYPE_REGION|BUILD_(?:ERROR|IN_PROGRESS|NONE|PROGRAM_FAILURE|SUCCESS)|COMMAND_(?:ACQUIRE_GL_OBJECTS|BARRIER|COPY_(?:BUFFER(?:_RECT|_TO_IMAGE)?|IMAGE(?:_TO_BUFFER)?)|FILL_(?:BUFFER|IMAGE)|MAP(?:_BUFFER|_IMAGE)|MARKER|MIGRATE(?:_SVM)?_MEM_OBJECTS|NATIVE_KERNEL|NDRANGE_KERNEL|READ_(?:BUFFER(?:_RECT)?|IMAGE)|RELEASE_GL_OBJECTS|SVM_(?:FREE|MAP|MEMCPY|MEMFILL|UNMAP)|TASK|UNMAP_MEM_OBJECT|USER|WRITE_(?:BUFFER(?:_RECT)?|IMAGE))|COMPILER_NOT_AVAILABLE|COMPILE_PROGRAM_FAILURE|COMPLETE|CONTEXT_(?:DEVICES|INTEROP_USER_SYNC|NUM_DEVICES|PLATFORM|PROPERTIES|REFERENCE_COUNT)|DEPTH(?:_STENCIL)?|DEVICE_(?:ADDRESS_BITS|AFFINITY_DOMAIN_(?:L[1-4]_CACHE|NEXT_PARTITIONABLE|NUMA)|AVAILABLE|BUILT_IN_KERNELS|COMPILER_AVAILABLE|DOUBLE_FP_CONFIG|ENDIAN_LITTLE|ERROR_CORRECTION_SUPPORT|EXECUTION_CAPABILITIES|EXTENSIONS|GLOBAL_(?:MEM_(?:CACHELINE_SIZE|CACHE_SIZE|CACHE_TYPE|SIZE)|VARIABLE_PREFERRED_TOTAL_SIZE)|HOST_UNIFIED_MEMORY|IL_VERSION|IMAGE(?:2D_MAX_(?:HEIGHT|WIDTH)|3D_MAX_(?:DEPTH|HEIGHT|WIDTH)|_BASE_ADDRESS_ALIGNMENT|_MAX_ARRAY_SIZE|_MAX_BUFFER_SIZE|_PITCH_ALIGNMENT|_SUPPORT)|LINKER_AVAILABLE|LOCAL_MEM_SIZE|LOCAL_MEM_TYPE|MAX_(?:CLOCK_FREQUENCY|COMPUTE_UNITS|CONSTANT_ARGS|CONSTANT_BUFFER_SIZE|GLOBAL_VARIABLE_SIZE|MEM_ALLOC_SIZE|NUM_SUB_GROUPS|ON_DEVICE_(?:EVENTS|QUEUES)|PARAMETER_SIZE|PIPE_ARGS|READ_IMAGE_ARGS|READ_WRITE_IMAGE_ARGS|SAMPLERS|WORK_GROUP_SIZE|WORK_ITEM_DIMENSIONS|WORK_ITEM_SIZES|WRITE_IMAGE_ARGS)|MEM_BASE_ADDR_ALIGN|MIN_DATA_TYPE_ALIGN_SIZE|NAME|NATIVE_VECTOR_WIDTH_(?:CHAR|DOUBLE|FLOAT|HALF|INT|LONG|SHORT)|NOT_(?:AVAILABLE|FOUND)|OPENCL_C_VERSION|PARENT_DEVICE|PARTITION_(?:AFFINITY_DOMAIN|BY_AFFINITY_DOMAIN|BY_COUNTS|BY_COUNTS_LIST_END|EQUALLY|FAILED|MAX_SUB_DEVICES|PROPERTIES|TYPE)|PIPE_MAX_(?:ACTIVE_RESERVATIONS|PACKET_SIZE)|PLATFORM|PREFERRED_(?:GLOBAL_ATOMIC_ALIGNMENT|INTEROP_USER_SYNC|LOCAL_ATOMIC_ALIGNMENT|PLATFORM_ATOMIC_ALIGNMENT|VECTOR_WIDTH_(?:CHAR|DOUBLE|FLOAT|HALF|INT|LONG|SHORT))|PRINTF_BUFFER_SIZE|PROFILE|PROFILING_TIMER_RESOLUTION|QUEUE_(?:ON_(?:DEVICE_(?:MAX_SIZE|PREFERRED_SIZE|PROPERTIES)|HOST_PROPERTIES)|PROPERTIES)|REFERENCE_COUNT|SINGLE_FP_CONFIG|SUB_GROUP_INDEPENDENT_FORWARD_PROGRESS|SVM_(?:ATOMICS|CAPABILITIES|COARSE_GRAIN_BUFFER|FINE_GRAIN_BUFFER|FINE_GRAIN_SYSTEM)|TYPE(?:_ACCELERATOR|_ALL|_CPU|_CUSTOM|_DEFAULT|_GPU)?|VENDOR(?:_ID)?|VERSION)|DRIVER_VERSION|EVENT_(?:COMMAND_(?:EXECUTION_STATUS|QUEUE|TYPE)|CONTEXT|REFERENCE_COUNT)|EXEC_(?:KERNEL|NATIVE_KERNEL|STATUS_ERROR_FOR_EVENTS_IN_WAIT_LIST)|FILTER_(?:LINEAR|NEAREST)|FLOAT|FP_(?:CORRECTLY_ROUNDED_DIVIDE_SQRT|DENORM|FMA|INF_NAN|ROUND_TO_INF|ROUND_TO_NEAREST|ROUND_TO_ZERO|SOFT_FLOAT)|GLOBAL|HALF_FLOAT|IMAGE_(?:ARRAY_SIZE|BUFFER|DEPTH|ELEMENT_SIZE|FORMAT|FORMAT_MISMATCH|FORMAT_NOT_SUPPORTED|HEIGHT|NUM_MIP_LEVELS|NUM_SAMPLES|ROW_PITCH|SLICE_PITCH|WIDTH)|INTENSITY|INVALID_(?:ARG_INDEX|ARG_SIZE|ARG_VALUE|BINARY|BUFFER_SIZE|BUILD_OPTIONS|COMMAND_QUEUE|COMPILER_OPTIONS|CONTEXT|DEVICE|DEVICE_PARTITION_COUNT|DEVICE_QUEUE|DEVICE_TYPE|EVENT|EVENT_WAIT_LIST|GLOBAL_OFFSET|GLOBAL_WORK_SIZE|GL_OBJECT|HOST_PTR|IMAGE_DESCRIPTOR|IMAGE_FORMAT_DESCRIPTOR|IMAGE_SIZE|KERNEL|KERNEL_ARGS|KERNEL_DEFINITION|KERNEL_NAME|LINKER_OPTIONS|MEM_OBJECT|MIP_LEVEL|OPERATION|PIPE_SIZE|PLATFORM|PROGRAM|PROGRAM_EXECUTABLE|PROPERTY|QUEUE_PROPERTIES|SAMPLER|VALUE|WORK_DIMENSION|WORK_GROUP_SIZE|WORK_ITEM_SIZE)|KERNEL_(?:ARG_(?:ACCESS_(?:NONE|QUALIFIER|READ_ONLY|READ_WRITE|WRITE_ONLY)|ADDRESS_(?:CONSTANT|GLOBAL|LOCAL|PRIVATE|QUALIFIER)|INFO_NOT_AVAILABLE|NAME|TYPE_(?:CONST|NAME|NONE|PIPE|QUALIFIER|RESTRICT|VOLATILE))|ATTRIBUTES|COMPILE_NUM_SUB_GROUPS|COMPILE_WORK_GROUP_SIZE|CONTEXT|EXEC_INFO_SVM_FINE_GRAIN_SYSTEM|EXEC_INFO_SVM_PTRS|FUNCTION_NAME|GLOBAL_WORK_SIZE|LOCAL_MEM_SIZE|LOCAL_SIZE_FOR_SUB_GROUP_COUNT|MAX_NUM_SUB_GROUPS|MAX_SUB_GROUP_SIZE_FOR_NDRANGE|NUM_ARGS|PREFERRED_WORK_GROUP_SIZE_MULTIPLE|PRIVATE_MEM_SIZE|PROGRAM|REFERENCE_COUNT|SUB_GROUP_COUNT_FOR_NDRANGE|WORK_GROUP_SIZE)|LINKER_NOT_AVAILABLE|LINK_PROGRAM_FAILURE|LOCAL|LUMINANCE|MAP_(?:FAILURE|READ|WRITE|WRITE_INVALIDATE_REGION)|MEM_(?:ALLOC_HOST_PTR|ASSOCIATED_MEMOBJECT|CONTEXT|COPY_HOST_PTR|COPY_OVERLAP|FLAGS|HOST_NO_ACCESS|HOST_PTR|HOST_READ_ONLY|HOST_WRITE_ONLY|KERNEL_READ_AND_WRITE|MAP_COUNT|OBJECT_(?:ALLOCATION_FAILURE|BUFFER|IMAGE1D|IMAGE1D_ARRAY|IMAGE1D_BUFFER|IMAGE2D|IMAGE2D_ARRAY|IMAGE3D|PIPE)|OFFSET|READ_ONLY|READ_WRITE|REFERENCE_COUNT|SIZE|SVM_ATOMICS|SVM_FINE_GRAIN_BUFFER|TYPE|USES_SVM_POINTER|USE_HOST_PTR|WRITE_ONLY)|MIGRATE_MEM_OBJECT_(?:CONTENT_UNDEFINED|HOST)|MISALIGNED_SUB_BUFFER_OFFSET|NONE|NON_BLOCKING|OUT_OF_(?:HOST_MEMORY|RESOURCES)|PIPE_(?:MAX_PACKETS|PACKET_SIZE)|PLATFORM_(?:EXTENSIONS|HOST_TIMER_RESOLUTION|NAME|PROFILE|VENDOR|VERSION)|PROFILING_(?:COMMAND_(?:COMPLETE|END|QUEUED|START|SUBMIT)|INFO_NOT_AVAILABLE)|PROGRAM_(?:BINARIES|BINARY_SIZES|BINARY_TYPE(?:_COMPILED_OBJECT|_EXECUTABLE|_LIBRARY|_NONE)?|BUILD_(?:GLOBAL_VARIABLE_TOTAL_SIZE|LOG|OPTIONS|STATUS)|CONTEXT|DEVICES|IL|KERNEL_NAMES|NUM_DEVICES|NUM_KERNELS|REFERENCE_COUNT|SOURCE)|QUEUED|QUEUE_(?:CONTEXT|DEVICE|DEVICE_DEFAULT|ON_DEVICE|ON_DEVICE_DEFAULT|OUT_OF_ORDER_EXEC_MODE_ENABLE|PROFILING_ENABLE|PROPERTIES|REFERENCE_COUNT|SIZE)|R|RA|READ_(?:ONLY|WRITE)_CACHE|RG|RGB|RGBA|RGBx|RGx|RUNNING|Rx|SAMPLER_(?:ADDRESSING_MODE|CONTEXT|FILTER_MODE|LOD_MAX|LOD_MIN|MIP_FILTER_MODE|NORMALIZED_COORDS|REFERENCE_COUNT)|(?:UN)?SIGNED_INT(?:8|16|32)|SNORM_INT(?:8|16)|SUBMITTED|SUCCESS|UNORM_INT(?:8|16|24|_101010|_101010_2)|UNORM_SHORT_(?:555|565)|VERSION_(?:1_0|1_1|1_2|2_0|2_1)|sBGRA|sRGB|sRGBA|sRGBx)\\b/,\n        alias: 'constant'\n      },\n      // Extracted from cl.h (2.0) and http://streamcomputing.eu/downloads/?opencl_host.lang (opencl-host)\n      'function-opencl-host': {\n        pattern:\n          /\\bcl(?:BuildProgram|CloneKernel|CompileProgram|Create(?:Buffer|CommandQueue(?:WithProperties)?|Context|ContextFromType|Image|Image2D|Image3D|Kernel|KernelsInProgram|Pipe|ProgramWith(?:Binary|BuiltInKernels|IL|Source)|Sampler|SamplerWithProperties|SubBuffer|SubDevices|UserEvent)|Enqueue(?:(?:Barrier|Marker)(?:WithWaitList)?|Copy(?:Buffer(?:Rect|ToImage)?|Image(?:ToBuffer)?)|(?:Fill|Map)(?:Buffer|Image)|MigrateMemObjects|NDRangeKernel|NativeKernel|(?:Read|Write)(?:Buffer(?:Rect)?|Image)|SVM(?:Free|Map|MemFill|Memcpy|MigrateMem|Unmap)|Task|UnmapMemObject|WaitForEvents)|Finish|Flush|Get(?:CommandQueueInfo|ContextInfo|Device(?:AndHostTimer|IDs|Info)|Event(?:Profiling)?Info|ExtensionFunctionAddress(?:ForPlatform)?|HostTimer|ImageInfo|Kernel(?:ArgInfo|Info|SubGroupInfo|WorkGroupInfo)|MemObjectInfo|PipeInfo|Platform(?:IDs|Info)|Program(?:Build)?Info|SamplerInfo|SupportedImageFormats)|LinkProgram|(?:Release|Retain)(?:CommandQueue|Context|Device|Event|Kernel|MemObject|Program|Sampler)|SVM(?:Alloc|Free)|Set(?:CommandQueueProperty|DefaultDeviceCommandQueue|EventCallback|Kernel|Kernel(?:Arg(?:SVMPointer)?|ExecInfo)|MemObjectDestructorCallback|UserEventStatus)|Unload(?:Platform)?Compiler|WaitForEvents)\\b/,\n        alias: 'function'\n      }\n    }\n    /* OpenCL host API */\n    Prism.languages.insertBefore('c', 'keyword', attributes) // C++ includes everything from the OpenCL C host API plus the classes defined in cl2.h\n    if (Prism.languages.cpp) {\n      // Extracted from doxygen class list http://github.khronos.org/OpenCL-CLHPP/annotated.html\n      attributes['type-opencl-host-cpp'] = {\n        pattern:\n          /\\b(?:Buffer|BufferGL|BufferRenderGL|CommandQueue|Context|Device|DeviceCommandQueue|EnqueueArgs|Event|Image|Image1D|Image1DArray|Image1DBuffer|Image2D|Image2DArray|Image2DGL|Image3D|Image3DGL|ImageFormat|ImageGL|Kernel|KernelFunctor|LocalSpaceArg|Memory|NDRange|Pipe|Platform|Program|SVMAllocator|SVMTraitAtomic|SVMTraitCoarse|SVMTraitFine|SVMTraitReadOnly|SVMTraitReadWrite|SVMTraitWriteOnly|Sampler|UserEvent)\\b/,\n        alias: 'keyword'\n      }\n      Prism.languages.insertBefore('cpp', 'keyword', attributes)\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/opencl.js\n"));

/***/ })

}]);