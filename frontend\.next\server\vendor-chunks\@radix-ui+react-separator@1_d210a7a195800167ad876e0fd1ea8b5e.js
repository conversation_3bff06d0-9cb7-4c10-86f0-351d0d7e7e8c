"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-separator@1_d210a7a195800167ad876e0fd1ea8b5e";
exports.ids = ["vendor-chunks/@radix-ui+react-separator@1_d210a7a195800167ad876e0fd1ea8b5e"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-separator@1_d210a7a195800167ad876e0fd1ea8b5e/node_modules/@radix-ui/react-separator/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-separator@1_d210a7a195800167ad876e0fd1ea8b5e/node_modules/@radix-ui/react-separator/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/separator.tsx\n\n\n\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-separator@1_d210a7a195800167ad876e0fd1ea8b5e/node_modules/@radix-ui/react-separator/dist/index.mjs\n");

/***/ })

};
;