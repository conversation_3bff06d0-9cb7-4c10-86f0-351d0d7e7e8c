"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_makefile"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/makefile.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/makefile.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = makefile\nmakefile.displayName = 'makefile'\nmakefile.aliases = []\nfunction makefile(Prism) {\n  Prism.languages.makefile = {\n    comment: {\n      pattern: /(^|[^\\\\])#(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n])*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'builtin-target': {\n      pattern: /\\.[A-Z][^:#=\\s]+(?=\\s*:(?!=))/,\n      alias: 'builtin'\n    },\n    target: {\n      pattern: /^(?:[^:=\\s]|[ \\t]+(?![\\s:]))+(?=\\s*:(?!=))/m,\n      alias: 'symbol',\n      inside: {\n        variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|(?=[({]))/\n      }\n    },\n    variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|\\([@*%<^+?][DF]\\)|(?=[({]))/,\n    // Directives\n    keyword:\n      /-include\\b|\\b(?:define|else|endef|endif|export|ifn?def|ifn?eq|include|override|private|sinclude|undefine|unexport|vpath)\\b/,\n    function: {\n      pattern:\n        /(\\()(?:abspath|addsuffix|and|basename|call|dir|error|eval|file|filter(?:-out)?|findstring|firstword|flavor|foreach|guile|if|info|join|lastword|load|notdir|or|origin|patsubst|realpath|shell|sort|strip|subst|suffix|value|warning|wildcard|word(?:list|s)?)(?=[ \\t])/,\n      lookbehind: true\n    },\n    operator: /(?:::|[?:+!])?=|[|@]/,\n    punctuation: /[:;(){}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/makefile.js\n"));

/***/ })

}]);