"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_cshtml"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csharp.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csharp.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = csharp\ncsharp.displayName = 'csharp'\ncsharp.aliases = ['dotnet', 'cs']\nfunction csharp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')'\n      })\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]')\n    } // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n      // keywords which are used to declare a type\n      typeDeclaration: 'class enum interface record struct',\n      // contextual keywords\n      // (\"var\" and \"dynamic\" are missing because they are used like types)\n      contextual:\n        'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n      // all other keywords\n      other:\n        'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n    } // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b'\n    }\n    var typeDeclarationKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration\n    )\n    var keywords = RegExp(\n      keywordsToPattern(\n        keywordKinds.type +\n          ' ' +\n          keywordKinds.typeDeclaration +\n          ' ' +\n          keywordKinds.contextual +\n          ' ' +\n          keywordKinds.other\n      )\n    )\n    var nonTypeKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.contextual +\n        ' ' +\n        keywordKinds.other\n    )\n    var nonContextualKeywords = keywordsToPattern(\n      keywordKinds.type +\n        ' ' +\n        keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.other\n    ) // types\n    var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2) // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n    var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2)\n    var name = /@?\\b[A-Za-z_]\\w*\\b/.source\n    var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic])\n    var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [\n      nonTypeKeywords,\n      genericName\n    ])\n    var array = /\\[\\s*(?:,\\s*)*\\]/.source\n    var typeExpressionWithoutTuple = replace(\n      /<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source,\n      [identifier, array]\n    )\n    var tupleElement = replace(\n      /[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,\n      [generic, nestedRound, array]\n    )\n    var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement])\n    var typeExpression = replace(\n      /(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source,\n      [tuple, identifier, array]\n    )\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    } // strings & characters\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n    var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source // simplified pattern\n    var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source\n    var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source\n    Prism.languages.csharp = Prism.languages.extend('clike', {\n      string: [\n        {\n          pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'class-name': [\n        {\n          // Using static\n          // using static System.Math;\n          pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [\n            identifier\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (type)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [\n            name,\n            typeExpression\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (alias)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Type declarations\n          // class Foo<A, B>\n          // interface Foo<out A, B>\n          pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [\n            typeDeclarationKeywords,\n            genericName\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Single catch exception declaration\n          // catch(Foo)\n          // (things like catch(Foo e) is covered by variable declaration)\n          pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Name of the type parameter of generic constraints\n          // where Foo : class\n          pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Casts and checks via as and is.\n          // as Foo<A>, is Bar<B>\n          // (things like if(a is Foo b) is covered by variable declaration)\n          pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [\n            typeExpressionWithoutTuple\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Variable, field and parameter declaration\n          // (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n          pattern: re(\n            /\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/\n              .source,\n            [typeExpression, nonContextualKeywords, name]\n          ),\n          inside: typeInside\n        }\n      ],\n      keyword: keywords,\n      // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n      number:\n        /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n      operator: />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n      punctuation: /\\?\\.?|::|[{}[\\];(),.:]/\n    })\n    Prism.languages.insertBefore('csharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'punctuation', {\n      'named-parameter': {\n        pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n        lookbehind: true,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      namespace: {\n        // namespace Foo.Bar {}\n        // using Foo.Bar;\n        pattern: re(\n          /(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source,\n          [name]\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      'type-expression': {\n        // default(Foo), typeof(Foo<Bar>), sizeof(int)\n        pattern: re(\n          /(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/\n            .source,\n          [nestedRound]\n        ),\n        lookbehind: true,\n        alias: 'class-name',\n        inside: typeInside\n      },\n      'return-type': {\n        // Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n        // int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n        // int Foo => 0; int Foo { get; set } = 0;\n        pattern: re(\n          /<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source,\n          [typeExpression, identifier]\n        ),\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      'constructor-invocation': {\n        // new List<Foo<Bar[]>> { }\n        pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n        lookbehind: true,\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      /*'explicit-implementation': {\n// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\npattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\ninside: classNameInside,\nalias: 'class-name'\n},*/\n      'generic-method': {\n        // foo<Bar>()\n        pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n        inside: {\n          function: re(/^<<0>>/.source, [name]),\n          generic: {\n            pattern: RegExp(generic),\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      },\n      'type-list': {\n        // The list of types inherited or of generic constraints\n        // class Foo<F> : Bar, IList<FooBar>\n        // where F : Bar, IList<int>\n        pattern: re(\n          /\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/\n            .source,\n          [\n            typeDeclarationKeywords,\n            genericName,\n            name,\n            typeExpression,\n            keywords.source,\n            nestedRound,\n            /\\bnew\\s*\\(\\s*\\)/.source\n          ]\n        ),\n        lookbehind: true,\n        inside: {\n          'record-arguments': {\n            pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [\n              genericName,\n              nestedRound\n            ]),\n            lookbehind: true,\n            greedy: true,\n            inside: Prism.languages.csharp\n          },\n          keyword: keywords,\n          'class-name': {\n            pattern: RegExp(typeExpression),\n            greedy: true,\n            inside: typeInside\n          },\n          punctuation: /[,()]/\n        }\n      },\n      preprocessor: {\n        pattern: /(^[\\t ]*)#.*/m,\n        lookbehind: true,\n        alias: 'property',\n        inside: {\n          // highlight preprocessor directives as keywords\n          directive: {\n            pattern:\n              /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n            lookbehind: true,\n            alias: 'keyword'\n          }\n        }\n      }\n    }) // attributes\n    var regularStringOrCharacter = regularString + '|' + character\n    var regularStringCharacterOrComment = replace(\n      /\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source,\n      [regularStringOrCharacter]\n    )\n    var roundExpression = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    ) // https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n    var attrTarget =\n      /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/\n        .source\n    var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [\n      identifier,\n      roundExpression\n    ])\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      attribute: {\n        // Attributes\n        // [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n        pattern: re(\n          /((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/\n            .source,\n          [attrTarget, attr]\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          target: {\n            pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n            alias: 'keyword'\n          },\n          'attribute-arguments': {\n            pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n            inside: Prism.languages.csharp\n          },\n          'class-name': {\n            pattern: RegExp(identifier),\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          punctuation: /[:,]/\n        }\n      }\n    }) // string interpolation\n    var formatString = /:[^}\\r\\n]+/.source // multi line\n    var mInterpolationRound = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    )\n    var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      mInterpolationRound,\n      formatString\n    ]) // single line\n    var sInterpolationRound = nested(\n      replace(\n        /[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/\n          .source,\n        [regularStringOrCharacter]\n      ),\n      2\n    )\n    var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      sInterpolationRound,\n      formatString\n    ])\n    function createInterpolationInside(interpolation, interpolationRound) {\n      return {\n        interpolation: {\n          pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n          lookbehind: true,\n          inside: {\n            'format-string': {\n              pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [\n                interpolationRound,\n                formatString\n              ]),\n              lookbehind: true,\n              inside: {\n                punctuation: /^:/\n              }\n            },\n            punctuation: /^\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              alias: 'language-csharp',\n              inside: Prism.languages.csharp\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.insertBefore('csharp', 'string', {\n      'interpolation-string': [\n        {\n          pattern: re(\n            /(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source,\n            [mInterpolation]\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n        },\n        {\n          pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [\n            sInterpolation\n          ]),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n        }\n      ],\n      char: {\n        pattern: RegExp(character),\n        greedy: true\n      }\n    })\n    Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csharp.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cshtml.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cshtml.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorCsharp = __webpack_require__(/*! ./csharp.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csharp.js\")\nmodule.exports = cshtml\ncshtml.displayName = 'cshtml'\ncshtml.aliases = ['razor']\nfunction cshtml(Prism) {\n  Prism.register(refractorCsharp)\n  // Docs:\n  // https://docs.microsoft.com/en-us/aspnet/core/razor-pages/?view=aspnetcore-5.0&tabs=visual-studio\n  // https://docs.microsoft.com/en-us/aspnet/core/mvc/views/razor?view=aspnetcore-5.0\n  ;(function (Prism) {\n    var commentLike = /\\/(?![/*])|\\/\\/.*[\\r\\n]|\\/\\*[^*]*(?:\\*(?!\\/)[^*]*)*\\*\\//\n      .source\n    var stringLike =\n      /@(?!\")|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|@\"(?:[^\\\\\"]|\"\"|\\\\[\\s\\S])*\"(?!\")/.source +\n      '|' +\n      /'(?:(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'|(?=[^\\\\](?!')))/.source\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<self>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern\n        .replace(/<self>/g, '[^\\\\s\\\\S]')\n        .replace(/<str>/g, '(?:' + stringLike + ')')\n        .replace(/<comment>/g, '(?:' + commentLike + ')')\n    }\n    var round = nested(/\\((?:[^()'\"@/]|<str>|<comment>|<self>)*\\)/.source, 2)\n    var square = nested(/\\[(?:[^\\[\\]'\"@/]|<str>|<comment>|<self>)*\\]/.source, 2)\n    var curly = nested(/\\{(?:[^{}'\"@/]|<str>|<comment>|<self>)*\\}/.source, 2)\n    var angle = nested(/<(?:[^<>'\"@/]|<str>|<comment>|<self>)*>/.source, 2) // Note about the above bracket patterns:\n    // They all ignore HTML expressions that might be in the C# code. This is a problem because HTML (like strings and\n    // comments) is parsed differently. This is a huge problem because HTML might contain brackets and quotes which\n    // messes up the bracket and string counting implemented by the above patterns.\n    //\n    // This problem is not fixable because 1) HTML expression are highly context sensitive and very difficult to detect\n    // and 2) they require one capturing group at every nested level. See the `tagRegion` pattern to admire the\n    // complexity of an HTML expression.\n    //\n    // To somewhat alleviate the problem a bit, the patterns for characters (e.g. 'a') is very permissive, it also\n    // allows invalid characters to support HTML expressions like this: <p>That's it!</p>.\n    var tagAttrs =\n      /(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?/\n        .source\n    var tagContent = /(?!\\d)[^\\s>\\/=$<%]+/.source + tagAttrs + /\\s*\\/?>/.source\n    var tagRegion =\n      /\\B@?/.source +\n      '(?:' +\n      /<([a-zA-Z][\\w:]*)/.source +\n      tagAttrs +\n      /\\s*>/.source +\n      '(?:' +\n      (/[^<]/.source +\n        '|' + // all tags that are not the start tag\n        // eslint-disable-next-line regexp/strict\n        /<\\/?(?!\\1\\b)/.source +\n        tagContent +\n        '|' + // nested start tag\n        nested(\n          // eslint-disable-next-line regexp/strict\n          /<\\1/.source +\n            tagAttrs +\n            /\\s*>/.source +\n            '(?:' +\n            (/[^<]/.source +\n              '|' + // all tags that are not the start tag\n              // eslint-disable-next-line regexp/strict\n              /<\\/?(?!\\1\\b)/.source +\n              tagContent +\n              '|' +\n              '<self>') +\n            ')*' + // eslint-disable-next-line regexp/strict\n            /<\\/\\1\\s*>/.source,\n          2\n        )) +\n      ')*' + // eslint-disable-next-line regexp/strict\n      /<\\/\\1\\s*>/.source +\n      '|' +\n      /</.source +\n      tagContent +\n      ')' // Now for the actual language definition(s):\n    //\n    // Razor as a language has 2 parts:\n    //  1) CSHTML: A markup-like language that has been extended with inline C# code expressions and blocks.\n    //  2) C#+HTML: A variant of C# that can contain CSHTML tags as expressions.\n    //\n    // In the below code, both CSHTML and C#+HTML will be create as separate language definitions that reference each\n    // other. However, only CSHTML will be exported via `Prism.languages`.\n    Prism.languages.cshtml = Prism.languages.extend('markup', {})\n    var csharpWithHtml = Prism.languages.insertBefore(\n      'csharp',\n      'string',\n      {\n        html: {\n          pattern: RegExp(tagRegion),\n          greedy: true,\n          inside: Prism.languages.cshtml\n        }\n      },\n      {\n        csharp: Prism.languages.extend('csharp', {})\n      }\n    )\n    var cs = {\n      pattern: /\\S[\\s\\S]*/,\n      alias: 'language-csharp',\n      inside: csharpWithHtml\n    }\n    Prism.languages.insertBefore('cshtml', 'prolog', {\n      'razor-comment': {\n        pattern: /@\\*[\\s\\S]*?\\*@/,\n        greedy: true,\n        alias: 'comment'\n      },\n      block: {\n        pattern: RegExp(\n          /(^|[^@])@/.source +\n            '(?:' +\n            [\n              // @{ ... }\n              curly, // @code{ ... }\n              /(?:code|functions)\\s*/.source + curly, // @for (...) { ... }\n              /(?:for|foreach|lock|switch|using|while)\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly, // @do { ... } while (...);\n              /do\\s*/.source +\n                curly +\n                /\\s*while\\s*/.source +\n                round +\n                /(?:\\s*;)?/.source, // @try { ... } catch (...) { ... } finally { ... }\n              /try\\s*/.source +\n                curly +\n                /\\s*catch\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly +\n                /\\s*finally\\s*/.source +\n                curly, // @if (...) {...} else if (...) {...} else {...}\n              /if\\s*/.source +\n                round +\n                /\\s*/.source +\n                curly +\n                '(?:' +\n                /\\s*else/.source +\n                '(?:' +\n                /\\s+if\\s*/.source +\n                round +\n                ')?' +\n                /\\s*/.source +\n                curly +\n                ')*'\n            ].join('|') +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w*/,\n          csharp: cs\n        }\n      },\n      directive: {\n        pattern:\n          /^([ \\t]*)@(?:addTagHelper|attribute|implements|inherits|inject|layout|model|namespace|page|preservewhitespace|removeTagHelper|section|tagHelperPrefix|using)(?=\\s).*/m,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^@\\w+/,\n          csharp: cs\n        }\n      },\n      value: {\n        pattern: RegExp(\n          /(^|[^@])@/.source +\n            /(?:await\\b\\s*)?/.source +\n            '(?:' +\n            /\\w+\\b/.source +\n            '|' +\n            round +\n            ')' +\n            '(?:' +\n            /[?!]?\\.\\w+\\b/.source +\n            '|' +\n            round +\n            '|' +\n            square +\n            '|' +\n            angle +\n            round +\n            ')*'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'variable',\n        inside: {\n          keyword: /^@/,\n          csharp: cs\n        }\n      },\n      'delegate-operator': {\n        pattern: /(^|[^@])@(?=<)/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.razor = Prism.languages.cshtml\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cshtml.js\n"));

/***/ })

}]);