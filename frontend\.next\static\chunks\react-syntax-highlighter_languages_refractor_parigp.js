"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_parigp"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parigp.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parigp.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = parigp\nparigp.displayName = 'parigp'\nparigp.aliases = []\nfunction parigp(Prism) {\n  Prism.languages.parigp = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\\\\\\\.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n      greedy: true\n    },\n    // PARI/GP does not care about white spaces at all\n    // so let's process the keywords to build an appropriate regexp\n    // (e.g. \"b *r *e *a *k\", etc.)\n    keyword: (function () {\n      var keywords = [\n        'breakpoint',\n        'break',\n        'dbg_down',\n        'dbg_err',\n        'dbg_up',\n        'dbg_x',\n        'forcomposite',\n        'fordiv',\n        'forell',\n        'forpart',\n        'forprime',\n        'forstep',\n        'forsubgroup',\n        'forvec',\n        'for',\n        'iferr',\n        'if',\n        'local',\n        'my',\n        'next',\n        'return',\n        'until',\n        'while'\n      ]\n      keywords = keywords\n        .map(function (keyword) {\n          return keyword.split('').join(' *')\n        })\n        .join('|')\n      return RegExp('\\\\b(?:' + keywords + ')\\\\b')\n    })(),\n    function: /\\b\\w(?:[\\w ]*\\w)?(?= *\\()/,\n    number: {\n      // The lookbehind and the negative lookahead prevent from breaking the .. operator\n      pattern:\n        /((?:\\. *\\. *)?)(?:\\b\\d(?: *\\d)*(?: *(?!\\. *\\.)\\.(?: *\\d)*)?|\\. *\\d(?: *\\d)*)(?: *e *(?:[+-] *)?\\d(?: *\\d)*)?/i,\n      lookbehind: true\n    },\n    operator:\n      /\\. *\\.|[*\\/!](?: *=)?|%(?: *=|(?: *#)?(?: *')*)?|\\+(?: *[+=])?|-(?: *[-=>])?|<(?: *>|(?: *<)?(?: *=)?)?|>(?: *>)?(?: *=)?|=(?: *=){0,2}|\\\\(?: *\\/)?(?: *=)?|&(?: *&)?|\\| *\\||['#~^]/,\n    punctuation: /[\\[\\]{}().,:;|]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parigp.js\n"));

/***/ })

}]);