"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_git"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/git.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/git.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = git\ngit.displayName = 'git'\ngit.aliases = []\nfunction git(Prism) {\n  Prism.languages.git = {\n    /*\n     * A simple one line comment like in a git status command\n     * For instance:\n     * $ git status\n     * # On branch infinite-scroll\n     * # Your branch and 'origin/sharedBranches/frontendTeam/infinite-scroll' have diverged,\n     * # and have 1 and 2 different commits each, respectively.\n     * nothing to commit (working directory clean)\n     */\n    comment: /^#.*/m,\n    /*\n     * Regexp to match the changed lines in a git diff output. Check the example below.\n     */\n    deleted: /^[-–].*/m,\n    inserted: /^\\+.*/m,\n    /*\n     * a string (double and simple quote)\n     */\n    string: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    /*\n     * a git command. It starts with a random prompt finishing by a $, then \"git\" then some other parameters\n     * For instance:\n     * $ git add file.txt\n     */\n    command: {\n      pattern: /^.*\\$ git .*$/m,\n      inside: {\n        /*\n         * A git command can contain a parameter starting by a single or a double dash followed by a string\n         * For instance:\n         * $ git diff --cached\n         * $ git log -p\n         */\n        parameter: /\\s--?\\w+/\n      }\n    },\n    /*\n     * Coordinates displayed in a git diff command\n     * For instance:\n     * $ git diff\n     * diff --git file.txt file.txt\n     * index 6214953..1d54a52 100644\n     * --- file.txt\n     * +++ file.txt\n     * @@ -1 +1,2 @@\n     * -Here's my tetx file\n     * +Here's my text file\n     * +And this is the second line\n     */\n    coord: /^@@.*@@$/m,\n    /*\n     * Match a \"commit [SHA1]\" line in a git log output.\n     * For instance:\n     * $ git log\n     * commit a11a14ef7e26f2ca62d4b35eac455ce636d0dc09\n     * Author: lgiraudel\n     * Date:   Mon Feb 17 11:18:34 2014 +0100\n     *\n     *     Add of a new line\n     */\n    'commit-sha1': /^commit \\w{40}$/m\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/git.js\n"));

/***/ })

}]);