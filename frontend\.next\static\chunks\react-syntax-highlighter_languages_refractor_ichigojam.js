"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_ichigojam"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ichigojam.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ichigojam.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = ichigojam\nichigojam.displayName = 'ichigojam'\nichigojam.aliases = []\nfunction ichigojam(Prism) {\n  // according to the offical reference (EN)\n  // https://ichigojam.net/IchigoJam-en.html\n  Prism.languages.ichigojam = {\n    comment: /(?:\\B'|REM)(?:[^\\n\\r]*)/i,\n    string: {\n      pattern: /\"(?:\"\"|[!#$%&'()*,\\/:;<=>?^\\w +\\-.])*\"/,\n      greedy: true\n    },\n    number: /\\B#[0-9A-F]+|\\B`[01]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    keyword:\n      /\\b(?:BEEP|BPS|CASE|CLEAR|CLK|CLO|CLP|CLS|CLT|CLV|CONT|COPY|ELSE|END|FILE|FILES|FOR|GOSUB|GOTO|GSB|IF|INPUT|KBD|LED|LET|LIST|LOAD|LOCATE|LRUN|NEW|NEXT|OUT|PLAY|POKE|PRINT|PWM|REM|RENUM|RESET|RETURN|RIGHT|RTN|RUN|SAVE|SCROLL|SLEEP|SRND|STEP|STOP|SUB|TEMPO|THEN|TO|UART|VIDEO|WAIT)(?:\\$|\\b)/i,\n    function:\n      /\\b(?:ABS|ANA|ASC|BIN|BTN|DEC|END|FREE|HELP|HEX|I2CR|I2CW|IN|INKEY|LEN|LINE|PEEK|RND|SCR|SOUND|STR|TICK|USR|VER|VPEEK|ZER)(?:\\$|\\b)/i,\n    label: /(?:\\B@\\S+)/,\n    operator: /<[=>]?|>=?|\\|\\||&&|[+\\-*\\/=|&^~!]|\\b(?:AND|NOT|OR)\\b/i,\n    punctuation: /[\\[,;:()\\]]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ichigojam.js\n"));

/***/ })

}]);