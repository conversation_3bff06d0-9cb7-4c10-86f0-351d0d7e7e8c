"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_julia"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/julia.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/julia.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = julia\njulia.displayName = 'julia'\njulia.aliases = []\nfunction julia(Prism) {\n  Prism.languages.julia = {\n    comment: {\n      // support one level of nested comments\n      // https://github.com/JuliaLang/julia/pull/6128\n      pattern:\n        /(^|[^\\\\])(?:#=(?:[^#=]|=(?!#)|#(?!=)|#=(?:[^#=]|=(?!#)|#(?!=))*=#)*=#|#.*)/,\n      lookbehind: true\n    },\n    regex: {\n      // https://docs.julialang.org/en/v1/manual/strings/#Regular-Expressions-1\n      pattern: /r\"(?:\\\\.|[^\"\\\\\\r\\n])*\"[imsx]{0,4}/,\n      greedy: true\n    },\n    string: {\n      // https://docs.julialang.org/en/v1/manual/strings/#String-Basics-1\n      // https://docs.julialang.org/en/v1/manual/strings/#non-standard-string-literals-1\n      // https://docs.julialang.org/en/v1/manual/running-external-programs/#Running-External-Programs-1\n      pattern:\n        /\"\"\"[\\s\\S]+?\"\"\"|(?:\\b\\w+)?\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`(?:[^\\\\`\\r\\n]|\\\\.)*`/,\n      greedy: true\n    },\n    char: {\n      // https://docs.julialang.org/en/v1/manual/strings/#man-characters-1\n      pattern: /(^|[^\\w'])'(?:\\\\[^\\r\\n][^'\\r\\n]*|[^\\\\\\r\\n])'/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:abstract|baremodule|begin|bitstype|break|catch|ccall|const|continue|do|else|elseif|end|export|finally|for|function|global|if|immutable|import|importall|in|let|local|macro|module|print|println|quote|return|struct|try|type|typealias|using|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[box])?(?:[\\da-f]+(?:_[\\da-f]+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[efp][+-]?\\d+(?:_\\d+)*)?j?/i,\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/#Operator-Precedence-and-Associativity-1\n    operator:\n      /&&|\\|\\||[-+*^%÷⊻&$\\\\]=?|\\/[\\/=]?|!=?=?|\\|[=>]?|<(?:<=?|[=:|])?|>(?:=|>>?=?)?|==?=?|[~≠≤≥'√∛]/,\n    punctuation: /::?|[{}[\\]();,.?]/,\n    // https://docs.julialang.org/en/v1/base/numbers/#Base.im\n    constant: /\\b(?:(?:Inf|NaN)(?:16|32|64)?|im|pi)\\b|[πℯ]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/julia.js\n"));

/***/ })

}]);