"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_sas"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sas.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sas.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = sas\nsas.displayName = 'sas'\nsas.aliases = []\nfunction sas(Prism) {\n  ;(function (Prism) {\n    var stringPattern = /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))/.source\n    var number = /\\b(?:\\d[\\da-f]*x|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i\n    var numericConstant = {\n      pattern: RegExp(stringPattern + '[bx]'),\n      alias: 'number'\n    }\n    var macroVariable = {\n      pattern: /&[a-z_]\\w*/i\n    }\n    var macroKeyword = {\n      pattern:\n        /((?:^|\\s|=|\\())%(?:ABORT|BY|CMS|COPY|DISPLAY|DO|ELSE|END|EVAL|GLOBAL|GO|GOTO|IF|INC|INCLUDE|INDEX|INPUT|KTRIM|LENGTH|LET|LIST|LOCAL|PUT|QKTRIM|QSCAN|QSUBSTR|QSYSFUNC|QUPCASE|RETURN|RUN|SCAN|SUBSTR|SUPERQ|SYMDEL|SYMEXIST|SYMGLOBL|SYMLOCAL|SYSCALL|SYSEVALF|SYSEXEC|SYSFUNC|SYSGET|SYSRPUT|THEN|TO|TSO|UNQUOTE|UNTIL|UPCASE|WHILE|WINDOW)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var step = {\n      pattern: /(^|\\s)(?:proc\\s+\\w+|data(?!=)|quit|run)\\b/i,\n      alias: 'keyword',\n      lookbehind: true\n    }\n    var comment = [\n      /\\/\\*[\\s\\S]*?\\*\\//,\n      {\n        pattern: /(^[ \\t]*|;\\s*)\\*[^;]*;/m,\n        lookbehind: true\n      }\n    ]\n    var string = {\n      pattern: RegExp(stringPattern),\n      greedy: true\n    }\n    var punctuation = /[$%@.(){}\\[\\];,\\\\]/\n    var func = {\n      pattern: /%?\\b\\w+(?=\\()/,\n      alias: 'keyword'\n    }\n    var args = {\n      function: func,\n      'arg-value': {\n        pattern: /(=\\s*)[A-Z\\.]+/i,\n        lookbehind: true\n      },\n      operator: /=/,\n      'macro-variable': macroVariable,\n      arg: {\n        pattern: /[A-Z]+/i,\n        alias: 'keyword'\n      },\n      number: number,\n      'numeric-constant': numericConstant,\n      punctuation: punctuation,\n      string: string\n    }\n    var format = {\n      pattern: /\\b(?:format|put)\\b=?[\\w'$.]+/i,\n      inside: {\n        keyword: /^(?:format|put)(?==)/i,\n        equals: /=/,\n        format: {\n          pattern: /(?:\\w|\\$\\d)+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    }\n    var altformat = {\n      pattern: /\\b(?:format|put)\\s+[\\w']+(?:\\s+[$.\\w]+)+(?=;)/i,\n      inside: {\n        keyword: /^(?:format|put)/i,\n        format: {\n          pattern: /[\\w$]+\\.\\d?/,\n          alias: 'number'\n        }\n      }\n    }\n    var globalStatements = {\n      pattern:\n        /((?:^|\\s)=?)(?:catname|checkpoint execute_always|dm|endsas|filename|footnote|%include|libname|%list|lock|missing|options|page|resetline|%run|sasfile|skip|sysecho|title\\d?)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var submitStatement = {\n      pattern: /(^|\\s)(?:submit(?:\\s+(?:load|norun|parseonly))?|endsubmit)\\b/i,\n      lookbehind: true,\n      alias: 'keyword'\n    }\n    var actionSets =\n      /aStore|accessControl|aggregation|audio|autotune|bayesianNetClassifier|bioMedImage|boolRule|builtins|cardinality|cdm|clustering|conditionalRandomFields|configuration|copula|countreg|dataDiscovery|dataPreprocess|dataSciencePilot|dataStep|decisionTree|deduplication|deepLearn|deepNeural|deepRnn|ds2|ecm|entityRes|espCluster|explainModel|factmac|fastKnn|fcmpact|fedSql|freqTab|gVarCluster|gam|gleam|graphSemiSupLearn|hiddenMarkovModel|hyperGroup|ica|image|iml|kernalPca|langModel|ldaTopic|loadStreams|mbc|mixed|mlTools|modelPublishing|network|neuralNet|nmf|nonParametricBayes|nonlinear|optNetwork|optimization|panel|pca|percentile|phreg|pls|qkb|qlim|quantreg|recommend|regression|reinforcementLearn|robustPca|ruleMining|sampling|sandwich|sccasl|search(?:Analytics)?|sentimentAnalysis|sequence|session(?:Prop)?|severity|simSystem|simple|smartData|sparkEmbeddedProcess|sparseML|spatialreg|spc|stabilityMonitoring|svDataDescription|svm|table|text(?:Filters|Frequency|Mining|Parse|Rule(?:Develop|Score)|Topic|Util)|timeData|transpose|tsInfo|tsReconcile|uniTimeSeries|varReduce/\n        .source\n    var casActions = {\n      pattern: RegExp(\n        /(^|\\s)(?:action\\s+)?(?:<act>)\\.[a-z]+\\b[^;]+/.source.replace(\n          /<act>/g,\n          function () {\n            return actionSets\n          }\n        ),\n        'i'\n      ),\n      lookbehind: true,\n      inside: {\n        keyword: RegExp(\n          /(?:<act>)\\.[a-z]+\\b/.source.replace(/<act>/g, function () {\n            return actionSets\n          }),\n          'i'\n        ),\n        action: {\n          pattern: /(?:action)/i,\n          alias: 'keyword'\n        },\n        comment: comment,\n        function: func,\n        'arg-value': args['arg-value'],\n        operator: args.operator,\n        argument: args.arg,\n        number: number,\n        'numeric-constant': numericConstant,\n        punctuation: punctuation,\n        string: string\n      }\n    }\n    var keywords = {\n      pattern:\n        /((?:^|\\s)=?)(?:after|analysis|and|array|barchart|barwidth|begingraph|by|call|cas|cbarline|cfill|class(?:lev)?|close|column|computed?|contains|continue|data(?==)|define|delete|describe|document|do\\s+over|do|dol|drop|dul|else|end(?:comp|source)?|entryTitle|eval(?:uate)?|exec(?:ute)?|exit|file(?:name)?|fill(?:attrs)?|flist|fnc|function(?:list)?|global|goto|group(?:by)?|headline|headskip|histogram|if|infile|keep|keylabel|keyword|label|layout|leave|legendlabel|length|libname|loadactionset|merge|midpoints|_?null_|name|noobs|nowd|ods|options|or|otherwise|out(?:put)?|over(?:lay)?|plot|print|put|raise|ranexp|rannor|rbreak|retain|return|select|session|sessref|set|source|statgraph|sum|summarize|table|temp|terminate|then\\s+do|then|title\\d?|to|var|when|where|xaxisopts|y2axisopts|yaxisopts)\\b/i,\n      lookbehind: true\n    }\n    Prism.languages.sas = {\n      datalines: {\n        pattern: /^([ \\t]*)(?:cards|(?:data)?lines);[\\s\\S]+?^[ \\t]*;/im,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          keyword: {\n            pattern: /^(?:cards|(?:data)?lines)/i\n          },\n          punctuation: /;/\n        }\n      },\n      'proc-sql': {\n        pattern:\n          /(^proc\\s+(?:fed)?sql(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          sql: {\n            pattern: RegExp(\n              /^[ \\t]*(?:select|alter\\s+table|(?:create|describe|drop)\\s+(?:index|table(?:\\s+constraints)?|view)|create\\s+unique\\s+index|insert\\s+into|update)(?:<str>|[^;\"'])+;/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            alias: 'language-sql',\n            inside: Prism.languages.sql\n          },\n          'global-statements': globalStatements,\n          'sql-statements': {\n            pattern:\n              /(^|\\s)(?:disconnect\\s+from|begin|commit|exec(?:ute)?|reset|rollback|validate)\\b/i,\n            lookbehind: true,\n            alias: 'keyword'\n          },\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-groovy': {\n        pattern:\n          /(^proc\\s+groovy(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          groovy: {\n            pattern: RegExp(\n              /(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            lookbehind: true,\n            alias: 'language-groovy',\n            inside: Prism.languages.groovy\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-lua': {\n        pattern:\n          /(^proc\\s+lua(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|data|quit|run);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          lua: {\n            pattern: RegExp(\n              /(^[ \\t]*submit(?:\\s+(?:load|norun|parseonly))?)(?:<str>|[^\"'])+?(?=endsubmit;)/.source.replace(\n                /<str>/g,\n                function () {\n                  return stringPattern\n                }\n              ),\n              'im'\n            ),\n            lookbehind: true,\n            alias: 'language-lua',\n            inside: Prism.languages.lua\n          },\n          keyword: keywords,\n          'submit-statement': submitStatement,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-cas': {\n        pattern:\n          /(^proc\\s+cas(?:\\s+[\\w|=]+)?;)[\\s\\S]+?(?=^(?:proc\\s+\\w+|quit|data);|(?![\\s\\S]))/im,\n        lookbehind: true,\n        inside: {\n          comment: comment,\n          'statement-var': {\n            pattern: /((?:^|\\s)=?)saveresult\\s[^;]+/im,\n            lookbehind: true,\n            inside: {\n              statement: {\n                pattern: /^saveresult\\s+\\S+/i,\n                inside: {\n                  keyword: /^(?:saveresult)/i\n                }\n              },\n              rest: args\n            }\n          },\n          'cas-actions': casActions,\n          statement: {\n            pattern:\n              /((?:^|\\s)=?)(?:default|(?:un)?set|on|output|upload)[^;]+/im,\n            lookbehind: true,\n            inside: args\n          },\n          step: step,\n          keyword: keywords,\n          function: func,\n          format: format,\n          altformat: altformat,\n          'global-statements': globalStatements,\n          number: number,\n          'numeric-constant': numericConstant,\n          punctuation: punctuation,\n          string: string\n        }\n      },\n      'proc-args': {\n        pattern: RegExp(\n          /(^proc\\s+\\w+\\s+)(?!\\s)(?:[^;\"']|<str>)+;/.source.replace(\n            /<str>/g,\n            function () {\n              return stringPattern\n            }\n          ),\n          'im'\n        ),\n        lookbehind: true,\n        inside: args\n      },\n      /*Special keywords within macros*/\n      'macro-keyword': macroKeyword,\n      'macro-variable': macroVariable,\n      'macro-string-functions': {\n        pattern:\n          /((?:^|\\s|=))%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)\\(.*?(?:[^%]\\))/i,\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /%(?:BQUOTE|NRBQUOTE|NRQUOTE|NRSTR|QUOTE|STR)/i,\n            alias: 'keyword'\n          },\n          'macro-keyword': macroKeyword,\n          'macro-variable': macroVariable,\n          'escaped-char': {\n            pattern: /%['\"()<>=¬^~;,#]/\n          },\n          punctuation: punctuation\n        }\n      },\n      'macro-declaration': {\n        pattern: /^%macro[^;]+(?=;)/im,\n        inside: {\n          keyword: /%macro/i\n        }\n      },\n      'macro-end': {\n        pattern: /^%mend[^;]+(?=;)/im,\n        inside: {\n          keyword: /%mend/i\n        }\n      },\n      /*%_zscore(headcir, _lhc, _mhc, _shc, headcz, headcpct, _Fheadcz); */\n      macro: {\n        pattern: /%_\\w+(?=\\()/,\n        alias: 'keyword'\n      },\n      input: {\n        pattern: /\\binput\\s[-\\w\\s/*.$&]+;/i,\n        inside: {\n          input: {\n            alias: 'keyword',\n            pattern: /^input/i\n          },\n          comment: comment,\n          number: number,\n          'numeric-constant': numericConstant\n        }\n      },\n      'options-args': {\n        pattern: /(^options)[-'\"|/\\\\<>*+=:()\\w\\s]*(?=;)/im,\n        lookbehind: true,\n        inside: args\n      },\n      'cas-actions': casActions,\n      comment: comment,\n      function: func,\n      format: format,\n      altformat: altformat,\n      'numeric-constant': numericConstant,\n      datetime: {\n        // '1jan2013'd, '9:25:19pm't, '18jan2003:9:27:05am'dt\n        pattern: RegExp(stringPattern + '(?:dt?|t)'),\n        alias: 'number'\n      },\n      string: string,\n      step: step,\n      keyword: keywords,\n      // In SAS Studio syntax highlighting, these operators are styled like keywords\n      'operator-keyword': {\n        pattern: /\\b(?:eq|ge|gt|in|le|lt|ne|not)\\b/i,\n        alias: 'operator'\n      },\n      // Decimal (1.2e23), hexadecimal (0c1x)\n      number: number,\n      operator: /\\*\\*?|\\|\\|?|!!?|¦¦?|<[>=]?|>[<=]?|[-+\\/=&]|[~¬^]=?/,\n      punctuation: punctuation\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sas.js\n"));

/***/ })

}]);