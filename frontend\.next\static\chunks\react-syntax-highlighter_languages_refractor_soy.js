"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_soy"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = markupTemplating\nmarkupTemplating.displayName = 'markupTemplating'\nmarkupTemplating.aliases = []\nfunction markupTemplating(Prism) {\n  ;(function (Prism) {\n    /**\n     * Returns the placeholder for the given language id and index.\n     *\n     * @param {string} language\n     * @param {string|number} index\n     * @returns {string}\n     */\n    function getPlaceholder(language, index) {\n      return '___' + language.toUpperCase() + index + '___'\n    }\n    Object.defineProperties((Prism.languages['markup-templating'] = {}), {\n      buildPlaceholders: {\n        /**\n         * Tokenize all inline templating expressions matching `placeholderPattern`.\n         *\n         * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n         * `true` will be replaced.\n         *\n         * @param {object} env The environment of the `before-tokenize` hook.\n         * @param {string} language The language id.\n         * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n         * @param {(match: string) => boolean} [replaceFilter]\n         */\n        value: function (env, language, placeholderPattern, replaceFilter) {\n          if (env.language !== language) {\n            return\n          }\n          var tokenStack = (env.tokenStack = [])\n          env.code = env.code.replace(placeholderPattern, function (match) {\n            if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n              return match\n            }\n            var i = tokenStack.length\n            var placeholder // Check for existing strings\n            while (\n              env.code.indexOf((placeholder = getPlaceholder(language, i))) !==\n              -1\n            ) {\n              ++i\n            } // Create a sparse array\n            tokenStack[i] = match\n            return placeholder\n          }) // Switch the grammar to markup\n          env.grammar = Prism.languages.markup\n        }\n      },\n      tokenizePlaceholders: {\n        /**\n         * Replace placeholders with proper tokens after tokenizing.\n         *\n         * @param {object} env The environment of the `after-tokenize` hook.\n         * @param {string} language The language id.\n         */\n        value: function (env, language) {\n          if (env.language !== language || !env.tokenStack) {\n            return\n          } // Switch the grammar back\n          env.grammar = Prism.languages[language]\n          var j = 0\n          var keys = Object.keys(env.tokenStack)\n          function walkTokens(tokens) {\n            for (var i = 0; i < tokens.length; i++) {\n              // all placeholders are replaced already\n              if (j >= keys.length) {\n                break\n              }\n              var token = tokens[i]\n              if (\n                typeof token === 'string' ||\n                (token.content && typeof token.content === 'string')\n              ) {\n                var k = keys[j]\n                var t = env.tokenStack[k]\n                var s = typeof token === 'string' ? token : token.content\n                var placeholder = getPlaceholder(language, k)\n                var index = s.indexOf(placeholder)\n                if (index > -1) {\n                  ++j\n                  var before = s.substring(0, index)\n                  var middle = new Prism.Token(\n                    language,\n                    Prism.tokenize(t, env.grammar),\n                    'language-' + language,\n                    t\n                  )\n                  var after = s.substring(index + placeholder.length)\n                  var replacement = []\n                  if (before) {\n                    replacement.push.apply(replacement, walkTokens([before]))\n                  }\n                  replacement.push(middle)\n                  if (after) {\n                    replacement.push.apply(replacement, walkTokens([after]))\n                  }\n                  if (typeof token === 'string') {\n                    tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                  } else {\n                    token.content = replacement\n                  }\n                }\n              } else if (\n                token.content\n                /* && typeof token.content !== 'string' */\n              ) {\n                walkTokens(token.content)\n              }\n            }\n            return tokens\n          }\n          walkTokens(env.tokens)\n        }\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/soy.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/soy.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorMarkupTemplating = __webpack_require__(/*! ./markup-templating.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js\")\nmodule.exports = soy\nsoy.displayName = 'soy'\nsoy.aliases = []\nfunction soy(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    var stringPattern = /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/\n    var numberPattern = /\\b\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?\\b|\\b0x[\\dA-F]+\\b/\n    Prism.languages.soy = {\n      comment: [\n        /\\/\\*[\\s\\S]*?\\*\\//,\n        {\n          pattern: /(\\s)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'command-arg': {\n        pattern:\n          /(\\{+\\/?\\s*(?:alias|call|delcall|delpackage|deltemplate|namespace|template)\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      parameter: {\n        pattern: /(\\{+\\/?\\s*@?param\\??\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\{+\\/?[^\\S\\r\\n]*)(?:\\\\[nrt]|alias|call|case|css|default|delcall|delpackage|deltemplate|else(?:if)?|fallbackmsg|for(?:each)?|if(?:empty)?|lb|let|literal|msg|namespace|nil|@?param\\??|rb|sp|switch|template|xid)/,\n          lookbehind: true\n        },\n        /\\b(?:any|as|attributes|bool|css|float|html|in|int|js|list|map|null|number|string|uri)\\b/\n      ],\n      delimiter: {\n        pattern: /^\\{+\\/?|\\/?\\}+$/,\n        alias: 'punctuation'\n      },\n      property: /\\w+(?==)/,\n      variable: {\n        pattern: /\\$[^\\W\\d]\\w*(?:\\??(?:\\.\\w+|\\[[^\\]]+\\]))*/,\n        inside: {\n          string: {\n            pattern: stringPattern,\n            greedy: true\n          },\n          number: numberPattern,\n          punctuation: /[\\[\\].?]/\n        }\n      },\n      string: {\n        pattern: stringPattern,\n        greedy: true\n      },\n      function: [\n        /\\w+(?=\\()/,\n        {\n          pattern: /(\\|[^\\S\\r\\n]*)\\w+/,\n          lookbehind: true\n        }\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: numberPattern,\n      operator: /\\?:?|<=?|>=?|==?|!=|[+*/%-]|\\b(?:and|not|or)\\b/,\n      punctuation: /[{}()\\[\\]|.,:]/\n    } // Tokenize all inline Soy expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var soyPattern = /\\{\\{.+?\\}\\}|\\{.+?\\}|\\s\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//g\n      var soyLitteralStart = '{literal}'\n      var soyLitteralEnd = '{/literal}'\n      var soyLitteralMode = false\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'soy',\n        soyPattern,\n        function (match) {\n          // Soy tags inside {literal} block are ignored\n          if (match === soyLitteralEnd) {\n            soyLitteralMode = false\n          }\n          if (!soyLitteralMode) {\n            if (match === soyLitteralStart) {\n              soyLitteralMode = true\n            }\n            return true\n          }\n          return false\n        }\n      )\n    }) // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'soy')\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/soy.js\n"));

/***/ })

}]);