"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dax"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dax.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dax.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dax\ndax.displayName = 'dax'\ndax.aliases = []\nfunction dax(Prism) {\n  Prism.languages.dax = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/).*)/,\n      lookbehind: true\n    },\n    'data-field': {\n      pattern:\n        /'(?:[^']|'')*'(?!')(?:\\[[ \\w\\xA0-\\uFFFF]+\\])?|\\w+\\[[ \\w\\xA0-\\uFFFF]+\\]/,\n      alias: 'symbol'\n    },\n    measure: {\n      pattern: /\\[[ \\w\\xA0-\\uFFFF]+\\]/,\n      alias: 'constant'\n    },\n    string: {\n      pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    function:\n      /\\b(?:ABS|ACOS|ACOSH|ACOT|ACOTH|ADDCOLUMNS|ADDMISSINGITEMS|ALL|ALLCROSSFILTERED|ALLEXCEPT|ALLNOBLANKROW|ALLSELECTED|AND|APPROXIMATEDISTINCTCOUNT|ASIN|ASINH|ATAN|ATANH|AVERAGE|AVERAGEA|AVERAGEX|BETA\\.DIST|BETA\\.INV|BLANK|CALCULATE|CALCULATETABLE|CALENDAR|CALENDARAUTO|CEILING|CHISQ\\.DIST|CHISQ\\.DIST\\.RT|CHISQ\\.INV|CHISQ\\.INV\\.RT|CLOSINGBALANCEMONTH|CLOSINGBALANCEQUARTER|CLOSINGBALANCEYEAR|COALESCE|COMBIN|COMBINA|COMBINEVALUES|CONCATENATE|CONCATENATEX|CONFIDENCE\\.NORM|CONFIDENCE\\.T|CONTAINS|CONTAINSROW|CONTAINSSTRING|CONTAINSSTRINGEXACT|CONVERT|COS|COSH|COT|COTH|COUNT|COUNTA|COUNTAX|COUNTBLANK|COUNTROWS|COUNTX|CROSSFILTER|CROSSJOIN|CURRENCY|CURRENTGROUP|CUSTOMDATA|DATATABLE|DATE|DATEADD|DATEDIFF|DATESBETWEEN|DATESINPERIOD|DATESMTD|DATESQTD|DATESYTD|DATEVALUE|DAY|DEGREES|DETAILROWS|DISTINCT|DISTINCTCOUNT|DISTINCTCOUNTNOBLANK|DIVIDE|EARLIER|EARLIEST|EDATE|ENDOFMONTH|ENDOFQUARTER|ENDOFYEAR|EOMONTH|ERROR|EVEN|EXACT|EXCEPT|EXP|EXPON\\.DIST|FACT|FALSE|FILTER|FILTERS|FIND|FIRSTDATE|FIRSTNONBLANK|FIRSTNONBLANKVALUE|FIXED|FLOOR|FORMAT|GCD|GENERATE|GENERATEALL|GENERATESERIES|GEOMEAN|GEOMEANX|GROUPBY|HASONEFILTER|HASONEVALUE|HOUR|IF|IF\\.EAGER|IFERROR|IGNORE|INT|INTERSECT|ISBLANK|ISCROSSFILTERED|ISEMPTY|ISERROR|ISEVEN|ISFILTERED|ISINSCOPE|ISLOGICAL|ISNONTEXT|ISNUMBER|ISO\\.CEILING|ISODD|ISONORAFTER|ISSELECTEDMEASURE|ISSUBTOTAL|ISTEXT|KEEPFILTERS|KEYWORDMATCH|LASTDATE|LASTNONBLANK|LASTNONBLANKVALUE|LCM|LEFT|LEN|LN|LOG|LOG10|LOOKUPVALUE|LOWER|MAX|MAXA|MAXX|MEDIAN|MEDIANX|MID|MIN|MINA|MINUTE|MINX|MOD|MONTH|MROUND|NATURALINNERJOIN|NATURALLEFTOUTERJOIN|NEXTDAY|NEXTMONTH|NEXTQUARTER|NEXTYEAR|NONVISUAL|NORM\\.DIST|NORM\\.INV|NORM\\.S\\.DIST|NORM\\.S\\.INV|NOT|NOW|ODD|OPENINGBALANCEMONTH|OPENINGBALANCEQUARTER|OPENINGBALANCEYEAR|OR|PARALLELPERIOD|PATH|PATHCONTAINS|PATHITEM|PATHITEMREVERSE|PATHLENGTH|PERCENTILE\\.EXC|PERCENTILE\\.INC|PERCENTILEX\\.EXC|PERCENTILEX\\.INC|PERMUT|PI|POISSON\\.DIST|POWER|PREVIOUSDAY|PREVIOUSMONTH|PREVIOUSQUARTER|PREVIOUSYEAR|PRODUCT|PRODUCTX|QUARTER|QUOTIENT|RADIANS|RAND|RANDBETWEEN|RANK\\.EQ|RANKX|RELATED|RELATEDTABLE|REMOVEFILTERS|REPLACE|REPT|RIGHT|ROLLUP|ROLLUPADDISSUBTOTAL|ROLLUPGROUP|ROLLUPISSUBTOTAL|ROUND|ROUNDDOWN|ROUNDUP|ROW|SAMEPERIODLASTYEAR|SAMPLE|SEARCH|SECOND|SELECTCOLUMNS|SELECTEDMEASURE|SELECTEDMEASUREFORMATSTRING|SELECTEDMEASURENAME|SELECTEDVALUE|SIGN|SIN|SINH|SQRT|SQRTPI|STARTOFMONTH|STARTOFQUARTER|STARTOFYEAR|STDEV\\.P|STDEV\\.S|STDEVX\\.P|STDEVX\\.S|SUBSTITUTE|SUBSTITUTEWITHINDEX|SUM|SUMMARIZE|SUMMARIZECOLUMNS|SUMX|SWITCH|T\\.DIST|T\\.DIST\\.2T|T\\.DIST\\.RT|T\\.INV|T\\.INV\\.2T|TAN|TANH|TIME|TIMEVALUE|TODAY|TOPN|TOPNPERLEVEL|TOPNSKIP|TOTALMTD|TOTALQTD|TOTALYTD|TREATAS|TRIM|TRUE|TRUNC|UNICHAR|UNICODE|UNION|UPPER|USERELATIONSHIP|USERNAME|USEROBJECTID|USERPRINCIPALNAME|UTCNOW|UTCTODAY|VALUE|VALUES|VAR\\.P|VAR\\.S|VARX\\.P|VARX\\.S|WEEKDAY|WEEKNUM|XIRR|XNPV|YEAR|YEARFRAC)(?=\\s*\\()/i,\n    keyword:\n      /\\b(?:DEFINE|EVALUATE|MEASURE|ORDER\\s+BY|RETURN|VAR|START\\s+AT|ASC|DESC)\\b/i,\n    boolean: {\n      pattern: /\\b(?:FALSE|NULL|TRUE)\\b/i,\n      alias: 'constant'\n    },\n    number: /\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/,\n    operator: /:=|[-+*\\/=^]|&&?|\\|\\||<(?:=>?|<|>)?|>[>=]?|\\b(?:IN|NOT)\\b/i,\n    punctuation: /[;\\[\\](){}`,.]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dax.js\n"));

/***/ })

}]);