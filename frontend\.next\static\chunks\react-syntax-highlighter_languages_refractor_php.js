"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_php"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = markupTemplating\nmarkupTemplating.displayName = 'markupTemplating'\nmarkupTemplating.aliases = []\nfunction markupTemplating(Prism) {\n  ;(function (Prism) {\n    /**\n     * Returns the placeholder for the given language id and index.\n     *\n     * @param {string} language\n     * @param {string|number} index\n     * @returns {string}\n     */\n    function getPlaceholder(language, index) {\n      return '___' + language.toUpperCase() + index + '___'\n    }\n    Object.defineProperties((Prism.languages['markup-templating'] = {}), {\n      buildPlaceholders: {\n        /**\n         * Tokenize all inline templating expressions matching `placeholderPattern`.\n         *\n         * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n         * `true` will be replaced.\n         *\n         * @param {object} env The environment of the `before-tokenize` hook.\n         * @param {string} language The language id.\n         * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n         * @param {(match: string) => boolean} [replaceFilter]\n         */\n        value: function (env, language, placeholderPattern, replaceFilter) {\n          if (env.language !== language) {\n            return\n          }\n          var tokenStack = (env.tokenStack = [])\n          env.code = env.code.replace(placeholderPattern, function (match) {\n            if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n              return match\n            }\n            var i = tokenStack.length\n            var placeholder // Check for existing strings\n            while (\n              env.code.indexOf((placeholder = getPlaceholder(language, i))) !==\n              -1\n            ) {\n              ++i\n            } // Create a sparse array\n            tokenStack[i] = match\n            return placeholder\n          }) // Switch the grammar to markup\n          env.grammar = Prism.languages.markup\n        }\n      },\n      tokenizePlaceholders: {\n        /**\n         * Replace placeholders with proper tokens after tokenizing.\n         *\n         * @param {object} env The environment of the `after-tokenize` hook.\n         * @param {string} language The language id.\n         */\n        value: function (env, language) {\n          if (env.language !== language || !env.tokenStack) {\n            return\n          } // Switch the grammar back\n          env.grammar = Prism.languages[language]\n          var j = 0\n          var keys = Object.keys(env.tokenStack)\n          function walkTokens(tokens) {\n            for (var i = 0; i < tokens.length; i++) {\n              // all placeholders are replaced already\n              if (j >= keys.length) {\n                break\n              }\n              var token = tokens[i]\n              if (\n                typeof token === 'string' ||\n                (token.content && typeof token.content === 'string')\n              ) {\n                var k = keys[j]\n                var t = env.tokenStack[k]\n                var s = typeof token === 'string' ? token : token.content\n                var placeholder = getPlaceholder(language, k)\n                var index = s.indexOf(placeholder)\n                if (index > -1) {\n                  ++j\n                  var before = s.substring(0, index)\n                  var middle = new Prism.Token(\n                    language,\n                    Prism.tokenize(t, env.grammar),\n                    'language-' + language,\n                    t\n                  )\n                  var after = s.substring(index + placeholder.length)\n                  var replacement = []\n                  if (before) {\n                    replacement.push.apply(replacement, walkTokens([before]))\n                  }\n                  replacement.push(middle)\n                  if (after) {\n                    replacement.push.apply(replacement, walkTokens([after]))\n                  }\n                  if (typeof token === 'string') {\n                    tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                  } else {\n                    token.content = replacement\n                  }\n                }\n              } else if (\n                token.content\n                /* && typeof token.content !== 'string' */\n              ) {\n                walkTokens(token.content)\n              }\n            }\n            return tokens\n          }\n          walkTokens(env.tokens)\n        }\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/php.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/php.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorMarkupTemplating = __webpack_require__(/*! ./markup-templating.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup-templating.js\")\nmodule.exports = php\nphp.displayName = 'php'\nphp.aliases = []\nfunction php(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  /**\n   * Original by Aaron Harun: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n   * Modified by Miles Johnson: http://milesj.me\n   * Rewritten by Tom Pavelec\n   *\n   * Supports PHP 5.3 - 8.0\n   */\n  ;(function (Prism) {\n    var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/\n    var constant = [\n      {\n        pattern: /\\b(?:false|true)\\b/i,\n        alias: 'boolean'\n      },\n      {\n        pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n        greedy: true,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n        greedy: true,\n        lookbehind: true\n      },\n      /\\b(?:null)\\b/i,\n      /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/\n    ]\n    var number =\n      /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    var operator =\n      /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/\n    var punctuation = /[{}\\[\\](),:;]/\n    Prism.languages.php = {\n      delimiter: {\n        pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n        alias: 'important'\n      },\n      comment: comment,\n      variable: /\\$+(?:\\w+\\b|(?=\\{))/,\n      package: {\n        pattern:\n          /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      'class-name-definition': {\n        pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'function-definition': {\n        pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n          alias: 'type-casting',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string|void)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n          alias: 'type-declaration',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          // yield from\n          pattern: /(\\byield\\s+)from\\b/i,\n          lookbehind: true\n        }, // `class` is always a keyword unlike other keywords\n        /\\bclass\\b/i,\n        {\n          // https://www.php.net/manual/en/reserved.keywords.php\n          //\n          // keywords cannot be preceded by \"->\"\n          // the complex lookbehind means `(?<!(?:->|::)\\s*)`\n          pattern:\n            /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n          lookbehind: true\n        }\n      ],\n      'argument-name': {\n        pattern: /([(,]\\s+)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-declaration'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n          alias: ['class-name-fully-qualified', 'static-context'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-hint'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: ['class-name-fully-qualified', 'return-type'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        }\n      ],\n      constant: constant,\n      function: {\n        pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      property: {\n        pattern: /(->\\s*)\\w+/,\n        lookbehind: true\n      },\n      number: number,\n      operator: operator,\n      punctuation: punctuation\n    }\n    var string_interpolation = {\n      pattern:\n        /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n      lookbehind: true,\n      inside: Prism.languages.php\n    }\n    var string = [\n      {\n        pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n        alias: 'nowdoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<'?|[';]$/\n            }\n          }\n        }\n      },\n      {\n        pattern:\n          /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n        alias: 'heredoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<\"?|[\";]$/\n            }\n          },\n          interpolation: string_interpolation\n        }\n      },\n      {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        alias: 'backtick-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n        alias: 'single-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n        alias: 'double-quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: string_interpolation\n        }\n      }\n    ]\n    Prism.languages.insertBefore('php', 'variable', {\n      string: string,\n      attribute: {\n        pattern:\n          /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n        greedy: true,\n        inside: {\n          'attribute-content': {\n            pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n            lookbehind: true,\n            // inside can appear subset of php\n            inside: {\n              comment: comment,\n              string: string,\n              'attribute-class-name': [\n                {\n                  pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                  alias: 'class-name',\n                  greedy: true,\n                  lookbehind: true\n                },\n                {\n                  pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                  alias: ['class-name', 'class-name-fully-qualified'],\n                  greedy: true,\n                  lookbehind: true,\n                  inside: {\n                    punctuation: /\\\\/\n                  }\n                }\n              ],\n              constant: constant,\n              number: number,\n              operator: operator,\n              punctuation: punctuation\n            }\n          },\n          delimiter: {\n            pattern: /^#\\[|\\]$/,\n            alias: 'punctuation'\n          }\n        }\n      }\n    })\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (!/<\\?/.test(env.code)) {\n        return\n      }\n      var phpPattern =\n        /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'php',\n        phpPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php')\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/php.js\n"));

/***/ })

}]);