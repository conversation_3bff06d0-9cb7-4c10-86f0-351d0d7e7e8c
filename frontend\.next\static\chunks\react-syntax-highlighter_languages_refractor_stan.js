"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_stan"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stan.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stan.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = stan\nstan.displayName = 'stan'\nstan.aliases = []\nfunction stan(Prism) {\n  ;(function (Prism) {\n    // https://mc-stan.org/docs/2_28/reference-manual/bnf-grammars.html\n    var higherOrderFunctions =\n      /\\b(?:algebra_solver|algebra_solver_newton|integrate_1d|integrate_ode|integrate_ode_bdf|integrate_ode_rk45|map_rect|ode_(?:adams|bdf|ckrk|rk45)(?:_tol)?|ode_adjoint_tol_ctl|reduce_sum|reduce_sum_static)\\b/\n    Prism.languages.stan = {\n      comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|#(?!include).*/,\n      string: {\n        // String literals can contain spaces and any printable ASCII characters except for \" and \\\n        // https://mc-stan.org/docs/2_24/reference-manual/print-statements-section.html#string-literals\n        pattern: /\"[\\x20\\x21\\x23-\\x5B\\x5D-\\x7E]*\"/,\n        greedy: true\n      },\n      directive: {\n        pattern: /^([ \\t]*)#include\\b.*/m,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'function-arg': {\n        pattern: RegExp(\n          '(' +\n            higherOrderFunctions.source +\n            /\\s*\\(\\s*/.source +\n            ')' +\n            /[a-zA-Z]\\w*/.source\n        ),\n        lookbehind: true,\n        alias: 'function'\n      },\n      constraint: {\n        pattern: /(\\b(?:int|matrix|real|row_vector|vector)\\s*)<[^<>]*>/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /(=\\s*)\\S(?:\\S|\\s+(?!\\s))*?(?=\\s*(?:>$|,\\s*\\w+\\s*=))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          property: /\\b[a-z]\\w*(?=\\s*=)/i,\n          operator: /=/,\n          punctuation: /^<|>$|,/\n        }\n      },\n      keyword: [\n        {\n          pattern:\n            /\\bdata(?=\\s*\\{)|\\b(?:functions|generated|model|parameters|quantities|transformed)\\b/,\n          alias: 'program-block'\n        },\n        /\\b(?:array|break|cholesky_factor_corr|cholesky_factor_cov|complex|continue|corr_matrix|cov_matrix|data|else|for|if|in|increment_log_prob|int|matrix|ordered|positive_ordered|print|real|reject|return|row_vector|simplex|target|unit_vector|vector|void|while)\\b/, // these are functions that are known to take another function as their first argument.\n        higherOrderFunctions\n      ],\n      function: /\\b[a-z]\\w*(?=\\s*\\()/i,\n      number:\n        /(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:E[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: /<-|\\.[*/]=?|\\|\\|?|&&|[!=<>+\\-*/]=?|['^%~?:]/,\n      punctuation: /[()\\[\\]{},;]/\n    }\n    Prism.languages.stan.constraint.inside.expression.inside =\n      Prism.languages.stan\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stan.js\n"));

/***/ })

}]);