"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_log"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/log.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/log.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = log\nlog.displayName = 'log'\nlog.aliases = []\nfunction log(Prism) {\n  // This is a language definition for generic log files.\n  // Since there is no one log format, this language definition has to support all formats to some degree.\n  //\n  // Based on https://github.com/MTDL9/vim-log-highlighting\n  Prism.languages.log = {\n    string: {\n      // Single-quoted strings must not be confused with plain text. E.g. Can't isn't Susan's Chris' toy\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?![st] | \\w)(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    exception: {\n      pattern:\n        /(^|[^\\w.])[a-z][\\w.]*(?:Error|Exception):.*(?:(?:\\r\\n?|\\n)[ \\t]*(?:at[ \\t].+|\\.{3}.*|Caused by:.*))+(?:(?:\\r\\n?|\\n)[ \\t]*\\.\\.\\. .*)?/,\n      lookbehind: true,\n      greedy: true,\n      alias: ['javastacktrace', 'language-javastacktrace'],\n      inside: Prism.languages['javastacktrace'] || {\n        keyword: /\\bat\\b/,\n        function: /[a-z_][\\w$]*(?=\\()/,\n        punctuation: /[.:()]/\n      }\n    },\n    level: [\n      {\n        pattern:\n          /\\b(?:ALERT|CRIT|CRITICAL|EMERG|EMERGENCY|ERR|ERROR|FAILURE|FATAL|SEVERE)\\b/,\n        alias: ['error', 'important']\n      },\n      {\n        pattern: /\\b(?:WARN|WARNING|WRN)\\b/,\n        alias: ['warning', 'important']\n      },\n      {\n        pattern: /\\b(?:DISPLAY|INF|INFO|NOTICE|STATUS)\\b/,\n        alias: ['info', 'keyword']\n      },\n      {\n        pattern: /\\b(?:DBG|DEBUG|FINE)\\b/,\n        alias: ['debug', 'keyword']\n      },\n      {\n        pattern: /\\b(?:FINER|FINEST|TRACE|TRC|VERBOSE|VRB)\\b/,\n        alias: ['trace', 'comment']\n      }\n    ],\n    property: {\n      pattern:\n        /((?:^|[\\]|])[ \\t]*)[a-z_](?:[\\w-]|\\b\\/\\b)*(?:[. ]\\(?\\w(?:[\\w-]|\\b\\/\\b)*\\)?)*:(?=\\s)/im,\n      lookbehind: true\n    },\n    separator: {\n      pattern: /(^|[^-+])-{3,}|={3,}|\\*{3,}|- - /m,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    url: /\\b(?:file|ftp|https?):\\/\\/[^\\s|,;'\"]*[^\\s|,;'\">.]/,\n    email: {\n      pattern: /(^|\\s)[-\\w+.]+@[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)+(?=\\s)/,\n      lookbehind: true,\n      alias: 'url'\n    },\n    'ip-address': {\n      pattern: /\\b(?:\\d{1,3}(?:\\.\\d{1,3}){3})\\b/,\n      alias: 'constant'\n    },\n    'mac-address': {\n      pattern: /\\b[a-f0-9]{2}(?::[a-f0-9]{2}){5}\\b/i,\n      alias: 'constant'\n    },\n    domain: {\n      pattern:\n        /(^|\\s)[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)*\\.[a-z][a-z0-9-]+(?=\\s)/,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    uuid: {\n      pattern:\n        /\\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\b/i,\n      alias: 'constant'\n    },\n    hash: {\n      pattern: /\\b(?:[a-f0-9]{32}){1,2}\\b/i,\n      alias: 'constant'\n    },\n    'file-path': {\n      pattern:\n        /\\b[a-z]:[\\\\/][^\\s|,;:(){}\\[\\]\"']+|(^|[\\s:\\[\\](>|])\\.{0,2}\\/\\w[^\\s|,;:(){}\\[\\]\"']*/i,\n      lookbehind: true,\n      greedy: true,\n      alias: 'string'\n    },\n    date: {\n      pattern: RegExp(\n        /\\b\\d{4}[-/]\\d{2}[-/]\\d{2}(?:T(?=\\d{1,2}:)|(?=\\s\\d{1,2}:))/.source +\n          '|' +\n          /\\b\\d{1,4}[-/ ](?:\\d{1,2}|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)[-/ ]\\d{2,4}T?\\b/\n            .source +\n          '|' +\n          /\\b(?:(?:Fri|Mon|Sat|Sun|Thu|Tue|Wed)(?:\\s{1,2}(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep))?|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)\\s{1,2}\\d{1,2}\\b/\n            .source,\n        'i'\n      ),\n      alias: 'number'\n    },\n    time: {\n      pattern:\n        /\\b\\d{1,2}:\\d{1,2}:\\d{1,2}(?:[.,:]\\d+)?(?:\\s?[+-]\\d{2}:?\\d{2}|Z)?\\b/,\n      alias: 'number'\n    },\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: {\n      pattern:\n        /(^|[^.\\w])(?:0x[a-f0-9]+|0o[0-7]+|0b[01]+|v?\\d[\\da-f]*(?:\\.\\d+)*(?:e[+-]?\\d+)?[a-z]{0,3}\\b)\\b(?!\\.\\w)/i,\n      lookbehind: true\n    },\n    operator: /[;:?<=>~/@!$%&+\\-|^(){}*#]/,\n    punctuation: /[\\[\\].,]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/log.js\n"));

/***/ })

}]);