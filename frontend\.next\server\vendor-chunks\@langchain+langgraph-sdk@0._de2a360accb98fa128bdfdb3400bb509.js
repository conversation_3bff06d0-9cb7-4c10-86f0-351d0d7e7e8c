"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509";
exports.ids = ["vendor-chunks/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/client.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/client.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantsClient: () => (/* binding */ AssistantsClient),\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CronsClient: () => (/* binding */ CronsClient),\n/* harmony export */   RunsClient: () => (/* binding */ RunsClient),\n/* harmony export */   StoreClient: () => (/* binding */ StoreClient),\n/* harmony export */   ThreadsClient: () => (/* binding */ ThreadsClient),\n/* harmony export */   getApiKey: () => (/* binding */ getApiKey)\n/* harmony export */ });\n/* harmony import */ var _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/async_caller.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js\");\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/env.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/env.js\");\n/* harmony import */ var _utils_signals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/signals.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js\");\n/* harmony import */ var _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/sse.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js\");\n/* harmony import */ var _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/stream.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js\");\n\n\n\n\n\n/**\n * Get the API key from the environment.\n * Precedence:\n *   1. explicit argument\n *   2. LANGGRAPH_API_KEY\n *   3. LANGSMITH_API_KEY\n *   4. LANGCHAIN_API_KEY\n *\n * @param apiKey - Optional API key provided as an argument\n * @returns The API key if found, otherwise undefined\n */\nfunction getApiKey(apiKey) {\n    if (apiKey) {\n        return apiKey;\n    }\n    const prefixes = [\"LANGGRAPH\", \"LANGSMITH\", \"LANGCHAIN\"];\n    for (const prefix of prefixes) {\n        const envKey = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_1__.getEnvironmentVariable)(`${prefix}_API_KEY`);\n        if (envKey) {\n            // Remove surrounding quotes\n            return envKey.trim().replace(/^[\"']|[\"']$/g, \"\");\n        }\n    }\n    return undefined;\n}\nclass BaseClient {\n    constructor(config) {\n        Object.defineProperty(this, \"asyncCaller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeoutMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"defaultHeaders\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const callerOptions = {\n            maxRetries: 4,\n            maxConcurrency: 4,\n            ...config?.callerOptions,\n        };\n        let defaultApiUrl = \"http://localhost:8123\";\n        if (!config?.apiUrl &&\n            typeof globalThis === \"object\" &&\n            globalThis != null) {\n            const fetchSmb = Symbol.for(\"langgraph_api:fetch\");\n            const urlSmb = Symbol.for(\"langgraph_api:url\");\n            const global = globalThis;\n            if (global[fetchSmb])\n                callerOptions.fetch ??= global[fetchSmb];\n            if (global[urlSmb])\n                defaultApiUrl = global[urlSmb];\n        }\n        this.asyncCaller = new _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__.AsyncCaller(callerOptions);\n        this.timeoutMs = config?.timeoutMs;\n        // default limit being capped by Chrome\n        // https://github.com/nodejs/undici/issues/1373\n        // Regex to remove trailing slash, if present\n        this.apiUrl = config?.apiUrl?.replace(/\\/$/, \"\") || defaultApiUrl;\n        this.defaultHeaders = config?.defaultHeaders || {};\n        const apiKey = getApiKey(config?.apiKey);\n        if (apiKey) {\n            this.defaultHeaders[\"X-Api-Key\"] = apiKey;\n        }\n    }\n    prepareFetchOptions(path, options) {\n        const mutatedOptions = {\n            ...options,\n            headers: { ...this.defaultHeaders, ...options?.headers },\n        };\n        if (mutatedOptions.json) {\n            mutatedOptions.body = JSON.stringify(mutatedOptions.json);\n            mutatedOptions.headers = {\n                ...mutatedOptions.headers,\n                \"Content-Type\": \"application/json\",\n            };\n            delete mutatedOptions.json;\n        }\n        let timeoutSignal = null;\n        if (typeof options?.timeoutMs !== \"undefined\") {\n            if (options.timeoutMs != null) {\n                timeoutSignal = AbortSignal.timeout(options.timeoutMs);\n            }\n        }\n        else if (this.timeoutMs != null) {\n            timeoutSignal = AbortSignal.timeout(this.timeoutMs);\n        }\n        mutatedOptions.signal = (0,_utils_signals_js__WEBPACK_IMPORTED_MODULE_2__.mergeSignals)(timeoutSignal, mutatedOptions.signal);\n        const targetUrl = new URL(`${this.apiUrl}${path}`);\n        if (mutatedOptions.params) {\n            for (const [key, value] of Object.entries(mutatedOptions.params)) {\n                if (value == null)\n                    continue;\n                let strValue = typeof value === \"string\" || typeof value === \"number\"\n                    ? value.toString()\n                    : JSON.stringify(value);\n                targetUrl.searchParams.append(key, strValue);\n            }\n            delete mutatedOptions.params;\n        }\n        return [targetUrl, mutatedOptions];\n    }\n    async fetch(path, options) {\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(path, options));\n        if (response.status === 202 || response.status === 204) {\n            return undefined;\n        }\n        return response.json();\n    }\n}\nclass CronsClient extends BaseClient {\n    /**\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns The created background run.\n     */\n    async createForThread(threadId, assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/threads/${threadId}/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns\n     */\n    async create(assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param cronId Cron ID of Cron job to delete.\n     */\n    async delete(cronId) {\n        await this.fetch(`/runs/crons/${cronId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     *\n     * @param query Query options.\n     * @returns List of crons.\n     */\n    async search(query) {\n        return this.fetch(\"/runs/crons/search\", {\n            method: \"POST\",\n            json: {\n                assistant_id: query?.assistantId ?? undefined,\n                thread_id: query?.threadId ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n            },\n        });\n    }\n}\nclass AssistantsClient extends BaseClient {\n    /**\n     * Get an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant.\n     * @returns Assistant\n     */\n    async get(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`);\n    }\n    /**\n     * Get the JSON representation of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @param options.xray Whether to include subgraphs in the serialized graph representation. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included.\n     * @returns Serialized graph\n     */\n    async getGraph(assistantId, options) {\n        return this.fetch(`/assistants/${assistantId}/graph`, {\n            params: { xray: options?.xray },\n        });\n    }\n    /**\n     * Get the state and config schema of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @returns Graph schema\n     */\n    async getSchemas(assistantId) {\n        return this.fetch(`/assistants/${assistantId}/schemas`);\n    }\n    /**\n     * Get the schemas of an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant to get the schema of.\n     * @param options Additional options for getting subgraphs, such as namespace or recursion extraction.\n     * @returns The subgraphs of the assistant.\n     */\n    async getSubgraphs(assistantId, options) {\n        if (options?.namespace) {\n            return this.fetch(`/assistants/${assistantId}/subgraphs/${options.namespace}`, { params: { recurse: options?.recurse } });\n        }\n        return this.fetch(`/assistants/${assistantId}/subgraphs`, {\n            params: { recurse: options?.recurse },\n        });\n    }\n    /**\n     * Create a new assistant.\n     * @param payload Payload for creating an assistant.\n     * @returns The created assistant.\n     */\n    async create(payload) {\n        return this.fetch(\"/assistants\", {\n            method: \"POST\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                assistant_id: payload.assistantId,\n                if_exists: payload.ifExists,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Update an assistant.\n     * @param assistantId ID of the assistant.\n     * @param payload Payload for updating the assistant.\n     * @returns The updated assistant.\n     */\n    async update(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"PATCH\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Delete an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     */\n    async delete(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List assistants.\n     * @param query Query options.\n     * @returns List of assistants.\n     */\n    async search(query) {\n        return this.fetch(\"/assistants/search\", {\n            method: \"POST\",\n            json: {\n                graph_id: query?.graphId ?? undefined,\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n                sort_by: query?.sortBy ?? undefined,\n                sort_order: query?.sortOrder ?? undefined,\n            },\n        });\n    }\n    /**\n     * List all versions of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @returns List of assistant versions.\n     */\n    async getVersions(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}/versions`, {\n            method: \"POST\",\n            json: {\n                metadata: payload?.metadata ?? undefined,\n                limit: payload?.limit ?? 10,\n                offset: payload?.offset ?? 0,\n            },\n        });\n    }\n    /**\n     * Change the version of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @param version The version to change to.\n     * @returns The updated assistant.\n     */\n    async setLatest(assistantId, version) {\n        return this.fetch(`/assistants/${assistantId}/latest`, {\n            method: \"POST\",\n            json: { version },\n        });\n    }\n}\nclass ThreadsClient extends BaseClient {\n    /**\n     * Get a thread by ID.\n     *\n     * @param threadId ID of the thread.\n     * @returns The thread.\n     */\n    async get(threadId) {\n        return this.fetch(`/threads/${threadId}`);\n    }\n    /**\n     * Create a new thread.\n     *\n     * @param payload Payload for creating a thread.\n     * @returns The created thread.\n     */\n    async create(payload) {\n        return this.fetch(`/threads`, {\n            method: \"POST\",\n            json: {\n                metadata: {\n                    ...payload?.metadata,\n                    graph_id: payload?.graphId,\n                },\n                thread_id: payload?.threadId,\n                if_exists: payload?.ifExists,\n                supersteps: payload?.supersteps?.map((s) => ({\n                    updates: s.updates.map((u) => ({\n                        values: u.values,\n                        command: u.command,\n                        as_node: u.asNode,\n                    })),\n                })),\n            },\n        });\n    }\n    /**\n     * Copy an existing thread\n     * @param threadId ID of the thread to be copied\n     * @returns Newly copied thread\n     */\n    async copy(threadId) {\n        return this.fetch(`/threads/${threadId}/copy`, {\n            method: \"POST\",\n        });\n    }\n    /**\n     * Update a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param payload Payload for updating the thread.\n     * @returns The updated thread.\n     */\n    async update(threadId, payload) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"PATCH\",\n            json: { metadata: payload?.metadata },\n        });\n    }\n    /**\n     * Delete a thread.\n     *\n     * @param threadId ID of the thread.\n     */\n    async delete(threadId) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List threads\n     *\n     * @param query Query options\n     * @returns List of threads\n     */\n    async search(query) {\n        return this.fetch(\"/threads/search\", {\n            method: \"POST\",\n            json: {\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n                status: query?.status,\n                sort_by: query?.sortBy,\n                sort_order: query?.sortOrder,\n            },\n        });\n    }\n    /**\n     * Get state for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @returns Thread state.\n     */\n    async getState(threadId, checkpoint, options) {\n        if (checkpoint != null) {\n            if (typeof checkpoint !== \"string\") {\n                return this.fetch(`/threads/${threadId}/state/checkpoint`, {\n                    method: \"POST\",\n                    json: { checkpoint, subgraphs: options?.subgraphs },\n                });\n            }\n            // deprecated\n            return this.fetch(`/threads/${threadId}/state/${checkpoint}`, { params: { subgraphs: options?.subgraphs } });\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            params: { subgraphs: options?.subgraphs },\n        });\n    }\n    /**\n     * Add state to a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @returns\n     */\n    async updateState(threadId, options) {\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"POST\",\n            json: {\n                values: options.values,\n                checkpoint_id: options.checkpointId,\n                checkpoint: options.checkpoint,\n                as_node: options?.asNode,\n            },\n        });\n    }\n    /**\n     * Patch the metadata of a thread.\n     *\n     * @param threadIdOrConfig Thread ID or config to patch the state of.\n     * @param metadata Metadata to patch the state with.\n     */\n    async patchState(threadIdOrConfig, metadata) {\n        let threadId;\n        if (typeof threadIdOrConfig !== \"string\") {\n            if (typeof threadIdOrConfig.configurable?.thread_id !== \"string\") {\n                throw new Error(\"Thread ID is required when updating state with a config.\");\n            }\n            threadId = threadIdOrConfig.configurable.thread_id;\n        }\n        else {\n            threadId = threadIdOrConfig;\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"PATCH\",\n            json: { metadata: metadata },\n        });\n    }\n    /**\n     * Get all past states for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param options Additional options.\n     * @returns List of thread states.\n     */\n    async getHistory(threadId, options) {\n        return this.fetch(`/threads/${threadId}/history`, {\n            method: \"POST\",\n            json: {\n                limit: options?.limit ?? 10,\n                before: options?.before,\n                metadata: options?.metadata,\n                checkpoint: options?.checkpoint,\n            },\n        });\n    }\n}\nclass RunsClient extends BaseClient {\n    /**\n     * Create a run and stream the results.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     */\n    async *stream(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            feedback_keys: payload?.feedbackKeys,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        const endpoint = threadId == null ? `/runs/stream` : `/threads/${threadId}/runs/stream`;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n        }));\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.BytesLineDecoder())\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.SSEDecoder());\n        yield* _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Create a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The created run.\n     */\n    async create(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            multitask_strategy: payload?.multitaskStrategy,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/threads/${threadId}/runs`, {\n            method: \"POST\",\n            json,\n            signal: payload?.signal,\n        });\n    }\n    /**\n     * Create a batch of stateless background runs.\n     *\n     * @param payloads An array of payloads for creating runs.\n     * @returns An array of created runs.\n     */\n    async createBatch(payloads) {\n        const filteredPayloads = payloads\n            .map((payload) => ({ ...payload, assistant_id: payload.assistantId }))\n            .map((payload) => {\n            return Object.fromEntries(Object.entries(payload).filter(([_, v]) => v !== undefined));\n        });\n        return this.fetch(\"/runs/batch\", {\n            method: \"POST\",\n            json: filteredPayloads,\n        });\n    }\n    /**\n     * Create a run and wait for it to complete.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The last values chunk of the thread.\n     */\n    async wait(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        const endpoint = threadId == null ? `/runs/wait` : `/threads/${threadId}/runs/wait`;\n        const response = await this.fetch(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n        });\n        const raiseError = payload?.raiseError !== undefined ? payload.raiseError : true;\n        if (raiseError &&\n            \"__error__\" in response &&\n            typeof response.__error__ === \"object\" &&\n            response.__error__ &&\n            \"error\" in response.__error__ &&\n            \"message\" in response.__error__) {\n            throw new Error(`${response.__error__?.error}: ${response.__error__?.message}`);\n        }\n        return response;\n    }\n    /**\n     * List all runs for a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @param options Filtering and pagination options.\n     * @returns List of runs.\n     */\n    async list(threadId, options) {\n        return this.fetch(`/threads/${threadId}/runs`, {\n            params: {\n                limit: options?.limit ?? 10,\n                offset: options?.offset ?? 0,\n                status: options?.status ?? undefined,\n            },\n        });\n    }\n    /**\n     * Get a run by ID.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns The run.\n     */\n    async get(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`);\n    }\n    /**\n     * Cancel a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @param wait Whether to block when canceling\n     * @param action Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. Default is `interrupt`.\n     * @returns\n     */\n    async cancel(threadId, runId, wait = false, action = \"interrupt\") {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/cancel`, {\n            method: \"POST\",\n            params: {\n                wait: wait ? \"1\" : \"0\",\n                action: action,\n            },\n        });\n    }\n    /**\n     * Block until a run is done.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async join(threadId, runId, options) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/join`, {\n            timeoutMs: null,\n            signal: options?.signal,\n        });\n    }\n    /**\n     * Stream output from a run in real-time, until the run is done.\n     * Output is not buffered, so any output produced before this call will\n     * not be received here.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @param options Additional options for controlling the stream behavior:\n     *   - signal: An AbortSignal that can be used to cancel the stream request\n     *   - cancelOnDisconnect: When true, automatically cancels the run if the client disconnects from the stream\n     *   - streamMode: Controls what types of events to receive from the stream (can be a single mode or array of modes)\n     *        Must be a subset of the stream modes passed when creating the run. Background runs default to having the union of all\n     *        stream modes enabled.\n     * @returns An async generator yielding stream parts.\n     */\n    async *joinStream(threadId, runId, options) {\n        const opts = typeof options === \"object\" &&\n            options != null &&\n            options instanceof AbortSignal\n            ? { signal: options }\n            : options;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/threads/${threadId}/runs/${runId}/stream`, {\n            method: \"GET\",\n            timeoutMs: null,\n            signal: opts?.signal,\n            params: {\n                cancel_on_disconnect: opts?.cancelOnDisconnect ? \"1\" : \"0\",\n                stream_mode: opts?.streamMode,\n            },\n        }));\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.BytesLineDecoder())\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.SSEDecoder());\n        yield* _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Delete a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async delete(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`, {\n            method: \"DELETE\",\n        });\n    }\n}\nclass StoreClient extends BaseClient {\n    /**\n     * Store or update an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item within the namespace.\n     * @param value A dictionary containing the item's data.\n     * @param options.index Controls search indexing - null (use defaults), false (disable), or list of field paths to index.\n     * @param options.ttl Optional time-to-live in minutes for the item, or null for no expiration.\n     * @returns Promise<void>\n     *\n     * @example\n     * ```typescript\n     * await client.store.putItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { title: \"My Document\", content: \"Hello World\" },\n     *   { ttl: 60 } // expires in 60 minutes\n     * );\n     * ```\n     */\n    async putItem(namespace, key, value, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const payload = {\n            namespace,\n            key,\n            value,\n            index: options?.index,\n            ttl: options?.ttl,\n        };\n        return this.fetch(\"/store/items\", {\n            method: \"PUT\",\n            json: payload,\n        });\n    }\n    /**\n     * Retrieve a single item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @param options.refreshTtl Whether to refresh the TTL on this read operation. If null, uses the store's default behavior.\n     * @returns Promise<Item>\n     *\n     * @example\n     * ```typescript\n     * const item = await client.store.getItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { refreshTtl: true }\n     * );\n     * console.log(item);\n     * // {\n     * //   namespace: [\"documents\", \"user123\"],\n     * //   key: \"item456\",\n     * //   value: { title: \"My Document\", content: \"Hello World\" },\n     * //   createdAt: \"2024-07-30T12:00:00Z\",\n     * //   updatedAt: \"2024-07-30T12:00:00Z\"\n     * // }\n     * ```\n     */\n    async getItem(namespace, key, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const params = {\n            namespace: namespace.join(\".\"),\n            key,\n        };\n        if (options?.refreshTtl !== undefined) {\n            params.refresh_ttl = options.refreshTtl;\n        }\n        const response = await this.fetch(\"/store/items\", {\n            params,\n        });\n        return response\n            ? {\n                ...response,\n                createdAt: response.created_at,\n                updatedAt: response.updated_at,\n            }\n            : null;\n    }\n    /**\n     * Delete an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @returns Promise<void>\n     */\n    async deleteItem(namespace, key) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        return this.fetch(\"/store/items\", {\n            method: \"DELETE\",\n            json: { namespace, key },\n        });\n    }\n    /**\n     * Search for items within a namespace prefix.\n     *\n     * @param namespacePrefix List of strings representing the namespace prefix.\n     * @param options.filter Optional dictionary of key-value pairs to filter results.\n     * @param options.limit Maximum number of items to return (default is 10).\n     * @param options.offset Number of items to skip before returning results (default is 0).\n     * @param options.query Optional search query.\n     * @param options.refreshTtl Whether to refresh the TTL on items returned by this search. If null, uses the store's default behavior.\n     * @returns Promise<SearchItemsResponse>\n     *\n     * @example\n     * ```typescript\n     * const results = await client.store.searchItems(\n     *   [\"documents\"],\n     *   {\n     *     filter: { author: \"John Doe\" },\n     *     limit: 5,\n     *     refreshTtl: true\n     *   }\n     * );\n     * console.log(results);\n     * // {\n     * //   items: [\n     * //     {\n     * //       namespace: [\"documents\", \"user123\"],\n     * //       key: \"item789\",\n     * //       value: { title: \"Another Document\", author: \"John Doe\" },\n     * //       createdAt: \"2024-07-30T12:00:00Z\",\n     * //       updatedAt: \"2024-07-30T12:00:00Z\"\n     * //     },\n     * //     // ... additional items ...\n     * //   ]\n     * // }\n     * ```\n     */\n    async searchItems(namespacePrefix, options) {\n        const payload = {\n            namespace_prefix: namespacePrefix,\n            filter: options?.filter,\n            limit: options?.limit ?? 10,\n            offset: options?.offset ?? 0,\n            query: options?.query,\n            refresh_ttl: options?.refreshTtl,\n        };\n        const response = await this.fetch(\"/store/items/search\", {\n            method: \"POST\",\n            json: payload,\n        });\n        return {\n            items: response.items.map((item) => ({\n                ...item,\n                createdAt: item.created_at,\n                updatedAt: item.updated_at,\n            })),\n        };\n    }\n    /**\n     * List namespaces with optional match conditions.\n     *\n     * @param options.prefix Optional list of strings representing the prefix to filter namespaces.\n     * @param options.suffix Optional list of strings representing the suffix to filter namespaces.\n     * @param options.maxDepth Optional integer specifying the maximum depth of namespaces to return.\n     * @param options.limit Maximum number of namespaces to return (default is 100).\n     * @param options.offset Number of namespaces to skip before returning results (default is 0).\n     * @returns Promise<ListNamespaceResponse>\n     */\n    async listNamespaces(options) {\n        const payload = {\n            prefix: options?.prefix,\n            suffix: options?.suffix,\n            max_depth: options?.maxDepth,\n            limit: options?.limit ?? 100,\n            offset: options?.offset ?? 0,\n        };\n        return this.fetch(\"/store/namespaces\", {\n            method: \"POST\",\n            json: payload,\n        });\n    }\n}\nclass UiClient extends BaseClient {\n    static getOrCached(key, fn) {\n        if (UiClient.promiseCache[key] != null) {\n            return UiClient.promiseCache[key];\n        }\n        const promise = fn();\n        UiClient.promiseCache[key] = promise;\n        return promise;\n    }\n    async getComponent(assistantId, agentName) {\n        return UiClient[\"getOrCached\"](`${this.apiUrl}-${assistantId}-${agentName}`, async () => {\n            const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/ui/${assistantId}`, {\n                headers: {\n                    Accept: \"text/html\",\n                    \"Content-Type\": \"application/json\",\n                },\n                method: \"POST\",\n                json: { name: agentName },\n            }));\n            return response.text();\n        });\n    }\n}\nObject.defineProperty(UiClient, \"promiseCache\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: {}\n});\nclass Client {\n    constructor(config) {\n        /**\n         * The client for interacting with assistants.\n         */\n        Object.defineProperty(this, \"assistants\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with threads.\n         */\n        Object.defineProperty(this, \"threads\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with runs.\n         */\n        Object.defineProperty(this, \"runs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with cron runs.\n         */\n        Object.defineProperty(this, \"crons\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the KV store.\n         */\n        Object.defineProperty(this, \"store\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the UI.\n         * @internal Used by LoadExternalComponent and the API might change in the future.\n         */\n        Object.defineProperty(this, \"~ui\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.assistants = new AssistantsClient(config);\n        this.threads = new ThreadsClient(config);\n        this.runs = new RunsClient(config);\n        this.crons = new CronsClient(config);\n        this.store = new StoreClient(config);\n        this[\"~ui\"] = new UiClient(config);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/index.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/index.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.Client),\n/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_1__.overrideFetchImplementation)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/client.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singletons/fetch.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFDO0FBQytCIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsYW5nY2hhaW4rbGFuZ2dyYXBoLXNka0AwLl9kZTJhMzYwYWNjYjk4ZmExMjhiZGZkYjM0MDBiYjUwOVxcbm9kZV9tb2R1bGVzXFxAbGFuZ2NoYWluXFxsYW5nZ3JhcGgtc2RrXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBDbGllbnQgfSBmcm9tIFwiLi9jbGllbnQuanNcIjtcbmV4cG9ydCB7IG92ZXJyaWRlRmV0Y2hJbXBsZW1lbnRhdGlvbiB9IGZyb20gXCIuL3NpbmdsZXRvbnMvZmV0Y2guanNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/client.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/client.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_2___namespace_cache;\nvar react_dom__WEBPACK_IMPORTED_MODULE_3___namespace_cache;\nvar react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadExternalComponent: () => (/* binding */ LoadExternalComponent),\n/* harmony export */   bootstrapUiContext: () => (/* binding */ bootstrapUiContext),\n/* harmony export */   experimental_loadShare: () => (/* binding */ experimental_loadShare),\n/* harmony export */   useStreamContext: () => (/* binding */ useStreamContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _react_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../react/index.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ useStreamContext,LoadExternalComponent,experimental_loadShare,bootstrapUiContext auto */ \n\n\n\n\nconst UseStreamContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createContext(null);\nfunction useStreamContext() {\n    const ctx = react__WEBPACK_IMPORTED_MODULE_2__.useContext(UseStreamContext);\n    if (!ctx) {\n        throw new Error(\"useStreamContext must be used within a LoadExternalComponent\");\n    }\n    return new Proxy(ctx, {\n        get (target, prop) {\n            if (prop === \"meta\") return target.meta;\n            return target.stream[prop];\n        }\n    });\n}\nclass ComponentStore {\n    constructor(){\n        Object.defineProperty(this, \"cache\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        Object.defineProperty(this, \"boundCache\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        Object.defineProperty(this, \"callbacks\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n    }\n    respond(shadowRootId, comp, targetElement) {\n        this.cache[shadowRootId] = {\n            comp,\n            target: targetElement\n        };\n        this.callbacks[shadowRootId]?.forEach((c)=>c(comp, targetElement));\n    }\n    getBoundStore(shadowRootId) {\n        this.boundCache[shadowRootId] ??= {\n            subscribe: (onStoreChange)=>{\n                this.callbacks[shadowRootId] ??= [];\n                this.callbacks[shadowRootId].push(onStoreChange);\n                return ()=>{\n                    this.callbacks[shadowRootId] = this.callbacks[shadowRootId].filter((c)=>c !== onStoreChange);\n                };\n            },\n            getSnapshot: ()=>this.cache[shadowRootId]\n        };\n        return this.boundCache[shadowRootId];\n    }\n}\nconst COMPONENT_STORE = new ComponentStore();\nconst EXT_STORE_SYMBOL = Symbol.for(\"LGUI_EXT_STORE\");\nconst REQUIRE_SYMBOL = Symbol.for(\"LGUI_REQUIRE\");\nconst REQUIRE_EXTRA_SYMBOL = Symbol.for(\"LGUI_REQUIRE_EXTRA\");\nconst isIterable = (value)=>value != null && typeof value === \"object\" && Symbol.iterator in value;\nconst isPromise = (value)=>value != null && typeof value === \"object\" && \"then\" in value && typeof value.then === \"function\";\nconst isReactNode = (value)=>{\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.isValidElement(value)) return true;\n    if (value == null) return true;\n    if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"bigint\" || typeof value === \"boolean\") {\n        return true;\n    }\n    if (isIterable(value)) return true;\n    if (isPromise(value)) return true;\n    return false;\n};\nfunction LoadExternalComponent({ stream, namespace, message, meta, fallback, components, ...props }) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    const id = react__WEBPACK_IMPORTED_MODULE_2__.useId();\n    const shadowRootId = `child-shadow-${id}`;\n    const store = react__WEBPACK_IMPORTED_MODULE_2__.useMemo({\n        \"LoadExternalComponent.useMemo[store]\": ()=>COMPONENT_STORE.getBoundStore(shadowRootId)\n    }[\"LoadExternalComponent.useMemo[store]\"], [\n        shadowRootId\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore(store.subscribe, store.getSnapshot);\n    const clientComponent = components?.[message.name];\n    const hasClientComponent = clientComponent != null;\n    const fallbackComponent = isReactNode(fallback) ? fallback : typeof fallback === \"object\" && fallback != null ? fallback?.[message.name] : null;\n    const uiNamespace = namespace ?? stream.assistantId;\n    const uiClient = stream.client[\"~ui\"];\n    react__WEBPACK_IMPORTED_MODULE_2__.useEffect({\n        \"LoadExternalComponent.useEffect\": ()=>{\n            if (hasClientComponent) return;\n            uiClient.getComponent(uiNamespace, message.name).then({\n                \"LoadExternalComponent.useEffect\": (html)=>{\n                    const dom = ref.current;\n                    if (!dom) return;\n                    const root = dom.shadowRoot ?? dom.attachShadow({\n                        mode: \"open\"\n                    });\n                    const fragment = document.createRange().createContextualFragment(html.replace(\"{{shadowRootId}}\", shadowRootId));\n                    root.appendChild(fragment);\n                }\n            }[\"LoadExternalComponent.useEffect\"]);\n        }\n    }[\"LoadExternalComponent.useEffect\"], [\n        uiClient,\n        uiNamespace,\n        message.name,\n        shadowRootId,\n        hasClientComponent\n    ]);\n    if (hasClientComponent) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(UseStreamContext.Provider, {\n            value: {\n                stream,\n                meta\n            },\n            children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(clientComponent, message.props)\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                id: shadowRootId,\n                ref: ref,\n                ...props\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(UseStreamContext.Provider, {\n                value: {\n                    stream,\n                    meta\n                },\n                children: state?.target != null ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(state.comp, message.props), state.target) : fallbackComponent\n            })\n        ]\n    });\n}\nfunction experimental_loadShare(name, module) {\n    if (true) return;\n    window[REQUIRE_EXTRA_SYMBOL] ??= {};\n    window[REQUIRE_EXTRA_SYMBOL][name] = module;\n}\nfunction bootstrapUiContext() {\n    if (true) {\n        return;\n    }\n    window[EXT_STORE_SYMBOL] = COMPONENT_STORE;\n    window[REQUIRE_SYMBOL] = (name)=>{\n        if (name === \"react\") return /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_2___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_2___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_2__, 2)));\n        if (name === \"react-dom\") return /*#__PURE__*/ (react_dom__WEBPACK_IMPORTED_MODULE_3___namespace_cache || (react_dom__WEBPACK_IMPORTED_MODULE_3___namespace_cache = __webpack_require__.t(react_dom__WEBPACK_IMPORTED_MODULE_3__, 2)));\n        if (name === \"react/jsx-runtime\") return /*#__PURE__*/ (react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__, 2)));\n        if (name === \"@langchain/langgraph-sdk/react\") return {\n            useStream: _react_index_js__WEBPACK_IMPORTED_MODULE_1__.useStream\n        };\n        if (name === \"@langchain/langgraph-sdk/react-ui\") {\n            return {\n                useStreamContext,\n                LoadExternalComponent: ()=>{\n                    throw new Error(\"Nesting LoadExternalComponent is not supported\");\n                }\n            };\n        }\n        if (window[REQUIRE_EXTRA_SYMBOL] != null && typeof window[REQUIRE_EXTRA_SYMBOL] === \"object\" && name in window[REQUIRE_EXTRA_SYMBOL]) {\n            return window[REQUIRE_EXTRA_SYMBOL][name];\n        }\n        throw new Error(`Unknown module...: ${name}`);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/index.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/index.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadExternalComponent: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.LoadExternalComponent),\n/* harmony export */   experimental_loadShare: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.experimental_loadShare),\n/* harmony export */   isRemoveUIMessage: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_1__.isRemoveUIMessage),\n/* harmony export */   isUIMessage: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_1__.isUIMessage),\n/* harmony export */   uiMessageReducer: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_1__.uiMessageReducer),\n/* harmony export */   useStreamContext: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.useStreamContext)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/client.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/types.js\");\n\n(0,_client_js__WEBPACK_IMPORTED_MODULE_0__.bootstrapUiContext)();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC9yZWFjdC11aS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpRDtBQUNqRCw4REFBa0I7QUFDNkU7QUFDaEIiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5XFxub2RlX21vZHVsZXNcXEBsYW5nY2hhaW5cXGxhbmdncmFwaC1zZGtcXGRpc3RcXHJlYWN0LXVpXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBib290c3RyYXBVaUNvbnRleHQgfSBmcm9tIFwiLi9jbGllbnQuanNcIjtcbmJvb3RzdHJhcFVpQ29udGV4dCgpO1xuZXhwb3J0IHsgdXNlU3RyZWFtQ29udGV4dCwgTG9hZEV4dGVybmFsQ29tcG9uZW50LCBleHBlcmltZW50YWxfbG9hZFNoYXJlLCB9IGZyb20gXCIuL2NsaWVudC5qc1wiO1xuZXhwb3J0IHsgdWlNZXNzYWdlUmVkdWNlciwgaXNVSU1lc3NhZ2UsIGlzUmVtb3ZlVUlNZXNzYWdlLCB9IGZyb20gXCIuL3R5cGVzLmpzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/types.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/types.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRemoveUIMessage: () => (/* binding */ isRemoveUIMessage),\n/* harmony export */   isUIMessage: () => (/* binding */ isUIMessage),\n/* harmony export */   uiMessageReducer: () => (/* binding */ uiMessageReducer)\n/* harmony export */ });\nfunction isUIMessage(message) {\n    if (typeof message !== \"object\" || message == null)\n        return false;\n    if (!(\"type\" in message))\n        return false;\n    return message.type === \"ui\";\n}\nfunction isRemoveUIMessage(message) {\n    if (typeof message !== \"object\" || message == null)\n        return false;\n    if (!(\"type\" in message))\n        return false;\n    return message.type === \"remove-ui\";\n}\nfunction uiMessageReducer(state, update) {\n    const events = Array.isArray(update) ? update : [update];\n    let newState = state.slice();\n    for (const event of events) {\n        if (event.type === \"remove-ui\") {\n            newState = newState.filter((ui) => ui.id !== event.id);\n            continue;\n        }\n        const index = state.findIndex((ui) => ui.id === event.id);\n        if (index !== -1) {\n            newState[index] = event.metadata.merge\n                ? { ...event, props: { ...state[index].props, ...event.props } }\n                : event;\n        }\n        else {\n            newState.push(event);\n        }\n    }\n    return newState;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStream: () => (/* reexport safe */ _stream_js__WEBPACK_IMPORTED_MODULE_0__.useStream)\n/* harmony export */ });\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/stream.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC9yZWFjdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QyIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC5fZGUyYTM2MGFjY2I5OGZhMTI4YmRmZGIzNDAwYmI1MDlcXG5vZGVfbW9kdWxlc1xcQGxhbmdjaGFpblxcbGFuZ2dyYXBoLXNka1xcZGlzdFxccmVhY3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHVzZVN0cmVhbSwgfSBmcm9tIFwiLi9zdHJlYW0uanNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/stream.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/stream.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStream: () => (/* binding */ useStream)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/client.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @langchain/core/messages */ \"(ssr)/./node_modules/.pnpm/@langchain+core@0.3.56_openai@4.100.0_ws@8.18.2_zod@3.24.4_/node_modules/@langchain/core/messages.js\");\n/* __LC_ALLOW_ENTRYPOINT_SIDE_EFFECTS__ */ /* __next_internal_client_entry_do_not_use__ useStream auto */ \n\n\nclass StreamError extends Error {\n    constructor(data){\n        super(data.message);\n        this.name = data.name ?? data.error ?? \"StreamError\";\n    }\n    static isStructuredError(error) {\n        return typeof error === \"object\" && error != null && \"message\" in error;\n    }\n}\nfunction tryConvertToChunk(message) {\n    try {\n        return (0,_langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__.convertToChunk)(message);\n    } catch  {\n        return null;\n    }\n}\nclass MessageTupleManager {\n    constructor(){\n        Object.defineProperty(this, \"chunks\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        this.chunks = {};\n    }\n    add(serialized) {\n        // TODO: this is sometimes sent from the API\n        // figure out how to prevent this or move this to LC.js\n        if (serialized.type.endsWith(\"MessageChunk\")) {\n            serialized.type = serialized.type.slice(0, -\"MessageChunk\".length).toLowerCase();\n        }\n        const message = (0,_langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__.coerceMessageLikeToMessage)(serialized);\n        const chunk = tryConvertToChunk(message);\n        const id = (chunk ?? message).id;\n        if (!id) {\n            console.warn(\"No message ID found for chunk, ignoring in state\", serialized);\n            return null;\n        }\n        this.chunks[id] ??= {};\n        if (chunk) {\n            const prev = this.chunks[id].chunk;\n            this.chunks[id].chunk = ((0,_langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__.isBaseMessageChunk)(prev) ? prev : null)?.concat(chunk) ?? chunk;\n        } else {\n            this.chunks[id].chunk = message;\n        }\n        return id;\n    }\n    clear() {\n        this.chunks = {};\n    }\n    get(id, defaultIndex) {\n        if (this.chunks[id] == null) return null;\n        this.chunks[id].index ??= defaultIndex;\n        return this.chunks[id];\n    }\n}\nconst toMessageDict = (chunk)=>{\n    const { type, data } = chunk.toDict();\n    return {\n        ...data,\n        type\n    };\n};\nfunction unique(array) {\n    return [\n        ...new Set(array)\n    ];\n}\nfunction findLastIndex(array, predicate) {\n    for(let i = array.length - 1; i >= 0; i--){\n        if (predicate(array[i])) return i;\n    }\n    return -1;\n}\nfunction getBranchSequence(history) {\n    const childrenMap = {};\n    // First pass - collect nodes for each checkpoint\n    history.forEach((state)=>{\n        const checkpointId = state.parent_checkpoint?.checkpoint_id ?? \"$\";\n        childrenMap[checkpointId] ??= [];\n        childrenMap[checkpointId].push(state);\n    });\n    const rootSequence = {\n        type: \"sequence\",\n        items: []\n    };\n    const queue = [\n        {\n            id: \"$\",\n            sequence: rootSequence,\n            path: []\n        }\n    ];\n    const paths = [];\n    const visited = new Set();\n    while(queue.length > 0){\n        const task = queue.shift();\n        if (visited.has(task.id)) continue;\n        visited.add(task.id);\n        const children = childrenMap[task.id];\n        if (children == null || children.length === 0) continue;\n        // If we've encountered a fork (2+ children), push the fork\n        // to the sequence and add a new sequence for each child\n        let fork;\n        if (children.length > 1) {\n            fork = {\n                type: \"fork\",\n                items: []\n            };\n            task.sequence.items.push(fork);\n        }\n        for (const value of children){\n            const id = value.checkpoint.checkpoint_id;\n            let sequence = task.sequence;\n            let path = task.path;\n            if (fork != null) {\n                sequence = {\n                    type: \"sequence\",\n                    items: []\n                };\n                fork.items.unshift(sequence);\n                path = path.slice();\n                path.push(id);\n                paths.push(path);\n            }\n            sequence.items.push({\n                type: \"node\",\n                value,\n                path\n            });\n            queue.push({\n                id,\n                sequence,\n                path\n            });\n        }\n    }\n    return {\n        rootSequence,\n        paths\n    };\n}\nconst PATH_SEP = \">\";\nconst ROOT_ID = \"$\";\n// Get flat view\nfunction getBranchView(sequence, paths, branch) {\n    const path = branch.split(PATH_SEP);\n    const pathMap = {};\n    for (const path of paths){\n        const parent = path.at(-2) ?? ROOT_ID;\n        pathMap[parent] ??= [];\n        pathMap[parent].unshift(path);\n    }\n    const history = [];\n    const branchByCheckpoint = {};\n    const forkStack = path.slice();\n    const queue = [\n        ...sequence.items\n    ];\n    while(queue.length > 0){\n        const item = queue.shift();\n        if (item.type === \"node\") {\n            history.push(item.value);\n            branchByCheckpoint[item.value.checkpoint.checkpoint_id] = {\n                branch: item.path.join(PATH_SEP),\n                branchOptions: (item.path.length > 0 ? pathMap[item.path.at(-2) ?? ROOT_ID] ?? [] : []).map((p)=>p.join(PATH_SEP))\n            };\n        }\n        if (item.type === \"fork\") {\n            const forkId = forkStack.shift();\n            const index = forkId != null ? item.items.findIndex((value)=>{\n                const firstItem = value.items.at(0);\n                if (!firstItem || firstItem.type !== \"node\") return false;\n                return firstItem.value.checkpoint.checkpoint_id === forkId;\n            }) : -1;\n            const nextItems = item.items.at(index)?.items ?? [];\n            queue.push(...nextItems);\n        }\n    }\n    return {\n        history,\n        branchByCheckpoint\n    };\n}\nfunction fetchHistory(client, threadId) {\n    return client.threads.getHistory(threadId, {\n        limit: 1000\n    });\n}\nfunction useThreadHistory(threadId, client, clearCallbackRef, submittingRef) {\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fetcher = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useThreadHistory.useCallback[fetcher]\": (threadId)=>{\n            if (threadId != null) {\n                return fetchHistory(client, threadId).then({\n                    \"useThreadHistory.useCallback[fetcher]\": (history)=>{\n                        setHistory(history);\n                        return history;\n                    }\n                }[\"useThreadHistory.useCallback[fetcher]\"]);\n            }\n            setHistory([]);\n            clearCallbackRef.current?.();\n            return Promise.resolve([]);\n        }\n    }[\"useThreadHistory.useCallback[fetcher]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useThreadHistory.useEffect\": ()=>{\n            if (submittingRef.current) return;\n            fetcher(threadId);\n        }\n    }[\"useThreadHistory.useEffect\"], [\n        fetcher,\n        submittingRef,\n        threadId\n    ]);\n    return {\n        data: history,\n        mutate: (mutateId)=>fetcher(mutateId ?? threadId)\n    };\n}\nconst useControllableThreadId = (options)=>{\n    const [localThreadId, _setLocalThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(options?.threadId ?? null);\n    const onThreadIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(options?.onThreadId);\n    onThreadIdRef.current = options?.onThreadId;\n    const onThreadId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useControllableThreadId.useCallback[onThreadId]\": (threadId)=>{\n            _setLocalThreadId(threadId);\n            onThreadIdRef.current?.(threadId);\n        }\n    }[\"useControllableThreadId.useCallback[onThreadId]\"], []);\n    if (!options || !(\"threadId\" in options)) {\n        return [\n            localThreadId,\n            onThreadId\n        ];\n    }\n    return [\n        options.threadId ?? null,\n        onThreadId\n    ];\n};\nfunction useStream(options) {\n    let { assistantId, messagesKey, onError, onFinish } = options;\n    messagesKey ??= \"messages\";\n    const client = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"useStream.useMemo[client]\": ()=>options.client ?? new _client_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                apiUrl: options.apiUrl,\n                apiKey: options.apiKey,\n                callerOptions: options.callerOptions,\n                defaultHeaders: options.defaultHeaders\n            })\n    }[\"useStream.useMemo[client]\"], [\n        options.client,\n        options.apiKey,\n        options.apiUrl,\n        options.callerOptions,\n        options.defaultHeaders\n    ]);\n    const [threadId, onThreadId] = useControllableThreadId(options);\n    const [branch, setBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamError, setStreamError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [streamValues, setStreamValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messageManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new MessageTupleManager());\n    const submittingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const trackStreamModeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const trackStreamMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useStream.useCallback[trackStreamMode]\": (...mode)=>{\n            for (const m of mode){\n                if (!trackStreamModeRef.current.includes(m)) {\n                    trackStreamModeRef.current.push(m);\n                }\n            }\n        }\n    }[\"useStream.useCallback[trackStreamMode]\"], []);\n    const hasUpdateListener = options.onUpdateEvent != null;\n    const hasCustomListener = options.onCustomEvent != null;\n    const hasLangChainListener = options.onLangChainEvent != null;\n    const hasDebugListener = options.onDebugEvent != null;\n    const callbackStreamMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"useStream.useMemo[callbackStreamMode]\": ()=>{\n            const modes = [];\n            if (hasUpdateListener) modes.push(\"updates\");\n            if (hasCustomListener) modes.push(\"custom\");\n            if (hasLangChainListener) modes.push(\"events\");\n            if (hasDebugListener) modes.push(\"debug\");\n            return modes;\n        }\n    }[\"useStream.useMemo[callbackStreamMode]\"], [\n        hasUpdateListener,\n        hasCustomListener,\n        hasLangChainListener,\n        hasDebugListener\n    ]);\n    const clearCallbackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    clearCallbackRef.current = ()=>{\n        setStreamError(undefined);\n        setStreamValues(null);\n    };\n    // TODO: this should be done on the server to avoid pagination\n    // TODO: should we permit adapter? SWR / React Query?\n    const history = useThreadHistory(threadId, client, clearCallbackRef, submittingRef);\n    const getMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"useStream.useMemo[getMessages]\": ()=>{\n            return ({\n                \"useStream.useMemo[getMessages]\": (value)=>Array.isArray(value[messagesKey]) ? value[messagesKey] : []\n            })[\"useStream.useMemo[getMessages]\"];\n        }\n    }[\"useStream.useMemo[getMessages]\"], [\n        messagesKey\n    ]);\n    const { rootSequence, paths } = getBranchSequence(history.data);\n    const { history: flatHistory, branchByCheckpoint } = getBranchView(rootSequence, paths, branch);\n    const threadHead = flatHistory.at(-1);\n    const historyValues = threadHead?.values ?? {};\n    const historyError = (()=>{\n        const error = threadHead?.tasks?.at(-1)?.error;\n        if (error == null) return undefined;\n        try {\n            const parsed = JSON.parse(error);\n            if (StreamError.isStructuredError(parsed)) {\n                return new StreamError(parsed);\n            }\n            return parsed;\n        } catch  {\n        // do nothing\n        }\n        return error;\n    })();\n    const messageMetadata = (()=>{\n        const alreadyShown = new Set();\n        return getMessages(historyValues).map((message, idx)=>{\n            const messageId = message.id ?? idx;\n            const firstSeenIdx = findLastIndex(history.data, (state)=>getMessages(state.values).map((m, idx)=>m.id ?? idx).includes(messageId));\n            const firstSeen = history.data[firstSeenIdx];\n            let branch = firstSeen ? branchByCheckpoint[firstSeen.checkpoint.checkpoint_id] : undefined;\n            if (!branch?.branch?.length) branch = undefined;\n            // serialize branches\n            const optionsShown = branch?.branchOptions?.flat(2).join(\",\");\n            if (optionsShown) {\n                if (alreadyShown.has(optionsShown)) branch = undefined;\n                alreadyShown.add(optionsShown);\n            }\n            return {\n                messageId: messageId.toString(),\n                firstSeenState: firstSeen,\n                branch: branch?.branch,\n                branchOptions: branch?.branchOptions\n            };\n        });\n    })();\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useStream.useCallback[stop]\": ()=>{\n            if (abortRef.current != null) abortRef.current.abort();\n            abortRef.current = null;\n        }\n    }[\"useStream.useCallback[stop]\"], []);\n    const submit = async (values, submitOptions)=>{\n        try {\n            setIsLoading(true);\n            setStreamError(undefined);\n            submittingRef.current = true;\n            abortRef.current = new AbortController();\n            // Unbranch things\n            const newPath = submitOptions?.checkpoint?.checkpoint_id ? branchByCheckpoint[submitOptions?.checkpoint?.checkpoint_id]?.branch : undefined;\n            if (newPath != null) setBranch(newPath ?? \"\");\n            // Assumption: we're setting the initial value\n            // Used for instant feedback\n            setStreamValues(()=>{\n                const values = {\n                    ...historyValues\n                };\n                if (submitOptions?.optimisticValues != null) {\n                    return {\n                        ...values,\n                        ...typeof submitOptions.optimisticValues === \"function\" ? submitOptions.optimisticValues(values) : submitOptions.optimisticValues\n                    };\n                }\n                return values;\n            });\n            let usableThreadId = threadId;\n            if (!usableThreadId) {\n                const thread = await client.threads.create();\n                onThreadId(thread.thread_id);\n                usableThreadId = thread.thread_id;\n            }\n            const streamMode = unique([\n                ...submitOptions?.streamMode ?? [],\n                ...trackStreamModeRef.current,\n                ...callbackStreamMode\n            ]);\n            const checkpoint = submitOptions?.checkpoint ?? threadHead?.checkpoint ?? undefined;\n            // @ts-expect-error\n            if (checkpoint != null) delete checkpoint.thread_id;\n            const run = client.runs.stream(usableThreadId, assistantId, {\n                input: values,\n                config: submitOptions?.config,\n                command: submitOptions?.command,\n                interruptBefore: submitOptions?.interruptBefore,\n                interruptAfter: submitOptions?.interruptAfter,\n                metadata: submitOptions?.metadata,\n                multitaskStrategy: submitOptions?.multitaskStrategy,\n                onCompletion: submitOptions?.onCompletion,\n                onDisconnect: submitOptions?.onDisconnect ?? \"cancel\",\n                signal: abortRef.current.signal,\n                checkpoint,\n                streamMode\n            });\n            let streamError;\n            for await (const { event, data } of run){\n                if (event === \"error\") {\n                    streamError = new StreamError(data);\n                    break;\n                }\n                if (event === \"updates\") options.onUpdateEvent?.(data);\n                if (event === \"custom\") options.onCustomEvent?.(data, {\n                    mutate: (update)=>setStreamValues((prev)=>{\n                            // should not happen\n                            if (prev == null) return prev;\n                            return {\n                                ...prev,\n                                ...typeof update === \"function\" ? update(prev) : update\n                            };\n                        })\n                });\n                if (event === \"metadata\") options.onMetadataEvent?.(data);\n                if (event === \"events\") options.onLangChainEvent?.(data);\n                if (event === \"debug\") options.onDebugEvent?.(data);\n                if (event === \"values\") setStreamValues(data);\n                if (event === \"messages\") {\n                    const [serialized] = data;\n                    const messageId = messageManagerRef.current.add(serialized);\n                    if (!messageId) {\n                        console.warn(\"Failed to add message to manager, no message ID found\");\n                        continue;\n                    }\n                    setStreamValues((streamValues)=>{\n                        const values = {\n                            ...historyValues,\n                            ...streamValues\n                        };\n                        // Assumption: we're concatenating the message\n                        const messages = getMessages(values).slice();\n                        const { chunk, index } = messageManagerRef.current.get(messageId, messages.length) ?? {};\n                        if (!chunk || index == null) return values;\n                        messages[index] = toMessageDict(chunk);\n                        return {\n                            ...values,\n                            [messagesKey]: messages\n                        };\n                    });\n                }\n            }\n            // TODO: stream created checkpoints to avoid an unnecessary network request\n            const result = await history.mutate(usableThreadId);\n            setStreamValues(null);\n            if (streamError != null) throw streamError;\n            const lastHead = result.at(0);\n            if (lastHead) onFinish?.(lastHead);\n        } catch (error) {\n            if (!(error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\"))) {\n                console.error(error);\n                setStreamError(error);\n                onError?.(error);\n            }\n        } finally{\n            setIsLoading(false);\n            // Assumption: messages are already handled, we can clear the manager\n            messageManagerRef.current.clear();\n            submittingRef.current = false;\n            abortRef.current = null;\n        }\n    };\n    const error = streamError ?? historyError;\n    const values = streamValues ?? historyValues;\n    return {\n        get values () {\n            trackStreamMode(\"values\");\n            return values;\n        },\n        client,\n        assistantId,\n        error,\n        isLoading,\n        stop,\n        submit,\n        branch,\n        setBranch,\n        history: flatHistory,\n        experimental_branchTree: rootSequence,\n        get interrupt () {\n            // Don't show the interrupt if the stream is loading\n            if (isLoading) return undefined;\n            const interrupts = threadHead?.tasks?.at(-1)?.interrupts;\n            if (interrupts == null || interrupts.length === 0) {\n                // check if there's a next task present\n                const next = threadHead?.next ?? [];\n                if (!next.length || error != null) return undefined;\n                return {\n                    when: \"breakpoint\"\n                };\n            }\n            // Return only the current interrupt\n            return interrupts.at(-1);\n        },\n        get messages () {\n            trackStreamMode(\"messages-tuple\", \"values\");\n            return getMessages(values);\n        },\n        getMessagesMetadata (message, index) {\n            trackStreamMode(\"messages-tuple\", \"values\");\n            return messageMetadata?.find((m)=>m.messageId === (message.id ?? index));\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _getFetchImplementation: () => (/* binding */ _getFetchImplementation),\n/* harmony export */   overrideFetchImplementation: () => (/* binding */ overrideFetchImplementation)\n/* harmony export */ });\n// Wrap the default fetch call due to issues with illegal invocations\n// in some environments:\n// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err\n// @ts-expect-error Broad typing to support a range of fetch implementations\nconst DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);\nconst LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for(\"lg:fetch_implementation\");\n/**\n * Overrides the fetch implementation used for LangSmith calls.\n * You should use this if you need to use an implementation of fetch\n * other than the default global (e.g. for dealing with proxies).\n * @param fetch The new fetch function to use.\n */\nconst overrideFetchImplementation = (fetch) => {\n    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch;\n};\n/**\n * @internal\n */\nconst _getFetchImplementation = () => {\n    return (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ??\n        DEFAULT_FETCH_IMPLEMENTATION);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncCaller: () => (/* binding */ AsyncCaller)\n/* harmony export */ });\n/* harmony import */ var p_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! p-retry */ \"(ssr)/./node_modules/.pnpm/p-retry@4.6.2/node_modules/p-retry/index.js\");\n/* harmony import */ var p_queue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! p-queue */ \"(ssr)/./node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/index.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../singletons/fetch.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\");\n\n\n\nconst STATUS_NO_RETRY = [\n    400, // Bad Request\n    401, // Unauthorized\n    402, // Payment required\n    403, // Forbidden\n    404, // Not Found\n    405, // Method Not Allowed\n    406, // Not Acceptable\n    407, // Proxy Authentication Required\n    408, // Request Timeout\n    409, // Conflict\n    422, // Unprocessable Entity\n];\n/**\n * Do not rely on globalThis.Response, rather just\n * do duck typing\n */\nfunction isResponse(x) {\n    if (x == null || typeof x !== \"object\")\n        return false;\n    return \"status\" in x && \"statusText\" in x && \"text\" in x;\n}\n/**\n * Utility error to properly handle failed requests\n */\nclass HTTPError extends Error {\n    constructor(status, message, response) {\n        super(`HTTP ${status}: ${message}`);\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"text\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.status = status;\n        this.text = message;\n        this.response = response;\n    }\n    static async fromResponse(response, options) {\n        try {\n            return new HTTPError(response.status, await response.text(), options?.includeResponse ? response : undefined);\n        }\n        catch {\n            return new HTTPError(response.status, response.statusText, options?.includeResponse ? response : undefined);\n        }\n    }\n}\n/**\n * A class that can be used to make async calls with concurrency and retry logic.\n *\n * This is useful for making calls to any kind of \"expensive\" external resource,\n * be it because it's rate-limited, subject to network issues, etc.\n *\n * Concurrent calls are limited by the `maxConcurrency` parameter, which defaults\n * to `Infinity`. This means that by default, all calls will be made in parallel.\n *\n * Retries are limited by the `maxRetries` parameter, which defaults to 5. This\n * means that by default, each call will be retried up to 5 times, with an\n * exponential backoff between each attempt.\n */\nclass AsyncCaller {\n    constructor(params) {\n        Object.defineProperty(this, \"maxConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxRetries\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"onFailedResponseHook\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"customFetch\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxConcurrency = params.maxConcurrency ?? Infinity;\n        this.maxRetries = params.maxRetries ?? 4;\n        if ( true) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n                concurrency: this.maxConcurrency,\n            });\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__({ concurrency: this.maxConcurrency });\n        }\n        this.onFailedResponseHook = params?.onFailedResponseHook;\n        this.customFetch = params.fetch;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    call(callable, ...args) {\n        const onFailedResponseHook = this.onFailedResponseHook;\n        return this.queue.add(() => p_retry__WEBPACK_IMPORTED_MODULE_0__(() => callable(...args).catch(async (error) => {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (error instanceof Error) {\n                throw error;\n            }\n            else if (isResponse(error)) {\n                throw await HTTPError.fromResponse(error, {\n                    includeResponse: !!onFailedResponseHook,\n                });\n            }\n            else {\n                throw new Error(error);\n            }\n        }), {\n            async onFailedAttempt(error) {\n                if (error.message.startsWith(\"Cancel\") ||\n                    error.message.startsWith(\"TimeoutError\") ||\n                    error.message.startsWith(\"AbortError\")) {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                if (error?.code === \"ECONNABORTED\") {\n                    throw error;\n                }\n                if (error instanceof HTTPError) {\n                    if (STATUS_NO_RETRY.includes(error.status)) {\n                        throw error;\n                    }\n                    if (onFailedResponseHook && error.response) {\n                        await onFailedResponseHook(error.response);\n                    }\n                }\n            },\n            // If needed we can change some of the defaults here,\n            // but they're quite sensible.\n            retries: this.maxRetries,\n            randomize: true,\n        }), { throwOnTimeout: true });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    callWithOptions(options, callable, ...args) {\n        // Note this doesn't cancel the underlying request,\n        // when available prefer to use the signal option of the underlying call\n        if (options.signal) {\n            return Promise.race([\n                this.call(callable, ...args),\n                new Promise((_, reject) => {\n                    options.signal?.addEventListener(\"abort\", () => {\n                        reject(new Error(\"AbortError\"));\n                    });\n                }),\n            ]);\n        }\n        return this.call(callable, ...args);\n    }\n    fetch(...args) {\n        const fetchFn = this.customFetch ?? (0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__._getFetchImplementation)();\n        return this.call(() => fetchFn(...args).then((res) => (res.ok ? res : Promise.reject(res))));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/env.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/env.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnvironmentVariable: () => (/* binding */ getEnvironmentVariable)\n/* harmony export */ });\nfunction getEnvironmentVariable(name) {\n    // Certain setups (Deno, frontend) will throw an error if you try to access environment variables\n    try {\n        return typeof process !== \"undefined\"\n            ? // eslint-disable-next-line no-process-env\n                process.env?.[name]\n            : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC91dGlscy9lbnYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC5fZGUyYTM2MGFjY2I5OGZhMTI4YmRmZGIzNDAwYmI1MDlcXG5vZGVfbW9kdWxlc1xcQGxhbmdjaGFpblxcbGFuZ2dyYXBoLXNka1xcZGlzdFxcdXRpbHNcXGVudi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0RW52aXJvbm1lbnRWYXJpYWJsZShuYW1lKSB7XG4gICAgLy8gQ2VydGFpbiBzZXR1cHMgKERlbm8sIGZyb250ZW5kKSB3aWxsIHRocm93IGFuIGVycm9yIGlmIHlvdSB0cnkgdG8gYWNjZXNzIGVudmlyb25tZW50IHZhcmlhYmxlc1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgcHJvY2VzcyAhPT0gXCJ1bmRlZmluZWRcIlxuICAgICAgICAgICAgPyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcHJvY2Vzcy1lbnZcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudj8uW25hbWVdXG4gICAgICAgICAgICA6IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/env.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeSignals: () => (/* binding */ mergeSignals)\n/* harmony export */ });\nfunction mergeSignals(...signals) {\n    const nonZeroSignals = signals.filter((signal) => signal != null);\n    if (nonZeroSignals.length === 0)\n        return undefined;\n    if (nonZeroSignals.length === 1)\n        return nonZeroSignals[0];\n    const controller = new AbortController();\n    for (const signal of signals) {\n        if (signal?.aborted) {\n            controller.abort(signal.reason);\n            return controller.signal;\n        }\n        signal?.addEventListener(\"abort\", () => controller.abort(signal.reason), {\n            once: true,\n        });\n    }\n    return controller.signal;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuX2RlMmEzNjBhY2NiOThmYTEyOGJkZmRiMzQwMGJiNTA5L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC91dGlscy9zaWduYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC5fZGUyYTM2MGFjY2I5OGZhMTI4YmRmZGIzNDAwYmI1MDlcXG5vZGVfbW9kdWxlc1xcQGxhbmdjaGFpblxcbGFuZ2dyYXBoLXNka1xcZGlzdFxcdXRpbHNcXHNpZ25hbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIG1lcmdlU2lnbmFscyguLi5zaWduYWxzKSB7XG4gICAgY29uc3Qgbm9uWmVyb1NpZ25hbHMgPSBzaWduYWxzLmZpbHRlcigoc2lnbmFsKSA9PiBzaWduYWwgIT0gbnVsbCk7XG4gICAgaWYgKG5vblplcm9TaWduYWxzLmxlbmd0aCA9PT0gMClcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICBpZiAobm9uWmVyb1NpZ25hbHMubGVuZ3RoID09PSAxKVxuICAgICAgICByZXR1cm4gbm9uWmVyb1NpZ25hbHNbMF07XG4gICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBmb3IgKGNvbnN0IHNpZ25hbCBvZiBzaWduYWxzKSB7XG4gICAgICAgIGlmIChzaWduYWw/LmFib3J0ZWQpIHtcbiAgICAgICAgICAgIGNvbnRyb2xsZXIuYWJvcnQoc2lnbmFsLnJlYXNvbik7XG4gICAgICAgICAgICByZXR1cm4gY29udHJvbGxlci5zaWduYWw7XG4gICAgICAgIH1cbiAgICAgICAgc2lnbmFsPy5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgKCkgPT4gY29udHJvbGxlci5hYm9ydChzaWduYWwucmVhc29uKSwge1xuICAgICAgICAgICAgb25jZTogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBjb250cm9sbGVyLnNpZ25hbDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BytesLineDecoder: () => (/* binding */ BytesLineDecoder),\n/* harmony export */   SSEDecoder: () => (/* binding */ SSEDecoder)\n/* harmony export */ });\nconst CR = \"\\r\".charCodeAt(0);\nconst LF = \"\\n\".charCodeAt(0);\nconst NULL = \"\\0\".charCodeAt(0);\nconst COLON = \":\".charCodeAt(0);\nconst SPACE = \" \".charCodeAt(0);\nconst TRAILING_NEWLINE = [CR, LF];\nclass BytesLineDecoder extends TransformStream {\n    constructor() {\n        let buffer = [];\n        let trailingCr = false;\n        super({\n            start() {\n                buffer = [];\n                trailingCr = false;\n            },\n            transform(chunk, controller) {\n                // See https://docs.python.org/3/glossary.html#term-universal-newlines\n                let text = chunk;\n                // Handle trailing CR from previous chunk\n                if (trailingCr) {\n                    text = joinArrays([[CR], text]);\n                    trailingCr = false;\n                }\n                // Check for trailing CR in current chunk\n                if (text.length > 0 && text.at(-1) === CR) {\n                    trailingCr = true;\n                    text = text.subarray(0, -1);\n                }\n                if (!text.length)\n                    return;\n                const trailingNewline = TRAILING_NEWLINE.includes(text.at(-1));\n                const lastIdx = text.length - 1;\n                const { lines } = text.reduce((acc, cur, idx) => {\n                    if (acc.from > idx)\n                        return acc;\n                    if (cur === CR || cur === LF) {\n                        acc.lines.push(text.subarray(acc.from, idx));\n                        if (cur === CR && text[idx + 1] === LF) {\n                            acc.from = idx + 2;\n                        }\n                        else {\n                            acc.from = idx + 1;\n                        }\n                    }\n                    if (idx === lastIdx && acc.from <= lastIdx) {\n                        acc.lines.push(text.subarray(acc.from));\n                    }\n                    return acc;\n                }, { lines: [], from: 0 });\n                if (lines.length === 1 && !trailingNewline) {\n                    buffer.push(lines[0]);\n                    return;\n                }\n                if (buffer.length) {\n                    // Include existing buffer in first line\n                    buffer.push(lines[0]);\n                    lines[0] = joinArrays(buffer);\n                    buffer = [];\n                }\n                if (!trailingNewline) {\n                    // If the last segment is not newline terminated,\n                    // buffer it for the next chunk\n                    if (lines.length)\n                        buffer = [lines.pop()];\n                }\n                // Enqueue complete lines\n                for (const line of lines) {\n                    controller.enqueue(line);\n                }\n            },\n            flush(controller) {\n                if (buffer.length) {\n                    controller.enqueue(joinArrays(buffer));\n                }\n            },\n        });\n    }\n}\nclass SSEDecoder extends TransformStream {\n    constructor() {\n        let event = \"\";\n        let data = [];\n        let lastEventId = \"\";\n        let retry = null;\n        const decoder = new TextDecoder();\n        super({\n            transform(chunk, controller) {\n                // Handle empty line case\n                if (!chunk.length) {\n                    if (!event && !data.length && !lastEventId && retry == null)\n                        return;\n                    const sse = {\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    };\n                    // NOTE: as per the SSE spec, do not reset lastEventId\n                    event = \"\";\n                    data = [];\n                    retry = null;\n                    controller.enqueue(sse);\n                    return;\n                }\n                // Ignore comments\n                if (chunk[0] === COLON)\n                    return;\n                const sepIdx = chunk.indexOf(COLON);\n                if (sepIdx === -1)\n                    return;\n                const fieldName = decoder.decode(chunk.subarray(0, sepIdx));\n                let value = chunk.subarray(sepIdx + 1);\n                if (value[0] === SPACE)\n                    value = value.subarray(1);\n                if (fieldName === \"event\") {\n                    event = decoder.decode(value);\n                }\n                else if (fieldName === \"data\") {\n                    data.push(value);\n                }\n                else if (fieldName === \"id\") {\n                    if (value.indexOf(NULL) === -1)\n                        lastEventId = decoder.decode(value);\n                }\n                else if (fieldName === \"retry\") {\n                    const retryNum = Number.parseInt(decoder.decode(value));\n                    if (!Number.isNaN(retryNum))\n                        retry = retryNum;\n                }\n            },\n            flush(controller) {\n                if (event) {\n                    controller.enqueue({\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    });\n                }\n            },\n        });\n    }\n}\nfunction joinArrays(data) {\n    const totalLength = data.reduce((acc, curr) => acc + curr.length, 0);\n    let merged = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const c of data) {\n        merged.set(c, offset);\n        offset += c.length;\n    }\n    return merged;\n}\nfunction decodeArraysToJson(decoder, data) {\n    return JSON.parse(decoder.decode(joinArrays(data)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IterableReadableStream: () => (/* binding */ IterableReadableStream)\n/* harmony export */ });\n/*\n * Support async iterator syntax for ReadableStreams in all environments.\n * Source: https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nclass IterableReadableStream extends ReadableStream {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"reader\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    ensureReader() {\n        if (!this.reader) {\n            this.reader = this.getReader();\n        }\n    }\n    async next() {\n        this.ensureReader();\n        try {\n            const result = await this.reader.read();\n            if (result.done) {\n                this.reader.releaseLock(); // release lock when stream becomes closed\n                return {\n                    done: true,\n                    value: undefined,\n                };\n            }\n            else {\n                return {\n                    done: false,\n                    value: result.value,\n                };\n            }\n        }\n        catch (e) {\n            this.reader.releaseLock(); // release lock when stream becomes errored\n            throw e;\n        }\n    }\n    async return() {\n        this.ensureReader();\n        // If wrapped in a Node stream, cancel is already called.\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        return { done: true, value: undefined };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async throw(e) {\n        this.ensureReader();\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        throw e;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore Not present in Node 18 types, required in latest Node 22\n    async [Symbol.asyncDispose]() {\n        await this.return();\n    }\n    [Symbol.asyncIterator]() {\n        return this;\n    }\n    static fromReadableStream(stream) {\n        // From https://developer.mozilla.org/en-US/docs/Web/API/Streams_API/Using_readable_streams#reading_the_stream\n        const reader = stream.getReader();\n        return new IterableReadableStream({\n            start(controller) {\n                return pump();\n                function pump() {\n                    return reader.read().then(({ done, value }) => {\n                        // When no more data needs to be consumed, close the stream\n                        if (done) {\n                            controller.close();\n                            return;\n                        }\n                        // Enqueue the next data chunk into our target stream\n                        controller.enqueue(value);\n                        return pump();\n                    });\n                }\n            },\n            cancel() {\n                reader.releaseLock();\n            },\n        });\n    }\n    static fromAsyncGenerator(generator) {\n        return new IterableReadableStream({\n            async pull(controller) {\n                const { value, done } = await generator.next();\n                // When no more data needs to be consumed, close the stream\n                if (done) {\n                    controller.close();\n                }\n                // Fix: `else if (value)` will hang the streaming when nullish value (e.g. empty string) is pulled\n                controller.enqueue(value);\n            },\n            async cancel(reason) {\n                await generator.return(reason);\n            },\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Client: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.Client),
/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.overrideFetchImplementation)
/* harmony export */ });
/* harmony import */ var _dist_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/index.js */ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/index.js");


/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/react-ui.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/react-ui.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoadExternalComponent: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.LoadExternalComponent),
/* harmony export */   experimental_loadShare: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.experimental_loadShare),
/* harmony export */   isRemoveUIMessage: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.isRemoveUIMessage),
/* harmony export */   isUIMessage: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.isUIMessage),
/* harmony export */   uiMessageReducer: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.uiMessageReducer),
/* harmony export */   useStreamContext: () => (/* reexport safe */ _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__.useStreamContext)
/* harmony export */ });
/* harmony import */ var _dist_react_ui_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/react-ui/index.js */ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react-ui/index.js");


/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/react.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/react.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useStream: () => (/* reexport safe */ _dist_react_index_js__WEBPACK_IMPORTED_MODULE_0__.useStream)
/* harmony export */ });
/* harmony import */ var _dist_react_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/react/index.js */ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-sdk@0._de2a360accb98fa128bdfdb3400bb509/node_modules/@langchain/langgraph-sdk/dist/react/index.js");


/***/ })

};
;