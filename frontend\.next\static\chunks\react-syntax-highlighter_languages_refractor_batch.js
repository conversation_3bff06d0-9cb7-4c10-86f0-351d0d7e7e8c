"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_batch"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/batch.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/batch.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = batch\nbatch.displayName = 'batch'\nbatch.aliases = []\nfunction batch(Prism) {\n  ;(function (Prism) {\n    var variable = /%%?[~:\\w]+%?|!\\S+!/\n    var parameter = {\n      pattern: /\\/[a-z?]+(?=[ :]|$):?|-[a-z]\\b|--[a-z-]+\\b/im,\n      alias: 'attr-name',\n      inside: {\n        punctuation: /:/\n      }\n    }\n    var string = /\"(?:[\\\\\"]\"|[^\"])*\"(?!\")/\n    var number = /(?:\\b|-)\\d+\\b/\n    Prism.languages.batch = {\n      comment: [\n        /^::.*/m,\n        {\n          pattern: /((?:^|[&(])[ \\t]*)rem\\b(?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true\n        }\n      ],\n      label: {\n        pattern: /^:.*/m,\n        alias: 'property'\n      },\n      command: [\n        {\n          // FOR command\n          pattern:\n            /((?:^|[&(])[ \\t]*)for(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* \\S+ in \\([^)]+\\) do/im,\n          lookbehind: true,\n          inside: {\n            keyword: /\\b(?:do|in)\\b|^for\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // IF command\n          pattern:\n            /((?:^|[&(])[ \\t]*)if(?: \\/[a-z?](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:not )?(?:cmdextversion \\d+|defined \\w+|errorlevel \\d+|exist \\S+|(?:\"[^\"]*\"|(?!\")(?:(?!==)\\S)+)?(?:==| (?:equ|geq|gtr|leq|lss|neq) )(?:\"[^\"]*\"|[^\\s\"]\\S*))/im,\n          lookbehind: true,\n          inside: {\n            keyword:\n              /\\b(?:cmdextversion|defined|errorlevel|exist|not)\\b|^if\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: variable,\n            number: number,\n            operator: /\\^|==|\\b(?:equ|geq|gtr|leq|lss|neq)\\b/i\n          }\n        },\n        {\n          // ELSE command\n          pattern: /((?:^|[&()])[ \\t]*)else\\b/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^else\\b/i\n          }\n        },\n        {\n          // SET command\n          pattern:\n            /((?:^|[&(])[ \\t]*)set(?: \\/[a-z](?:[ :](?:\"[^\"]*\"|[^\\s\"/]\\S*))?)* (?:[^^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/im,\n          lookbehind: true,\n          inside: {\n            keyword: /^set\\b/i,\n            string: string,\n            parameter: parameter,\n            variable: [variable, /\\w+(?=(?:[*\\/%+\\-&^|]|<<|>>)?=)/],\n            number: number,\n            operator: /[*\\/%+\\-&^|]=?|<<=?|>>=?|[!~_=]/,\n            punctuation: /[()',]/\n          }\n        },\n        {\n          // Other commands\n          pattern:\n            /((?:^|[&(])[ \\t]*@?)\\w+\\b(?:\"(?:[\\\\\"]\"|[^\"])*\"(?!\")|[^\"^&)\\r\\n]|\\^(?:\\r\\n|[\\s\\S]))*/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^\\w+\\b/,\n            string: string,\n            parameter: parameter,\n            label: {\n              pattern: /(^\\s*):\\S+/m,\n              lookbehind: true,\n              alias: 'property'\n            },\n            variable: variable,\n            number: number,\n            operator: /\\^/\n          }\n        }\n      ],\n      operator: /[&@]/,\n      punctuation: /[()']/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/batch.js\n"));

/***/ })

}]);