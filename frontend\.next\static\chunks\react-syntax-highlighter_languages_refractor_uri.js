"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_uri"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uri.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uri.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = uri\nuri.displayName = 'uri'\nuri.aliases = ['url']\nfunction uri(Prism) {\n  // https://tools.ietf.org/html/rfc3986#appendix-A\n  Prism.languages.uri = {\n    scheme: {\n      pattern: /^[a-z][a-z0-9+.-]*:/im,\n      greedy: true,\n      inside: {\n        'scheme-delimiter': /:$/\n      }\n    },\n    fragment: {\n      pattern: /#[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'fragment-delimiter': /^#/\n      }\n    },\n    query: {\n      pattern: /\\?[\\w\\-.~!$&'()*+,;=%:@/?]*/,\n      inside: {\n        'query-delimiter': {\n          pattern: /^\\?/,\n          greedy: true\n        },\n        'pair-delimiter': /[&;]/,\n        pair: {\n          pattern: /^[^=][\\s\\S]*/,\n          inside: {\n            key: /^[^=]+/,\n            value: {\n              pattern: /(^=)[\\s\\S]+/,\n              lookbehind: true\n            }\n          }\n        }\n      }\n    },\n    authority: {\n      pattern: RegExp(\n        /^\\/\\//.source + // [ userinfo \"@\" ]\n          /(?:[\\w\\-.~!$&'()*+,;=%:]*@)?/.source + // host\n          ('(?:' + // IP-literal\n            /\\[(?:[0-9a-fA-F:.]{2,48}|v[0-9a-fA-F]+\\.[\\w\\-.~!$&'()*+,;=]+)\\]/\n              .source +\n            '|' + // IPv4address or registered name\n            /[\\w\\-.~!$&'()*+,;=%]*/.source +\n            ')') + // [ \":\" port ]\n          /(?::\\d*)?/.source,\n        'm'\n      ),\n      inside: {\n        'authority-delimiter': /^\\/\\//,\n        'user-info-segment': {\n          pattern: /^[\\w\\-.~!$&'()*+,;=%:]*@/,\n          inside: {\n            'user-info-delimiter': /@$/,\n            'user-info': /^[\\w\\-.~!$&'()*+,;=%:]+/\n          }\n        },\n        'port-segment': {\n          pattern: /:\\d*$/,\n          inside: {\n            'port-delimiter': /^:/,\n            port: /^\\d+/\n          }\n        },\n        host: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            'ip-literal': {\n              pattern: /^\\[[\\s\\S]+\\]$/,\n              inside: {\n                'ip-literal-delimiter': /^\\[|\\]$/,\n                'ipv-future': /^v[\\s\\S]+/,\n                'ipv6-address': /^[\\s\\S]+/\n              }\n            },\n            'ipv4-address':\n              /^(?:(?:[03-9]\\d?|[12]\\d{0,2})\\.){3}(?:[03-9]\\d?|[12]\\d{0,2})$/\n          }\n        }\n      }\n    },\n    path: {\n      pattern: /^[\\w\\-.~!$&'()*+,;=%:@/]+/m,\n      inside: {\n        'path-separator': /\\//\n      }\n    }\n  }\n  Prism.languages.url = Prism.languages.uri\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL3VyaS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBLGdDQUFnQyxLQUFLLGlDQUFpQztBQUN0RTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQSw0Q0FBNEM7QUFDNUM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsdUNBQXVDLElBQUksS0FBSyxFQUFFLG9CQUFvQixJQUFJO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFx1cmkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gdXJpXG51cmkuZGlzcGxheU5hbWUgPSAndXJpJ1xudXJpLmFsaWFzZXMgPSBbJ3VybCddXG5mdW5jdGlvbiB1cmkoUHJpc20pIHtcbiAgLy8gaHR0cHM6Ly90b29scy5pZXRmLm9yZy9odG1sL3JmYzM5ODYjYXBwZW5kaXgtQVxuICBQcmlzbS5sYW5ndWFnZXMudXJpID0ge1xuICAgIHNjaGVtZToge1xuICAgICAgcGF0dGVybjogL15bYS16XVthLXowLTkrLi1dKjovaW0sXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgJ3NjaGVtZS1kZWxpbWl0ZXInOiAvOiQvXG4gICAgICB9XG4gICAgfSxcbiAgICBmcmFnbWVudDoge1xuICAgICAgcGF0dGVybjogLyNbXFx3XFwtLn4hJCYnKCkqKyw7PSU6QC8/XSovLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgICdmcmFnbWVudC1kZWxpbWl0ZXInOiAvXiMvXG4gICAgICB9XG4gICAgfSxcbiAgICBxdWVyeToge1xuICAgICAgcGF0dGVybjogL1xcP1tcXHdcXC0ufiEkJicoKSorLDs9JTpALz9dKi8sXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgJ3F1ZXJ5LWRlbGltaXRlcic6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvXlxcPy8sXG4gICAgICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgICAgIH0sXG4gICAgICAgICdwYWlyLWRlbGltaXRlcic6IC9bJjtdLyxcbiAgICAgICAgcGFpcjoge1xuICAgICAgICAgIHBhdHRlcm46IC9eW149XVtcXHNcXFNdKi8sXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICBrZXk6IC9eW149XSsvLFxuICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgcGF0dGVybjogLyhePSlbXFxzXFxTXSsvLFxuICAgICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICBhdXRob3JpdHk6IHtcbiAgICAgIHBhdHRlcm46IFJlZ0V4cChcbiAgICAgICAgL15cXC9cXC8vLnNvdXJjZSArIC8vIFsgdXNlcmluZm8gXCJAXCIgXVxuICAgICAgICAgIC8oPzpbXFx3XFwtLn4hJCYnKCkqKyw7PSU6XSpAKT8vLnNvdXJjZSArIC8vIGhvc3RcbiAgICAgICAgICAoJyg/OicgKyAvLyBJUC1saXRlcmFsXG4gICAgICAgICAgICAvXFxbKD86WzAtOWEtZkEtRjouXXsyLDQ4fXx2WzAtOWEtZkEtRl0rXFwuW1xcd1xcLS5+ISQmJygpKissOz1dKylcXF0vXG4gICAgICAgICAgICAgIC5zb3VyY2UgK1xuICAgICAgICAgICAgJ3wnICsgLy8gSVB2NGFkZHJlc3Mgb3IgcmVnaXN0ZXJlZCBuYW1lXG4gICAgICAgICAgICAvW1xcd1xcLS5+ISQmJygpKissOz0lXSovLnNvdXJjZSArXG4gICAgICAgICAgICAnKScpICsgLy8gWyBcIjpcIiBwb3J0IF1cbiAgICAgICAgICAvKD86OlxcZCopPy8uc291cmNlLFxuICAgICAgICAnbSdcbiAgICAgICksXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgJ2F1dGhvcml0eS1kZWxpbWl0ZXInOiAvXlxcL1xcLy8sXG4gICAgICAgICd1c2VyLWluZm8tc2VnbWVudCc6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvXltcXHdcXC0ufiEkJicoKSorLDs9JTpdKkAvLFxuICAgICAgICAgIGluc2lkZToge1xuICAgICAgICAgICAgJ3VzZXItaW5mby1kZWxpbWl0ZXInOiAvQCQvLFxuICAgICAgICAgICAgJ3VzZXItaW5mbyc6IC9eW1xcd1xcLS5+ISQmJygpKissOz0lOl0rL1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgJ3BvcnQtc2VnbWVudCc6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvOlxcZCokLyxcbiAgICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICAgICdwb3J0LWRlbGltaXRlcic6IC9eOi8sXG4gICAgICAgICAgICBwb3J0OiAvXlxcZCsvXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBob3N0OiB7XG4gICAgICAgICAgcGF0dGVybjogL1tcXHNcXFNdKy8sXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICAnaXAtbGl0ZXJhbCc6IHtcbiAgICAgICAgICAgICAgcGF0dGVybjogL15cXFtbXFxzXFxTXStcXF0kLyxcbiAgICAgICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICAgICAgJ2lwLWxpdGVyYWwtZGVsaW1pdGVyJzogL15cXFt8XFxdJC8sXG4gICAgICAgICAgICAgICAgJ2lwdi1mdXR1cmUnOiAvXnZbXFxzXFxTXSsvLFxuICAgICAgICAgICAgICAgICdpcHY2LWFkZHJlc3MnOiAvXltcXHNcXFNdKy9cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICdpcHY0LWFkZHJlc3MnOlxuICAgICAgICAgICAgICAvXig/Oig/OlswMy05XVxcZD98WzEyXVxcZHswLDJ9KVxcLil7M30oPzpbMDMtOV1cXGQ/fFsxMl1cXGR7MCwyfSkkL1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgcGF0aDoge1xuICAgICAgcGF0dGVybjogL15bXFx3XFwtLn4hJCYnKCkqKyw7PSU6QC9dKy9tLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgICdwYXRoLXNlcGFyYXRvcic6IC9cXC8vXG4gICAgICB9XG4gICAgfVxuICB9XG4gIFByaXNtLmxhbmd1YWdlcy51cmwgPSBQcmlzbS5sYW5ndWFnZXMudXJpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uri.js\n"));

/***/ })

}]);