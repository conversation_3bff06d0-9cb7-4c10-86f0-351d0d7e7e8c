"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_toml"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/toml.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/toml.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = toml\ntoml.displayName = 'toml'\ntoml.aliases = []\nfunction toml(Prism) {\n  ;(function (Prism) {\n    var key = /(?:[\\w-]+|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/.source\n    /**\n     * @param {string} pattern\n     */\n    function insertKey(pattern) {\n      return pattern.replace(/__/g, function () {\n        return key\n      })\n    }\n    Prism.languages.toml = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      table: {\n        pattern: RegExp(\n          insertKey(\n            /(^[\\t ]*\\[\\s*(?:\\[\\s*)?)__(?:\\s*\\.\\s*__)*(?=\\s*\\])/.source\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name'\n      },\n      key: {\n        pattern: RegExp(\n          insertKey(/(^[\\t ]*|[{,]\\s*)__(?:\\s*\\.\\s*__)*(?=\\s*=)/.source),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'property'\n      },\n      string: {\n        pattern:\n          /\"\"\"(?:\\\\[\\s\\S]|[^\\\\])*?\"\"\"|'''[\\s\\S]*?'''|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      date: [\n        {\n          // Offset Date-Time, Local Date-Time, Local Date\n          pattern:\n            /\\b\\d{4}-\\d{2}-\\d{2}(?:[T\\s]\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:\\d{2})?)?\\b/i,\n          alias: 'number'\n        },\n        {\n          // Local Time\n          pattern: /\\b\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?\\b/,\n          alias: 'number'\n        }\n      ],\n      number:\n        /(?:\\b0(?:x[\\da-zA-Z]+(?:_[\\da-zA-Z]+)*|o[0-7]+(?:_[0-7]+)*|b[10]+(?:_[10]+)*))\\b|[-+]?\\b\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?\\b|[-+]?\\b(?:inf|nan)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /[.,=[\\]{}]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/toml.js\n"));

/***/ })

}]);