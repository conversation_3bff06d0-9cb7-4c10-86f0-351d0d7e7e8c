"use client";

import React, { useCallback, useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { 
  Upload, 
  File, 
  X, 
  AlertCircle,
  CheckCircle 
} from "lucide-react";
import { cn } from "@/lib/utils";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileLoad: (filePath: string) => void;
  className?: string;
  accept?: string;
  maxSize?: number; // in MB
}

export function FileUpload({
  onFileSelect,
  onFileLoad,
  className,
  accept = ".doc,.docx",
  maxSize = 10
}: FileUploadProps) {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>("");

  const validateFile = (file: File): boolean => {
    console.log("Validating file:", file.name, "Type:", file.type, "Size:", file.size);

    // 检查文件扩展名（更宽松的检查）
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.docx')) {
      setErrorMessage("仅支持 .docx 格式的Word文档");
      return false;
    }

    // 检查文件大小
    if (file.size > maxSize * 1024 * 1024) {
      setErrorMessage(`文件大小不能超过 ${maxSize}MB`);
      return false;
    }

    // 检查文件不为空
    if (file.size === 0) {
      setErrorMessage("文件不能为空");
      return false;
    }

    return true;
  };

  const handleFileSelect = useCallback((file: File) => {
    setErrorMessage("");

    if (!validateFile(file)) {
      setUploadStatus('error');
      return;
    }

    setSelectedFile(file);
    setUploadStatus('idle');
    onFileSelect(file);
  }, [onFileSelect, maxSize]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡到全局文件上传handler
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡到全局文件上传handler
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件冒泡到全局文件上传handler
    setDragOver(false);
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleUpload = async () => {
    if (!selectedFile) return;

    console.log('开始上传文件:', selectedFile.name, selectedFile.type, selectedFile.size);
    setUploadStatus('uploading');
    setErrorMessage("");

    try {
      // 创建FormData对象上传文件
      const formData = new FormData();
      formData.append('file', selectedFile);

      console.log('发送上传请求到 /api/word/upload');

      const response = await fetch('/api/word/upload', {
        method: 'POST',
        body: formData,
      });

      console.log('上传响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Upload response error:', response.status, errorText);
        throw new Error(`上传失败 (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('上传响应结果:', result);

      if (result.success) {
        setUploadStatus('success');
        // 上传成功后立即触发文档加载和分析
        onFileLoad(result.filePath || `/uploads/${selectedFile.name}`);
      } else {
        throw new Error(result.message || '上传失败');
      }

    } catch (error) {
      setUploadStatus('error');
      setErrorMessage(error instanceof Error ? error.message : "上传失败");
      console.error("Upload error:", error);
    }
  };

  const clearFile = () => {
    setSelectedFile(null);
    setUploadStatus('idle');
    setErrorMessage("");
  };

  // 文件选择后自动上传
  useEffect(() => {
    if (selectedFile && uploadStatus === 'idle') {
      handleUpload();
    }
  }, [selectedFile, uploadStatus]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* 拖拽上传区域 */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          dragOver ? "border-blue-500 bg-blue-50" : "border-gray-300",
          uploadStatus === 'error' && "border-red-300 bg-red-50"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="flex flex-col items-center space-y-2">
          <Upload className={cn(
            "h-8 w-8",
            dragOver ? "text-blue-500" : "text-gray-400"
          )} />
          <div>
            <p className="text-sm font-medium text-gray-900">
              拖拽Word文档到此处
            </p>
            <p className="text-xs text-gray-500">
              或点击选择文件
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="cursor-pointer"
            onClick={() => inputRef.current?.click()}
          >
            选择文件
          </Button>
          <Input
            id="word-file-input"
            type="file"
            // 更全面的 Word 文档过滤：同时包含扩展名与 MIME 类型
            accept={accept || ".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"}
            ref={inputRef}
            onChange={handleInputChange}
            className="hidden"
          />
        </div>
      </div>

      {/* 文件信息显示 */}
      {selectedFile && (
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <File className="h-5 w-5 text-blue-500" />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {selectedFile.name}
            </p>
            <p className="text-xs text-gray-500">
              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </p>
          </div>
          
          {uploadStatus === 'success' && (
            <CheckCircle className="h-5 w-5 text-green-500" />
          )}
          
          {uploadStatus === 'error' && (
            <AlertCircle className="h-5 w-5 text-red-500" />
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFile}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* 错误信息 */}
      {errorMessage && (
        <div className="flex items-center space-x-2 text-sm text-red-600">
          <AlertCircle className="h-4 w-4" />
          <span>{errorMessage}</span>
        </div>
      )}

      {/* 上传按钮 */}
      {selectedFile && uploadStatus !== 'success' && (
        <Button
          onClick={handleUpload}
          disabled={uploadStatus === 'uploading'}
          className="w-full"
        >
          {uploadStatus === 'uploading' ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              上传中...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              上传并分析文档
            </>
          )}
        </Button>
      )}

      {/* 成功状态 */}
      {uploadStatus === 'success' && (
        <div className="flex items-center space-x-2 text-sm text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span>文档上传成功，正在分析章节...</span>
        </div>
      )}
    </div>
  );
}

export default FileUpload;
