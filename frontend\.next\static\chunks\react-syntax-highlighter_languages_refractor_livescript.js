"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_livescript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/livescript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/livescript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = livescript\nlivescript.displayName = 'livescript'\nlivescript.aliases = []\nfunction livescript(Prism) {\n  Prism.languages.livescript = {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true\n      }\n    ],\n    'interpolated-string': {\n      /* Look-behind and look-ahead prevents wrong behavior of the greedy pattern\n       * forcing it to match \"\"\"-quoted string when it would otherwise match \"-quoted first. */\n      pattern: /(^|[^\"])(\"\"\"|\")(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2(?!\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /(^|[^\\\\])#[a-z_](?:-?[a-z]|[\\d_])*/m,\n          lookbehind: true\n        },\n        interpolation: {\n          pattern: /(^|[^\\\\])#\\{[^}]+\\}/m,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^#\\{|\\}$/,\n              alias: 'variable'\n            } // See rest below\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    string: [\n      {\n        pattern: /('''|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      {\n        pattern: /<\\[[\\s\\S]*?\\]>/,\n        greedy: true\n      },\n      /\\\\[^\\s,;\\])}]+/\n    ],\n    regex: [\n      {\n        pattern: /\\/\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|(?!\\/\\/)[^\\\\\\[])+\\/\\/[gimyu]{0,5}/,\n        greedy: true,\n        inside: {\n          comment: {\n            pattern: /(^|[^\\\\])#.*/,\n            lookbehind: true\n          }\n        }\n      },\n      {\n        pattern: /\\/(?:\\[[^\\r\\n\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/[gimyu]{0,5}/,\n        greedy: true\n      }\n    ],\n    keyword: {\n      pattern:\n        /(^|(?!-).)\\b(?:break|case|catch|class|const|continue|default|do|else|extends|fallthrough|finally|for(?: ever)?|function|if|implements|it|let|loop|new|null|otherwise|own|return|super|switch|that|then|this|throw|try|unless|until|var|void|when|while|yield)(?!-)\\b/m,\n      lookbehind: true\n    },\n    'keyword-operator': {\n      pattern:\n        /(^|[^-])\\b(?:(?:delete|require|typeof)!|(?:and|by|delete|export|from|import(?: all)?|in|instanceof|is(?: not|nt)?|not|of|or|til|to|typeof|with|xor)(?!-)\\b)/m,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    boolean: {\n      pattern: /(^|[^-])\\b(?:false|no|off|on|true|yes)(?!-)\\b/m,\n      lookbehind: true\n    },\n    argument: {\n      // Don't match .&. nor &&\n      pattern: /(^|(?!\\.&\\.)[^&])&(?!&)\\d*/m,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    number: /\\b(?:\\d+~[\\da-z]+|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[a-z]\\w*)?)/i,\n    identifier: /[a-z_](?:-?[a-z]|[\\d_])*/i,\n    operator: [\n      // Spaced .\n      {\n        pattern: /( )\\.(?= )/,\n        lookbehind: true\n      }, // Full list, in order:\n      // .= .~ .. ...\n      // .&. .^. .<<. .>>. .>>>.\n      // := :: ::=\n      // &&\n      // || |>\n      // < << <<< <<<<\n      // <- <-- <-! <--!\n      // <~ <~~ <~! <~~!\n      // <| <= <?\n      // > >> >= >?\n      // - -- -> -->\n      // + ++\n      // @ @@\n      // % %%\n      // * **\n      // ! != !~=\n      // !~> !~~>\n      // !-> !-->\n      // ~ ~> ~~> ~=\n      // = ==\n      // ^ ^^\n      // / ?\n      /\\.(?:[=~]|\\.\\.?)|\\.(?:[&|^]|<<|>>>?)\\.|:(?:=|:=?)|&&|\\|[|>]|<(?:<<?<?|--?!?|~~?!?|[|=?])?|>[>=?]?|-(?:->?|>)?|\\+\\+?|@@?|%%?|\\*\\*?|!(?:~?=|--?>|~?~>)?|~(?:~?>|=)?|==?|\\^\\^?|[\\/?]/\n    ],\n    punctuation: /[(){}\\[\\]|.,:;`]/\n  }\n  Prism.languages.livescript['interpolated-string'].inside[\n    'interpolation'\n  ].inside.rest = Prism.languages.livescript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/livescript.js\n"));

/***/ })

}]);