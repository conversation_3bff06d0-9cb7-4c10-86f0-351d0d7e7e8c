"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_lolcode"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/lolcode.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/lolcode.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = lolcode\nlolcode.displayName = 'lolcode'\nlolcode.aliases = []\nfunction lolcode(Prism) {\n  Prism.languages.lolcode = {\n    comment: [/\\bOBTW\\s[\\s\\S]*?\\sTLDR\\b/, /\\bBTW.+/],\n    string: {\n      pattern: /\"(?::.|[^\":])*\"/,\n      inside: {\n        variable: /:\\{[^}]+\\}/,\n        symbol: [/:\\([a-f\\d]+\\)/i, /:\\[[^\\]]+\\]/, /:[)>o\":]/]\n      },\n      greedy: true\n    },\n    number: /(?:\\B-)?(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)/,\n    symbol: {\n      pattern: /(^|\\s)(?:A )?(?:BUKKIT|NOOB|NUMBAR|NUMBR|TROOF|YARN)(?=\\s|,|$)/,\n      lookbehind: true,\n      inside: {\n        keyword: /A(?=\\s)/\n      }\n    },\n    label: {\n      pattern: /((?:^|\\s)(?:IM IN YR|IM OUTTA YR) )[a-zA-Z]\\w*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    function: {\n      pattern: /((?:^|\\s)(?:HOW IZ I|I IZ|IZ) )[a-zA-Z]\\w*/,\n      lookbehind: true\n    },\n    keyword: [\n      {\n        pattern:\n          /(^|\\s)(?:AN|FOUND YR|GIMMEH|GTFO|HAI|HAS A|HOW IZ I|I HAS A|I IZ|IF U SAY SO|IM IN YR|IM OUTTA YR|IS NOW(?: A)?|ITZ(?: A)?|IZ|KTHX|KTHXBYE|LIEK(?: A)?|MAEK|MEBBE|MKAY|NERFIN|NO WAI|O HAI IM|O RLY\\?|OIC|OMG|OMGWTF|R|SMOOSH|SRS|TIL|UPPIN|VISIBLE|WILE|WTF\\?|YA RLY|YR)(?=\\s|,|$)/,\n        lookbehind: true\n      },\n      /'Z(?=\\s|,|$)/\n    ],\n    boolean: {\n      pattern: /(^|\\s)(?:FAIL|WIN)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    variable: {\n      pattern: /(^|\\s)IT(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    operator: {\n      pattern:\n        /(^|\\s)(?:NOT|BOTH SAEM|DIFFRINT|(?:ALL|ANY|BIGGR|BOTH|DIFF|EITHER|MOD|PRODUKT|QUOSHUNT|SMALLR|SUM|WON) OF)(?=\\s|,|$)/,\n      lookbehind: true\n    },\n    punctuation: /\\.{3}|…|,|!/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/lolcode.js\n"));

/***/ })

}]);