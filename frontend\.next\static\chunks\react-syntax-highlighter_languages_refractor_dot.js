"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dot"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dot.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dot.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dot\ndot.displayName = 'dot'\ndot.aliases = ['gv']\nfunction dot(Prism) {\n  // https://www.graphviz.org/doc/info/lang.html\n  ;(function (Prism) {\n    var ID =\n      '(?:' +\n      [\n        // an identifier\n        /[a-zA-Z_\\x80-\\uFFFF][\\w\\x80-\\uFFFF]*/.source, // a number\n        /-?(?:\\.\\d+|\\d+(?:\\.\\d*)?)/.source, // a double-quoted string\n        /\"[^\"\\\\]*(?:\\\\[\\s\\S][^\"\\\\]*)*\"/.source, // HTML-like string\n        /<(?:[^<>]|(?!<!--)<(?:[^<>\"']|\"[^\"]*\"|'[^']*')+>|<!--(?:[^-]|-(?!->))*-->)*>/\n          .source\n      ].join('|') +\n      ')'\n    var IDInside = {\n      markup: {\n        pattern: /(^<)[\\s\\S]+(?=>$)/,\n        lookbehind: true,\n        alias: ['language-markup', 'language-html', 'language-xml'],\n        inside: Prism.languages.markup\n      }\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function withID(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return ID\n        }),\n        flags\n      )\n    }\n    Prism.languages.dot = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|^#.*/m,\n        greedy: true\n      },\n      'graph-name': {\n        pattern: withID(\n          /(\\b(?:digraph|graph|subgraph)[ \\t\\r\\n]+)<ID>/.source,\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name',\n        inside: IDInside\n      },\n      'attr-value': {\n        pattern: withID(/(=[ \\t\\r\\n]*)<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      'attr-name': {\n        pattern: withID(/([\\[;, \\t\\r\\n])<ID>(?=[ \\t\\r\\n]*=)/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      keyword: /\\b(?:digraph|edge|graph|node|strict|subgraph)\\b/i,\n      'compass-point': {\n        pattern: /(:[ \\t\\r\\n]*)(?:[ewc_]|[ns][ew]?)(?![\\w\\x80-\\uFFFF])/,\n        lookbehind: true,\n        alias: 'builtin'\n      },\n      node: {\n        pattern: withID(/(^|[^-.\\w\\x80-\\uFFFF\\\\])<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      operator: /[=:]|-[->]/,\n      punctuation: /[\\[\\]{};,]/\n    }\n    Prism.languages.gv = Prism.languages.dot\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dot.js\n"));

/***/ })

}]);