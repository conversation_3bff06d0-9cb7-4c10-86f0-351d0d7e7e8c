"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_xojo"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xojo.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xojo.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = xojo\nxojo.displayName = 'xojo'\nxojo.aliases = []\nfunction xojo(Prism) {\n  Prism.languages.xojo = {\n    comment: {\n      pattern: /(?:'|\\/\\/|Rem\\b).+/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    number: [/(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i, /&[bchou][a-z\\d]+/i],\n    directive: {\n      pattern: /#(?:Else|ElseIf|Endif|If|Pragma)\\b/i,\n      alias: 'property'\n    },\n    keyword:\n      /\\b(?:AddHandler|App|Array|As(?:signs)?|Auto|Boolean|Break|By(?:Ref|Val)|Byte|Call|Case|Catch|CFStringRef|CGFloat|Class|Color|Const|Continue|CString|Currency|CurrentMethodName|Declare|Delegate|Dim|Do(?:uble|wnTo)?|Each|Else(?:If)?|End|Enumeration|Event|Exception|Exit|Extends|False|Finally|For|Function|Get|GetTypeInfo|Global|GOTO|If|Implements|In|Inherits|Int(?:8|16|32|64|eger|erface)?|Lib|Loop|Me|Module|Next|Nil|Object|Optional|OSType|ParamArray|Private|Property|Protected|PString|Ptr|Raise(?:Event)?|ReDim|RemoveHandler|Return|Select(?:or)?|Self|Set|Shared|Short|Single|Soft|Static|Step|String|Sub|Super|Text|Then|To|True|Try|Ubound|UInt(?:8|16|32|64|eger)?|Until|Using|Var(?:iant)?|Wend|While|WindowPtr|WString)\\b/i,\n    operator:\n      /<[=>]?|>=?|[+\\-*\\/\\\\^=]|\\b(?:AddressOf|And|Ctype|IsA?|Mod|New|Not|Or|WeakAddressOf|Xor)\\b/i,\n    punctuation: /[.,;:()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xojo.js\n"));

/***/ })

}]);