"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_mongodb"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mongodb.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mongodb.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = mongodb\nmongodb.displayName = 'mongodb'\nmongodb.aliases = []\nfunction mongodb(Prism) {\n  ;(function (Prism) {\n    var operators = [\n      // query and projection\n      '$eq',\n      '$gt',\n      '$gte',\n      '$in',\n      '$lt',\n      '$lte',\n      '$ne',\n      '$nin',\n      '$and',\n      '$not',\n      '$nor',\n      '$or',\n      '$exists',\n      '$type',\n      '$expr',\n      '$jsonSchema',\n      '$mod',\n      '$regex',\n      '$text',\n      '$where',\n      '$geoIntersects',\n      '$geoWithin',\n      '$near',\n      '$nearSphere',\n      '$all',\n      '$elemMatch',\n      '$size',\n      '$bitsAllClear',\n      '$bitsAllSet',\n      '$bitsAnyClear',\n      '$bitsAnySet',\n      '$comment',\n      '$elemMatch',\n      '$meta',\n      '$slice', // update\n      '$currentDate',\n      '$inc',\n      '$min',\n      '$max',\n      '$mul',\n      '$rename',\n      '$set',\n      '$setOnInsert',\n      '$unset',\n      '$addToSet',\n      '$pop',\n      '$pull',\n      '$push',\n      '$pullAll',\n      '$each',\n      '$position',\n      '$slice',\n      '$sort',\n      '$bit', // aggregation pipeline stages\n      '$addFields',\n      '$bucket',\n      '$bucketAuto',\n      '$collStats',\n      '$count',\n      '$currentOp',\n      '$facet',\n      '$geoNear',\n      '$graphLookup',\n      '$group',\n      '$indexStats',\n      '$limit',\n      '$listLocalSessions',\n      '$listSessions',\n      '$lookup',\n      '$match',\n      '$merge',\n      '$out',\n      '$planCacheStats',\n      '$project',\n      '$redact',\n      '$replaceRoot',\n      '$replaceWith',\n      '$sample',\n      '$set',\n      '$skip',\n      '$sort',\n      '$sortByCount',\n      '$unionWith',\n      '$unset',\n      '$unwind',\n      '$setWindowFields', // aggregation pipeline operators\n      '$abs',\n      '$accumulator',\n      '$acos',\n      '$acosh',\n      '$add',\n      '$addToSet',\n      '$allElementsTrue',\n      '$and',\n      '$anyElementTrue',\n      '$arrayElemAt',\n      '$arrayToObject',\n      '$asin',\n      '$asinh',\n      '$atan',\n      '$atan2',\n      '$atanh',\n      '$avg',\n      '$binarySize',\n      '$bsonSize',\n      '$ceil',\n      '$cmp',\n      '$concat',\n      '$concatArrays',\n      '$cond',\n      '$convert',\n      '$cos',\n      '$dateFromParts',\n      '$dateToParts',\n      '$dateFromString',\n      '$dateToString',\n      '$dayOfMonth',\n      '$dayOfWeek',\n      '$dayOfYear',\n      '$degreesToRadians',\n      '$divide',\n      '$eq',\n      '$exp',\n      '$filter',\n      '$first',\n      '$floor',\n      '$function',\n      '$gt',\n      '$gte',\n      '$hour',\n      '$ifNull',\n      '$in',\n      '$indexOfArray',\n      '$indexOfBytes',\n      '$indexOfCP',\n      '$isArray',\n      '$isNumber',\n      '$isoDayOfWeek',\n      '$isoWeek',\n      '$isoWeekYear',\n      '$last',\n      '$last',\n      '$let',\n      '$literal',\n      '$ln',\n      '$log',\n      '$log10',\n      '$lt',\n      '$lte',\n      '$ltrim',\n      '$map',\n      '$max',\n      '$mergeObjects',\n      '$meta',\n      '$min',\n      '$millisecond',\n      '$minute',\n      '$mod',\n      '$month',\n      '$multiply',\n      '$ne',\n      '$not',\n      '$objectToArray',\n      '$or',\n      '$pow',\n      '$push',\n      '$radiansToDegrees',\n      '$range',\n      '$reduce',\n      '$regexFind',\n      '$regexFindAll',\n      '$regexMatch',\n      '$replaceOne',\n      '$replaceAll',\n      '$reverseArray',\n      '$round',\n      '$rtrim',\n      '$second',\n      '$setDifference',\n      '$setEquals',\n      '$setIntersection',\n      '$setIsSubset',\n      '$setUnion',\n      '$size',\n      '$sin',\n      '$slice',\n      '$split',\n      '$sqrt',\n      '$stdDevPop',\n      '$stdDevSamp',\n      '$strcasecmp',\n      '$strLenBytes',\n      '$strLenCP',\n      '$substr',\n      '$substrBytes',\n      '$substrCP',\n      '$subtract',\n      '$sum',\n      '$switch',\n      '$tan',\n      '$toBool',\n      '$toDate',\n      '$toDecimal',\n      '$toDouble',\n      '$toInt',\n      '$toLong',\n      '$toObjectId',\n      '$toString',\n      '$toLower',\n      '$toUpper',\n      '$trim',\n      '$trunc',\n      '$type',\n      '$week',\n      '$year',\n      '$zip',\n      '$count',\n      '$dateAdd',\n      '$dateDiff',\n      '$dateSubtract',\n      '$dateTrunc',\n      '$getField',\n      '$rand',\n      '$sampleRate',\n      '$setField',\n      '$unsetField', // aggregation pipeline query modifiers\n      '$comment',\n      '$explain',\n      '$hint',\n      '$max',\n      '$maxTimeMS',\n      '$min',\n      '$orderby',\n      '$query',\n      '$returnKey',\n      '$showDiskLoc',\n      '$natural'\n    ]\n    var builtinFunctions = [\n      'ObjectId',\n      'Code',\n      'BinData',\n      'DBRef',\n      'Timestamp',\n      'NumberLong',\n      'NumberDecimal',\n      'MaxKey',\n      'MinKey',\n      'RegExp',\n      'ISODate',\n      'UUID'\n    ]\n    operators = operators.map(function (operator) {\n      return operator.replace('$', '\\\\$')\n    })\n    var operatorsSource = '(?:' + operators.join('|') + ')\\\\b'\n    Prism.languages.mongodb = Prism.languages.extend('javascript', {})\n    Prism.languages.insertBefore('mongodb', 'string', {\n      property: {\n        pattern:\n          /(?:([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)(?=\\s*:)/,\n        greedy: true,\n        inside: {\n          keyword: RegExp('^([\\'\"])?' + operatorsSource + '(?:\\\\1)?$')\n        }\n      }\n    })\n    Prism.languages.mongodb.string.inside = {\n      url: {\n        // url pattern\n        pattern:\n          /https?:\\/\\/[-\\w@:%.+~#=]{1,256}\\.[a-z0-9()]{1,6}\\b[-\\w()@:%+.~#?&/=]*/i,\n        greedy: true\n      },\n      entity: {\n        // ipv4\n        pattern:\n          /\\b(?:(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\b/,\n        greedy: true\n      }\n    }\n    Prism.languages.insertBefore('mongodb', 'constant', {\n      builtin: {\n        pattern: RegExp('\\\\b(?:' + builtinFunctions.join('|') + ')\\\\b'),\n        alias: 'keyword'\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mongodb.js\n"));

/***/ })

}]);