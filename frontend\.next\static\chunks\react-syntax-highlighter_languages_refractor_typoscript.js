"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_typoscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/typoscript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/typoscript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = typoscript\ntyposcript.displayName = 'typoscript'\ntyposcript.aliases = ['tsconfig']\nfunction typoscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:ACT|ACTIFSUB|CARRAY|CASE|CLEARGIF|COA|COA_INT|CONSTANTS|CONTENT|CUR|EDITPANEL|EFFECT|EXT|FILE|FLUIDTEMPLATE|FORM|FRAME|FRAMESET|GIFBUILDER|GMENU|GMENU_FOLDOUT|GMENU_LAYERS|GP|HMENU|HRULER|HTML|IENV|IFSUB|IMAGE|IMGMENU|IMGMENUITEM|IMGTEXT|IMG_RESOURCE|INCLUDE_TYPOSCRIPT|JSMENU|JSMENUITEM|LLL|LOAD_REGISTER|NO|PAGE|RECORDS|RESTORE_REGISTER|TEMPLATE|TEXT|TMENU|TMENUITEM|TMENU_LAYERS|USER|USER_INT|_GIFBUILDER|global|globalString|globalVar)\\b/\n    Prism.languages.typoscript = {\n      comment: [\n        {\n          // multiline comments /* */\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true\n        },\n        {\n          // double-slash comments - ignored when backslashes or colon is found in front\n          // also ignored whenever directly after an equal-sign, because it would probably be an url without protocol\n          pattern: /(^|[^\\\\:= \\t]|(?:^|[^= \\t])[ \\t]+)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // hash comments - ignored when leading quote is found for hex colors in strings\n          pattern: /(^|[^\"'])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      function: [\n        {\n          // old include style\n          pattern:\n            /<INCLUDE_TYPOSCRIPT:\\s*source\\s*=\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*>/,\n          inside: {\n            string: {\n              pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n              inside: {\n                keyword: keywords\n              }\n            },\n            keyword: {\n              pattern: /INCLUDE_TYPOSCRIPT/\n            }\n          }\n        },\n        {\n          // new include style\n          pattern: /@import\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')/,\n          inside: {\n            string: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/\n          }\n        }\n      ],\n      string: {\n        pattern: /^([^=]*=[< ]?)(?:(?!\\]\\n).)*/,\n        lookbehind: true,\n        inside: {\n          function: /\\{\\$.*\\}/,\n          // constants include\n          keyword: keywords,\n          number: /^\\d+$/,\n          punctuation: /[,|:]/\n        }\n      },\n      keyword: keywords,\n      number: {\n        // special highlighting for indexes of arrays in tags\n        pattern: /\\b\\d+\\s*[.{=]/,\n        inside: {\n          operator: /[.{=]/\n        }\n      },\n      tag: {\n        pattern: /\\.?[-\\w\\\\]+\\.?/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      punctuation: /[{}[\\];(),.:|]/,\n      operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/\n    }\n    Prism.languages.tsconfig = Prism.languages.typoscript\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/typoscript.js\n"));

/***/ })

}]);