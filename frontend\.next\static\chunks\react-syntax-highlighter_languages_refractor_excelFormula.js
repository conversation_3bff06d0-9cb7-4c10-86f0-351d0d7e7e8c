"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_excelFormula"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/excel-formula.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/excel-formula.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = excelFormula\nexcelFormula.displayName = 'excelFormula'\nexcelFormula.aliases = []\nfunction excelFormula(Prism) {\n  Prism.languages['excel-formula'] = {\n    comment: {\n      pattern: /(\\bN\\(\\s*)\"(?:[^\"]|\"\")*\"(?=\\s*\\))/i,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    reference: {\n      // https://www.ablebits.com/office-addins-blog/2015/12/08/excel-reference-another-sheet-workbook/\n      // Sales!B2\n      // 'Winter sales'!B2\n      // [Sales.xlsx]Jan!B2:B5\n      // D:\\Reports\\[Sales.xlsx]Jan!B2:B5\n      // '[Sales.xlsx]Jan sales'!B2:B5\n      // 'D:\\Reports\\[Sales.xlsx]Jan sales'!B2:B5\n      pattern:\n        /(?:'[^']*'|(?:[^\\s()[\\]{}<>*?\"';,$&]*\\[[^^\\s()[\\]{}<>*?\"']+\\])?\\w+)!/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        operator: /!$/,\n        punctuation: /'/,\n        sheet: {\n          pattern: /[^[\\]]+$/,\n          alias: 'function'\n        },\n        file: {\n          pattern: /\\[[^[\\]]+\\]$/,\n          inside: {\n            punctuation: /[[\\]]/\n          }\n        },\n        path: /[\\s\\S]+/\n      }\n    },\n    'function-name': {\n      pattern: /\\b[A-Z]\\w*(?=\\()/i,\n      alias: 'keyword'\n    },\n    range: {\n      pattern:\n        /\\$?\\b(?:[A-Z]+\\$?\\d+:\\$?[A-Z]+\\$?\\d+|[A-Z]+:\\$?[A-Z]+|\\d+:\\$?\\d+)\\b/i,\n      alias: 'property',\n      inside: {\n        operator: /:/,\n        cell: /\\$?[A-Z]+\\$?\\d+/i,\n        column: /\\$?[A-Z]+/i,\n        row: /\\$?\\d+/\n      }\n    },\n    cell: {\n      // Excel is case insensitive, so the string \"foo1\" could be either a variable or a cell.\n      // To combat this, we match cells case insensitive, if the contain at least one \"$\", and case sensitive otherwise.\n      pattern: /\\b[A-Z]+\\d+\\b|\\$[A-Za-z]+\\$?\\d+\\b|\\b[A-Za-z]+\\$\\d+\\b/,\n      alias: 'property'\n    },\n    number: /(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:FALSE|TRUE)\\b/i,\n    operator: /[-+*/^%=&,]|<[=>]?|>=?/,\n    punctuation: /[[\\]();{}|]/\n  }\n  Prism.languages['xlsx'] = Prism.languages['xls'] =\n    Prism.languages['excel-formula']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2V4Y2VsLWZvcm11bGEuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsT0FBTyxtQkFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGV4Y2VsLWZvcm11bGEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gZXhjZWxGb3JtdWxhXG5leGNlbEZvcm11bGEuZGlzcGxheU5hbWUgPSAnZXhjZWxGb3JtdWxhJ1xuZXhjZWxGb3JtdWxhLmFsaWFzZXMgPSBbXVxuZnVuY3Rpb24gZXhjZWxGb3JtdWxhKFByaXNtKSB7XG4gIFByaXNtLmxhbmd1YWdlc1snZXhjZWwtZm9ybXVsYSddID0ge1xuICAgIGNvbW1lbnQ6IHtcbiAgICAgIHBhdHRlcm46IC8oXFxiTlxcKFxccyopXCIoPzpbXlwiXXxcIlwiKSpcIig/PVxccypcXCkpL2ksXG4gICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBzdHJpbmc6IHtcbiAgICAgIHBhdHRlcm46IC9cIig/OlteXCJdfFwiXCIpKlwiKD8hXCIpLyxcbiAgICAgIGdyZWVkeTogdHJ1ZVxuICAgIH0sXG4gICAgcmVmZXJlbmNlOiB7XG4gICAgICAvLyBodHRwczovL3d3dy5hYmxlYml0cy5jb20vb2ZmaWNlLWFkZGlucy1ibG9nLzIwMTUvMTIvMDgvZXhjZWwtcmVmZXJlbmNlLWFub3RoZXItc2hlZXQtd29ya2Jvb2svXG4gICAgICAvLyBTYWxlcyFCMlxuICAgICAgLy8gJ1dpbnRlciBzYWxlcychQjJcbiAgICAgIC8vIFtTYWxlcy54bHN4XUphbiFCMjpCNVxuICAgICAgLy8gRDpcXFJlcG9ydHNcXFtTYWxlcy54bHN4XUphbiFCMjpCNVxuICAgICAgLy8gJ1tTYWxlcy54bHN4XUphbiBzYWxlcychQjI6QjVcbiAgICAgIC8vICdEOlxcUmVwb3J0c1xcW1NhbGVzLnhsc3hdSmFuIHNhbGVzJyFCMjpCNVxuICAgICAgcGF0dGVybjpcbiAgICAgICAgLyg/OidbXiddKid8KD86W15cXHMoKVtcXF17fTw+Kj9cIic7LCQmXSpcXFtbXl5cXHMoKVtcXF17fTw+Kj9cIiddK1xcXSk/XFx3KykhLyxcbiAgICAgIGdyZWVkeTogdHJ1ZSxcbiAgICAgIGFsaWFzOiAnc3RyaW5nJyxcbiAgICAgIGluc2lkZToge1xuICAgICAgICBvcGVyYXRvcjogLyEkLyxcbiAgICAgICAgcHVuY3R1YXRpb246IC8nLyxcbiAgICAgICAgc2hlZXQ6IHtcbiAgICAgICAgICBwYXR0ZXJuOiAvW15bXFxdXSskLyxcbiAgICAgICAgICBhbGlhczogJ2Z1bmN0aW9uJ1xuICAgICAgICB9LFxuICAgICAgICBmaWxlOiB7XG4gICAgICAgICAgcGF0dGVybjogL1xcW1teW1xcXV0rXFxdJC8sXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICBwdW5jdHVhdGlvbjogL1tbXFxdXS9cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHBhdGg6IC9bXFxzXFxTXSsvXG4gICAgICB9XG4gICAgfSxcbiAgICAnZnVuY3Rpb24tbmFtZSc6IHtcbiAgICAgIHBhdHRlcm46IC9cXGJbQS1aXVxcdyooPz1cXCgpL2ksXG4gICAgICBhbGlhczogJ2tleXdvcmQnXG4gICAgfSxcbiAgICByYW5nZToge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgL1xcJD9cXGIoPzpbQS1aXStcXCQ/XFxkKzpcXCQ/W0EtWl0rXFwkP1xcZCt8W0EtWl0rOlxcJD9bQS1aXSt8XFxkKzpcXCQ/XFxkKylcXGIvaSxcbiAgICAgIGFsaWFzOiAncHJvcGVydHknLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgIG9wZXJhdG9yOiAvOi8sXG4gICAgICAgIGNlbGw6IC9cXCQ/W0EtWl0rXFwkP1xcZCsvaSxcbiAgICAgICAgY29sdW1uOiAvXFwkP1tBLVpdKy9pLFxuICAgICAgICByb3c6IC9cXCQ/XFxkKy9cbiAgICAgIH1cbiAgICB9LFxuICAgIGNlbGw6IHtcbiAgICAgIC8vIEV4Y2VsIGlzIGNhc2UgaW5zZW5zaXRpdmUsIHNvIHRoZSBzdHJpbmcgXCJmb28xXCIgY291bGQgYmUgZWl0aGVyIGEgdmFyaWFibGUgb3IgYSBjZWxsLlxuICAgICAgLy8gVG8gY29tYmF0IHRoaXMsIHdlIG1hdGNoIGNlbGxzIGNhc2UgaW5zZW5zaXRpdmUsIGlmIHRoZSBjb250YWluIGF0IGxlYXN0IG9uZSBcIiRcIiwgYW5kIGNhc2Ugc2Vuc2l0aXZlIG90aGVyd2lzZS5cbiAgICAgIHBhdHRlcm46IC9cXGJbQS1aXStcXGQrXFxifFxcJFtBLVphLXpdK1xcJD9cXGQrXFxifFxcYltBLVphLXpdK1xcJFxcZCtcXGIvLFxuICAgICAgYWxpYXM6ICdwcm9wZXJ0eSdcbiAgICB9LFxuICAgIG51bWJlcjogLyg/OlxcYlxcZCsoPzpcXC5cXGQrKT98XFxCXFwuXFxkKykoPzplWystXT9cXGQrKT9cXGIvaSxcbiAgICBib29sZWFuOiAvXFxiKD86RkFMU0V8VFJVRSlcXGIvaSxcbiAgICBvcGVyYXRvcjogL1stKyovXiU9JixdfDxbPT5dP3w+PT8vLFxuICAgIHB1bmN0dWF0aW9uOiAvW1tcXF0oKTt7fXxdL1xuICB9XG4gIFByaXNtLmxhbmd1YWdlc1sneGxzeCddID0gUHJpc20ubGFuZ3VhZ2VzWyd4bHMnXSA9XG4gICAgUHJpc20ubGFuZ3VhZ2VzWydleGNlbC1mb3JtdWxhJ11cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/excel-formula.js\n"));

/***/ })

}]);