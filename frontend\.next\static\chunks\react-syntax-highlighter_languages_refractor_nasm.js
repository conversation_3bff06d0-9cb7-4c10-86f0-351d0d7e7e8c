"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nasm"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nasm.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nasm.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nasm\nnasm.displayName = 'nasm'\nnasm.aliases = []\nfunction nasm(Prism) {\n  Prism.languages.nasm = {\n    comment: /;.*$/m,\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    label: {\n      pattern: /(^\\s*)[A-Za-z._?$][\\w.?$@~#]*:/m,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword: [\n      /\\[?BITS (?:16|32|64)\\]?/,\n      {\n        pattern: /(^\\s*)section\\s*[a-z.]+:?/im,\n        lookbehind: true\n      },\n      /(?:extern|global)[^;\\r\\n]*/i,\n      /(?:CPU|DEFAULT|FLOAT).*$/m\n    ],\n    register: {\n      pattern:\n        /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s)\\b/i,\n      alias: 'variable'\n    },\n    number:\n      /(?:\\b|(?=\\$))(?:0[hx](?:\\.[\\da-f]+|[\\da-f]+(?:\\.[\\da-f]+)?)(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n    operator: /[\\[\\]*+\\-\\/%<>=&|$!]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nasm.js\n"));

/***/ })

}]);