"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_visualBasic"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/visual-basic.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/visual-basic.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = visualBasic\nvisualBasic.displayName = 'visualBasic'\nvisualBasic.aliases = []\nfunction visualBasic(Prism) {\n  Prism.languages['visual-basic'] = {\n    comment: {\n      pattern: /(?:['‘’]|REM\\b)(?:[^\\r\\n_]|_(?:\\r\\n?|\\n)?)*/i,\n      inside: {\n        keyword: /^REM/i\n      }\n    },\n    directive: {\n      pattern:\n        /#(?:Const|Else|ElseIf|End|ExternalChecksum|ExternalSource|If|Region)(?:\\b_[ \\t]*(?:\\r\\n?|\\n)|.)+/i,\n      alias: 'property',\n      greedy: true\n    },\n    string: {\n      pattern: /\\$?[\"“”](?:[\"“”]{2}|[^\"“”])*[\"“”]C?/i,\n      greedy: true\n    },\n    date: {\n      pattern:\n        /#[ \\t]*(?:\\d+([/-])\\d+\\1\\d+(?:[ \\t]+(?:\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?))?|\\d+[ \\t]*(?:AM|PM)|\\d+:\\d+(?::\\d+)?(?:[ \\t]*(?:AM|PM))?)[ \\t]*#/i,\n      alias: 'number'\n    },\n    number:\n      /(?:(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)(?:E[+-]?\\d+)?|&[HO][\\dA-F]+)(?:[FRD]|U?[ILS])?/i,\n    boolean: /\\b(?:False|Nothing|True)\\b/i,\n    keyword:\n      /\\b(?:AddHandler|AddressOf|Alias|And(?:Also)?|As|Boolean|ByRef|Byte|ByVal|Call|Case|Catch|C(?:Bool|Byte|Char|Date|Dbl|Dec|Int|Lng|Obj|SByte|Short|Sng|Str|Type|UInt|ULng|UShort)|Char|Class|Const|Continue|Currency|Date|Decimal|Declare|Default|Delegate|Dim|DirectCast|Do|Double|Each|Else(?:If)?|End(?:If)?|Enum|Erase|Error|Event|Exit|Finally|For|Friend|Function|Get(?:Type|XMLNamespace)?|Global|GoSub|GoTo|Handles|If|Implements|Imports|In|Inherits|Integer|Interface|Is|IsNot|Let|Lib|Like|Long|Loop|Me|Mod|Module|Must(?:Inherit|Override)|My(?:Base|Class)|Namespace|Narrowing|New|Next|Not(?:Inheritable|Overridable)?|Object|Of|On|Operator|Option(?:al)?|Or(?:Else)?|Out|Overloads|Overridable|Overrides|ParamArray|Partial|Private|Property|Protected|Public|RaiseEvent|ReadOnly|ReDim|RemoveHandler|Resume|Return|SByte|Select|Set|Shadows|Shared|short|Single|Static|Step|Stop|String|Structure|Sub|SyncLock|Then|Throw|To|Try|TryCast|Type|TypeOf|U(?:Integer|Long|Short)|Until|Using|Variant|Wend|When|While|Widening|With(?:Events)?|WriteOnly|Xor)\\b/i,\n    operator: /[+\\-*/\\\\^<=>&#@$%!]|\\b_(?=[ \\t]*[\\r\\n])/,\n    punctuation: /[{}().,:?]/\n  }\n  Prism.languages.vb = Prism.languages['visual-basic']\n  Prism.languages.vba = Prism.languages['visual-basic']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/visual-basic.js\n"));

/***/ })

}]);