"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dhall"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dhall.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dhall.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dhall\ndhall.displayName = 'dhall'\ndhall.aliases = []\nfunction dhall(Prism) {\n  // ABNF grammar:\n  // https://github.com/dhall-lang/dhall-lang/blob/master/standard/dhall.abnf\n  Prism.languages.dhall = {\n    // Multi-line comments can be nested. E.g. {- foo {- bar -} -}\n    // The multi-line pattern is essentially this:\n    //   \\{-(?:[^-{]|-(?!\\})|\\{(?!-)|<SELF>)*-\\}\n    comment:\n      /--.*|\\{-(?:[^-{]|-(?!\\})|\\{(?!-)|\\{-(?:[^-{]|-(?!\\})|\\{(?!-))*-\\})*-\\}/,\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"|''(?:[^']|'(?!')|'''|''\\$\\{)*''(?!'|\\$)/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              alias: 'language-dhall',\n              inside: null // see blow\n            },\n            punctuation: /\\$\\{|\\}/\n          }\n        }\n      }\n    },\n    label: {\n      pattern: /`[^`]*`/,\n      greedy: true\n    },\n    url: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L596\n      pattern:\n        /\\bhttps?:\\/\\/[\\w.:%!$&'*+;=@~-]+(?:\\/[\\w.:%!$&'*+;=@~-]*)*(?:\\?[/?\\w.:%!$&'*+;=@~-]*)?/,\n      greedy: true\n    },\n    env: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L661\n      pattern: /\\benv:(?:(?!\\d)\\w+|\"(?:[^\"\\\\=]|\\\\.)*\")/,\n      greedy: true,\n      inside: {\n        function: /^env/,\n        operator: /^:/,\n        variable: /[\\s\\S]+/\n      }\n    },\n    hash: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L725\n      pattern: /\\bsha256:[\\da-fA-F]{64}\\b/,\n      inside: {\n        function: /sha256/,\n        operator: /:/,\n        number: /[\\da-fA-F]{64}/\n      }\n    },\n    // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L359\n    keyword:\n      /\\b(?:as|assert|else|forall|if|in|let|merge|missing|then|toMap|using|with)\\b|\\u2200/,\n    builtin: /\\b(?:None|Some)\\b/,\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /\\bNaN\\b|-?\\bInfinity\\b|[+-]?\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/,\n    operator:\n      /\\/\\\\|\\/\\/\\\\\\\\|&&|\\|\\||===|[!=]=|\\/\\/|->|\\+\\+|::|[+*#@=:?<>|\\\\\\u2227\\u2a53\\u2261\\u2afd\\u03bb\\u2192]/,\n    punctuation: /\\.\\.|[{}\\[\\](),./]/,\n    // we'll just assume that every capital word left is a type name\n    'class-name': /\\b[A-Z]\\w*\\b/\n  }\n  Prism.languages.dhall.string.inside.interpolation.inside.expression.inside =\n    Prism.languages.dhall\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dhall.js\n"));

/***/ })

}]);