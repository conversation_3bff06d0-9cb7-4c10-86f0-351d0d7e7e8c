"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_matlab"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/matlab.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/matlab.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = matlab\nmatlab.displayName = 'matlab'\nmatlab.aliases = []\nfunction matlab(Prism) {\n  Prism.languages.matlab = {\n    comment: [/%\\{[\\s\\S]*?\\}%/, /%.+/],\n    string: {\n      pattern: /\\B'(?:''|[^'\\r\\n])*'/,\n      greedy: true\n    },\n    // FIXME We could handle imaginary numbers as a whole\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?(?:[ij])?|\\b[ij]\\b/,\n    keyword:\n      /\\b(?:NaN|break|case|catch|continue|else|elseif|end|for|function|if|inf|otherwise|parfor|pause|pi|return|switch|try|while)\\b/,\n    function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n    operator: /\\.?[*^\\/\\\\']|[+\\-:@]|[<>=~]=?|&&?|\\|\\|?/,\n    punctuation: /\\.{3}|[.,;\\[\\](){}!]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL21hdGxhYi5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFVBQVU7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixFQUFFLEtBQUssUUFBUTtBQUNwQztBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXG1hdGxhYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBtYXRsYWJcbm1hdGxhYi5kaXNwbGF5TmFtZSA9ICdtYXRsYWInXG5tYXRsYWIuYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBtYXRsYWIoUHJpc20pIHtcbiAgUHJpc20ubGFuZ3VhZ2VzLm1hdGxhYiA9IHtcbiAgICBjb21tZW50OiBbLyVcXHtbXFxzXFxTXSo/XFx9JS8sIC8lLisvXSxcbiAgICBzdHJpbmc6IHtcbiAgICAgIHBhdHRlcm46IC9cXEInKD86Jyd8W14nXFxyXFxuXSkqJy8sXG4gICAgICBncmVlZHk6IHRydWVcbiAgICB9LFxuICAgIC8vIEZJWE1FIFdlIGNvdWxkIGhhbmRsZSBpbWFnaW5hcnkgbnVtYmVycyBhcyBhIHdob2xlXG4gICAgbnVtYmVyOiAvKD86XFxiXFxkKyg/OlxcLlxcZCopP3xcXEJcXC5cXGQrKSg/OltlRV1bKy1dP1xcZCspPyg/Oltpal0pP3xcXGJbaWpdXFxiLyxcbiAgICBrZXl3b3JkOlxuICAgICAgL1xcYig/Ok5hTnxicmVha3xjYXNlfGNhdGNofGNvbnRpbnVlfGVsc2V8ZWxzZWlmfGVuZHxmb3J8ZnVuY3Rpb258aWZ8aW5mfG90aGVyd2lzZXxwYXJmb3J8cGF1c2V8cGl8cmV0dXJufHN3aXRjaHx0cnl8d2hpbGUpXFxiLyxcbiAgICBmdW5jdGlvbjogL1xcYig/IVxcZClcXHcrKD89XFxzKlxcKCkvLFxuICAgIG9wZXJhdG9yOiAvXFwuP1sqXlxcL1xcXFwnXXxbK1xcLTpAXXxbPD49fl09P3wmJj98XFx8XFx8Py8sXG4gICAgcHVuY3R1YXRpb246IC9cXC57M318Wy4sO1xcW1xcXSgpe30hXS9cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/matlab.js\n"));

/***/ })

}]);