"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_uorazor"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uorazor.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uorazor.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = uorazor\nuorazor.displayName = 'uorazor'\nuorazor.aliases = []\nfunction uorazor(Prism) {\n  Prism.languages.uorazor = {\n    'comment-hash': {\n      pattern: /#.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    'comment-slash': {\n      pattern: /\\/\\/.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      },\n      greedy: true\n    },\n    'source-layers': {\n      pattern:\n        /\\b(?:arms|backpack|blue|bracelet|cancel|clear|cloak|criminal|earrings|enemy|facialhair|friend|friendly|gloves|gray|grey|ground|hair|head|innerlegs|innertorso|innocent|lefthand|middletorso|murderer|neck|nonfriendly|onehandedsecondary|outerlegs|outertorso|pants|red|righthand|ring|self|shirt|shoes|talisman|waist)\\b/i,\n      alias: 'function'\n    },\n    'source-commands': {\n      pattern:\n        /\\b(?:alliance|attack|cast|clearall|clearignore|clearjournal|clearlist|clearsysmsg|createlist|createtimer|dclick|dclicktype|dclickvar|dress|dressconfig|drop|droprelloc|emote|getlabel|guild|gumpclose|gumpresponse|hotkey|ignore|lasttarget|lift|lifttype|menu|menuresponse|msg|org|organize|organizer|overhead|pause|poplist|potion|promptresponse|pushlist|removelist|removetimer|rename|restock|say|scav|scavenger|script|setability|setlasttarget|setskill|settimer|setvar|sysmsg|target|targetloc|targetrelloc|targettype|undress|unignore|unsetvar|useobject|useonce|useskill|usetype|virtue|wait|waitforgump|waitformenu|waitforprompt|waitforstat|waitforsysmsg|waitfortarget|walk|wfsysmsg|wft|whisper|yell)\\b/,\n      alias: 'function'\n    },\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    function:\n      /\\b(?:atlist|close|closest|count|counter|counttype|dead|dex|diffhits|diffmana|diffstam|diffweight|find|findbuff|finddebuff|findlayer|findtype|findtypelist|followers|gumpexists|hidden|hits|hp|hue|human|humanoid|ingump|inlist|insysmessage|insysmsg|int|invul|lhandempty|list|listexists|mana|maxhits|maxhp|maxmana|maxstam|maxweight|monster|mounted|name|next|noto|paralyzed|poisoned|position|prev|previous|queued|rand|random|rhandempty|skill|stam|str|targetexists|timer|timerexists|varexist|warmode|weight)\\b/,\n    keyword:\n      /\\b(?:and|as|break|continue|else|elseif|endfor|endif|endwhile|for|if|loop|not|or|replay|stop|while)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [\n      {\n        pattern:\n          /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n        lookbehind: true\n      },\n      /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/\n    ],\n    punctuation: /[()\\[\\]{}:.,]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/uorazor.js\n"));

/***/ })

}]);