"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_scheme"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scheme.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scheme.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = scheme\nscheme.displayName = 'scheme'\nscheme.aliases = []\nfunction scheme(Prism) {\n  ;(function (Prism) {\n    Prism.languages.scheme = {\n      // this supports \"normal\" single-line comments:\n      //   ; comment\n      // and (potentially nested) multiline comments:\n      //   #| comment #| nested |# still comment |#\n      // (only 1 level of nesting is supported)\n      comment:\n        /;.*|#;\\s*(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\[(?:[^\\[\\]]|\\[[^\\[\\]]*\\])*\\])|#\\|(?:[^#|]|#(?!\\|)|\\|(?!#)|#\\|(?:[^#|]|#(?!\\|)|\\|(?!#))*\\|#)*\\|#/,\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      symbol: {\n        pattern: /'[^()\\[\\]#'\\s]+/,\n        greedy: true\n      },\n      char: {\n        pattern:\n          /#\\\\(?:[ux][a-fA-F\\d]+\\b|[-a-zA-Z]+\\b|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|\\S)/,\n        greedy: true\n      },\n      'lambda-parameter': [\n        // https://www.cs.cmu.edu/Groups/AI/html/r4rs/r4rs_6.html#SEC30\n        {\n          pattern:\n            /((?:^|[^'`#])[(\\[]lambda\\s+)(?:[^|()\\[\\]'\\s]+|\\|(?:[^\\\\|]|\\\\.)*\\|)/,\n          lookbehind: true\n        },\n        {\n          pattern: /((?:^|[^'`#])[(\\[]lambda\\s+[(\\[])[^()\\[\\]']+/,\n          lookbehind: true\n        }\n      ],\n      keyword: {\n        pattern:\n          /((?:^|[^'`#])[(\\[])(?:begin|case(?:-lambda)?|cond(?:-expand)?|define(?:-library|-macro|-record-type|-syntax|-values)?|defmacro|delay(?:-force)?|do|else|except|export|guard|if|import|include(?:-ci|-library-declarations)?|lambda|let(?:rec)?(?:-syntax|-values|\\*)?|let\\*-values|only|parameterize|prefix|(?:quasi-?)?quote|rename|set!|syntax-(?:case|rules)|unless|unquote(?:-splicing)?|when)(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true\n      },\n      builtin: {\n        // all functions of the base library of R7RS plus some of built-ins of R5Rs\n        pattern:\n          /((?:^|[^'`#])[(\\[])(?:abs|and|append|apply|assoc|ass[qv]|binary-port\\?|boolean=?\\?|bytevector(?:-append|-copy|-copy!|-length|-u8-ref|-u8-set!|\\?)?|caar|cadr|call-with-(?:current-continuation|port|values)|call\\/cc|car|cdar|cddr|cdr|ceiling|char(?:->integer|-ready\\?|\\?|<\\?|<=\\?|=\\?|>\\?|>=\\?)|close-(?:input-port|output-port|port)|complex\\?|cons|current-(?:error|input|output)-port|denominator|dynamic-wind|eof-object\\??|eq\\?|equal\\?|eqv\\?|error|error-object(?:-irritants|-message|\\?)|eval|even\\?|exact(?:-integer-sqrt|-integer\\?|\\?)?|expt|features|file-error\\?|floor(?:-quotient|-remainder|\\/)?|flush-output-port|for-each|gcd|get-output-(?:bytevector|string)|inexact\\??|input-port(?:-open\\?|\\?)|integer(?:->char|\\?)|lcm|length|list(?:->string|->vector|-copy|-ref|-set!|-tail|\\?)?|make-(?:bytevector|list|parameter|string|vector)|map|max|member|memq|memv|min|modulo|negative\\?|newline|not|null\\?|number(?:->string|\\?)|numerator|odd\\?|open-(?:input|output)-(?:bytevector|string)|or|output-port(?:-open\\?|\\?)|pair\\?|peek-char|peek-u8|port\\?|positive\\?|procedure\\?|quotient|raise|raise-continuable|rational\\?|rationalize|read-(?:bytevector|bytevector!|char|error\\?|line|string|u8)|real\\?|remainder|reverse|round|set-c[ad]r!|square|string(?:->list|->number|->symbol|->utf8|->vector|-append|-copy|-copy!|-fill!|-for-each|-length|-map|-ref|-set!|\\?|<\\?|<=\\?|=\\?|>\\?|>=\\?)?|substring|symbol(?:->string|\\?|=\\?)|syntax-error|textual-port\\?|truncate(?:-quotient|-remainder|\\/)?|u8-ready\\?|utf8->string|values|vector(?:->list|->string|-append|-copy|-copy!|-fill!|-for-each|-length|-map|-ref|-set!|\\?)?|with-exception-handler|write-(?:bytevector|char|string|u8)|zero\\?)(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true\n      },\n      operator: {\n        pattern: /((?:^|[^'`#])[(\\[])(?:[-+*%/]|[<>]=?|=>?)(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true\n      },\n      number: {\n        // The number pattern from [the R7RS spec](https://small.r7rs.org/attachment/r7rs.pdf).\n        //\n        // <number>      := <num 2>|<num 8>|<num 10>|<num 16>\n        // <num R>       := <prefix R><complex R>\n        // <complex R>   := <real R>(?:@<real R>|<imaginary R>)?|<imaginary R>\n        // <imaginary R> := [+-](?:<ureal R>|(?:inf|nan)\\.0)?i\n        // <real R>      := [+-]?<ureal R>|[+-](?:inf|nan)\\.0\n        // <ureal R>     := <uint R>(?:\\/<uint R>)?\n        //                | <decimal R>\n        //\n        // <decimal 10>  := (?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?\n        // <uint R>      := <digit R>+\n        // <prefix R>    := <radix R>(?:#[ei])?|(?:#[ei])?<radix R>\n        // <radix 2>     := #b\n        // <radix 8>     := #o\n        // <radix 10>    := (?:#d)?\n        // <radix 16>    := #x\n        // <digit 2>     := [01]\n        // <digit 8>     := [0-7]\n        // <digit 10>    := \\d\n        // <digit 16>    := [0-9a-f]\n        //\n        // The problem with this grammar is that the resulting regex is way to complex, so we simplify by grouping all\n        // non-decimal bases together. This results in a decimal (dec) and combined binary, octal, and hexadecimal (box)\n        // pattern:\n        pattern: RegExp(\n          SortedBNF({\n            '<ureal dec>':\n              /\\d+(?:\\/\\d+)|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[esfdl][+-]?\\d+)?/.source,\n            '<real dec>': /[+-]?<ureal dec>|[+-](?:inf|nan)\\.0/.source,\n            '<imaginary dec>': /[+-](?:<ureal dec>|(?:inf|nan)\\.0)?i/.source,\n            '<complex dec>':\n              /<real dec>(?:@<real dec>|<imaginary dec>)?|<imaginary dec>/\n                .source,\n            '<num dec>': /(?:#d(?:#[ei])?|#[ei](?:#d)?)?<complex dec>/.source,\n            '<ureal box>': /[0-9a-f]+(?:\\/[0-9a-f]+)?/.source,\n            '<real box>': /[+-]?<ureal box>|[+-](?:inf|nan)\\.0/.source,\n            '<imaginary box>': /[+-](?:<ureal box>|(?:inf|nan)\\.0)?i/.source,\n            '<complex box>':\n              /<real box>(?:@<real box>|<imaginary box>)?|<imaginary box>/\n                .source,\n            '<num box>': /#[box](?:#[ei])?|(?:#[ei])?#[box]<complex box>/\n              .source,\n            '<number>': /(^|[()\\[\\]\\s])(?:<num dec>|<num box>)(?=[()\\[\\]\\s]|$)/\n              .source\n          }),\n          'i'\n        ),\n        lookbehind: true\n      },\n      boolean: {\n        pattern: /(^|[()\\[\\]\\s])#(?:[ft]|false|true)(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true\n      },\n      function: {\n        pattern:\n          /((?:^|[^'`#])[(\\[])(?:[^|()\\[\\]'\\s]+|\\|(?:[^\\\\|]|\\\\.)*\\|)(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true\n      },\n      identifier: {\n        pattern: /(^|[()\\[\\]\\s])\\|(?:[^\\\\|]|\\\\.)*\\|(?=[()\\[\\]\\s]|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      punctuation: /[()\\[\\]']/\n    }\n    /**\n     * Given a topologically sorted BNF grammar, this will return the RegExp source of last rule of the grammar.\n     *\n     * @param {Record<string, string>} grammar\n     * @returns {string}\n     */\n    function SortedBNF(grammar) {\n      for (var key in grammar) {\n        grammar[key] = grammar[key].replace(/<[\\w\\s]+>/g, function (key) {\n          return '(?:' + grammar[key].trim() + ')'\n        })\n      } // return the last item\n      return grammar[key]\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scheme.js\n"));

/***/ })

}]);