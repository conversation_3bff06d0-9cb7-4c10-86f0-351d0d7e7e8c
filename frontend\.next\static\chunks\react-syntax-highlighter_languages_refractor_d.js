"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_d"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/d.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/d.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = d\nd.displayName = 'd'\nd.aliases = []\nfunction d(Prism) {\n  Prism.languages.d = Prism.languages.extend('clike', {\n    comment: [\n      {\n        // Shebang\n        pattern: /^\\s*#!.+/,\n        greedy: true\n      },\n      {\n        pattern: RegExp(\n          /(^|[^\\\\])/.source +\n            '(?:' +\n            [\n              // /+ comment +/\n              // Allow one level of nesting\n              /\\/\\+(?:\\/\\+(?:[^+]|\\+(?!\\/))*\\+\\/|(?!\\/\\+)[\\s\\S])*?\\+\\//.source, // // comment\n              /\\/\\/.*/.source, // /* comment */\n              /\\/\\*[\\s\\S]*?\\*\\//.source\n            ].join('|') +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: [\n      {\n        pattern: RegExp(\n          [\n            // r\"\", x\"\"\n            /\\b[rx]\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"[cwd]?/.source, // q\"[]\", q\"()\", q\"<>\", q\"{}\"\n            /\\bq\"(?:\\[[\\s\\S]*?\\]|\\([\\s\\S]*?\\)|<[\\s\\S]*?>|\\{[\\s\\S]*?\\})\"/.source, // q\"IDENT\n            // ...\n            // IDENT\"\n            /\\bq\"((?!\\d)\\w+)$[\\s\\S]*?^\\1\"/.source, // q\"//\", q\"||\", etc.\n            // eslint-disable-next-line regexp/strict\n            /\\bq\"(.)[\\s\\S]*?\\2\"/.source, // eslint-disable-next-line regexp/strict\n            /([\"`])(?:\\\\[\\s\\S]|(?!\\3)[^\\\\])*\\3[cwd]?/.source\n          ].join('|'),\n          'm'\n        ),\n        greedy: true\n      },\n      {\n        pattern: /\\bq\\{(?:\\{[^{}]*\\}|[^{}])*\\}/,\n        greedy: true,\n        alias: 'token-string'\n      }\n    ],\n    // In order: $, keywords and special tokens, globally defined symbols\n    keyword:\n      /\\$|\\b(?:__(?:(?:DATE|EOF|FILE|FUNCTION|LINE|MODULE|PRETTY_FUNCTION|TIMESTAMP|TIME|VENDOR|VERSION)__|gshared|parameters|traits|vector)|abstract|alias|align|asm|assert|auto|body|bool|break|byte|case|cast|catch|cdouble|cent|cfloat|char|class|const|continue|creal|dchar|debug|default|delegate|delete|deprecated|do|double|dstring|else|enum|export|extern|false|final|finally|float|for|foreach|foreach_reverse|function|goto|idouble|if|ifloat|immutable|import|inout|int|interface|invariant|ireal|lazy|long|macro|mixin|module|new|nothrow|null|out|override|package|pragma|private|protected|ptrdiff_t|public|pure|real|ref|return|scope|shared|short|size_t|static|string|struct|super|switch|synchronized|template|this|throw|true|try|typedef|typeid|typeof|ubyte|ucent|uint|ulong|union|unittest|ushort|version|void|volatile|wchar|while|with|wstring)\\b/,\n    number: [\n      // The lookbehind and the negative look-ahead try to prevent bad highlighting of the .. operator\n      // Hexadecimal numbers must be handled separately to avoid problems with exponent \"e\"\n      /\\b0x\\.?[a-f\\d_]+(?:(?!\\.\\.)\\.[a-f\\d_]*)?(?:p[+-]?[a-f\\d_]+)?[ulfi]{0,4}/i,\n      {\n        pattern:\n          /((?:\\.\\.)?)(?:\\b0b\\.?|\\b|\\.)\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?[ulfi]{0,4}/i,\n        lookbehind: true\n      }\n    ],\n    operator:\n      /\\|[|=]?|&[&=]?|\\+[+=]?|-[-=]?|\\.?\\.\\.|=[>=]?|!(?:i[ns]\\b|<>?=?|>=?|=)?|\\bi[ns]\\b|(?:<[<>]?|>>?>?|\\^\\^|[*\\/%^~])=?/\n  })\n  Prism.languages.insertBefore('d', 'string', {\n    // Characters\n    // 'a', '\\\\', '\\n', '\\xFF', '\\377', '\\uFFFF', '\\U0010FFFF', '\\quot'\n    char: /'(?:\\\\(?:\\W|\\w+)|[^\\\\])'/\n  })\n  Prism.languages.insertBefore('d', 'keyword', {\n    property: /\\B@\\w*/\n  })\n  Prism.languages.insertBefore('d', 'function', {\n    register: {\n      // Iasm registers\n      pattern:\n        /\\b(?:[ABCD][LHX]|E?(?:BP|DI|SI|SP)|[BS]PL|[ECSDGF]S|CR[0234]|[DS]IL|DR[012367]|E[ABCD]X|X?MM[0-7]|R(?:1[0-5]|[89])[BWD]?|R[ABCD]X|R[BS]P|R[DS]I|TR[3-7]|XMM(?:1[0-5]|[89])|YMM(?:1[0-5]|\\d))\\b|\\bST(?:\\([0-7]\\)|\\b)/,\n      alias: 'variable'\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/d.js\n"));

/***/ })

}]);