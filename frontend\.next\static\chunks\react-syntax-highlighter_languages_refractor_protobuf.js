"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_protobuf"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/protobuf.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/protobuf.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = protobuf\nprotobuf.displayName = 'protobuf'\nprotobuf.aliases = []\nfunction protobuf(Prism) {\n  ;(function (Prism) {\n    var builtinTypes =\n      /\\b(?:bool|bytes|double|s?fixed(?:32|64)|float|[su]?int(?:32|64)|string)\\b/\n    Prism.languages.protobuf = Prism.languages.extend('clike', {\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:enum|extend|message|service)\\s+)[A-Za-z_]\\w*(?=\\s*\\{)/,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\b(?:rpc\\s+\\w+|returns)\\s*\\(\\s*(?:stream\\s+)?)\\.?[A-Za-z_]\\w*(?:\\.[A-Za-z_]\\w*)*(?=\\s*\\))/,\n          lookbehind: true\n        }\n      ],\n      keyword:\n        /\\b(?:enum|extend|extensions|import|message|oneof|option|optional|package|public|repeated|required|reserved|returns|rpc(?=\\s+\\w)|service|stream|syntax|to)\\b(?!\\s*=\\s*\\d)/,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i\n    })\n    Prism.languages.insertBefore('protobuf', 'operator', {\n      map: {\n        pattern: /\\bmap<\\s*[\\w.]+\\s*,\\s*[\\w.]+\\s*>(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /[<>.,]/,\n          builtin: builtinTypes\n        }\n      },\n      builtin: builtinTypes,\n      'positional-class-name': {\n        pattern: /(?:\\b|\\B\\.)[a-z_]\\w*(?:\\.[a-z_]\\w*)*(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      annotation: {\n        pattern: /(\\[\\s*)[a-z_]\\w*(?=\\s*=)/i,\n        lookbehind: true\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/protobuf.js\n"));

/***/ })

}]);