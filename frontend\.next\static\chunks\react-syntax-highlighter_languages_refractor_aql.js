"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_aql"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/aql.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/aql.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = aql\naql.displayName = 'aql'\naql.aliases = []\nfunction aql(Prism) {\n  Prism.languages.aql = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    property: {\n      pattern:\n        /([{,]\\s*)(?:(?!\\d)\\w+|([\"'´`])(?:(?!\\2)[^\\\\\\r\\n]|\\\\.)*\\2)(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    identifier: {\n      pattern: /([´`])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    variable: /@@?\\w+/,\n    keyword: [\n      {\n        pattern: /(\\bWITH\\s+)COUNT(?=\\s+INTO\\b)/i,\n        lookbehind: true\n      },\n      /\\b(?:AGGREGATE|ALL|AND|ANY|ASC|COLLECT|DESC|DISTINCT|FILTER|FOR|GRAPH|IN|INBOUND|INSERT|INTO|K_PATHS|K_SHORTEST_PATHS|LET|LIKE|LIMIT|NONE|NOT|NULL|OR|OUTBOUND|REMOVE|REPLACE|RETURN|SHORTEST_PATH|SORT|UPDATE|UPSERT|WINDOW|WITH)\\b/i, // pseudo keywords get a lookbehind to avoid false positives\n      {\n        pattern: /(^|[^\\w.[])(?:KEEP|PRUNE|SEARCH|TO)\\b/i,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\w.[])(?:CURRENT|NEW|OLD)\\b/,\n        lookbehind: true\n      },\n      {\n        pattern: /\\bOPTIONS(?=\\s*\\{)/i\n      }\n    ],\n    function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n    boolean: /\\b(?:false|true)\\b/i,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    number: [\n      /\\b0b[01]+/i,\n      /\\b0x[0-9a-f]+/i,\n      /(?:\\B\\.\\d+|\\b(?:0|[1-9]\\d*)(?:\\.\\d+)?)(?:e[+-]?\\d+)?/i\n    ],\n    operator: /\\*{2,}|[=!]~|[!=<>]=?|&&|\\|\\||[-+*/%]/,\n    punctuation: /::|[?.:,;()[\\]{}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/aql.js\n"));

/***/ })

}]);