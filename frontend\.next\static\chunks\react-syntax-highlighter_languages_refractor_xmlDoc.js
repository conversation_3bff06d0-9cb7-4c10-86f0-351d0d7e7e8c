"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_xmlDoc"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xml-doc.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xml-doc.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = xmlDoc\nxmlDoc.displayName = 'xmlDoc'\nxmlDoc.aliases = []\nfunction xmlDoc(Prism) {\n  ;(function (Prism) {\n    /**\n     * If the given language is present, it will insert the given doc comment grammar token into it.\n     *\n     * @param {string} lang\n     * @param {any} docComment\n     */\n    function insertDocComment(lang, docComment) {\n      if (Prism.languages[lang]) {\n        Prism.languages.insertBefore(lang, 'comment', {\n          'doc-comment': docComment\n        })\n      }\n    }\n    var tag = Prism.languages.markup.tag\n    var slashDocComment = {\n      pattern: /\\/\\/\\/.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    var tickDocComment = {\n      pattern: /'''.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    insertDocComment('csharp', slashDocComment)\n    insertDocComment('fsharp', slashDocComment)\n    insertDocComment('vbnet', tickDocComment)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xml-doc.js\n"));

/***/ })

}]);