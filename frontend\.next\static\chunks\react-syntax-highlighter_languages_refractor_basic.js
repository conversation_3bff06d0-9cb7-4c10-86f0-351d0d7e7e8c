"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_basic"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/basic.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/basic.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = basic\nbasic.displayName = 'basic'\nbasic.aliases = []\nfunction basic(Prism) {\n  Prism.languages.basic = {\n    comment: {\n      pattern: /(?:!|REM\\b).+/i,\n      inside: {\n        keyword: /^REM/i\n      }\n    },\n    string: {\n      pattern: /\"(?:\"\"|[!#$%&'()*,\\/:;<=>?^\\w +\\-.])*\"/,\n      greedy: true\n    },\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    keyword:\n      /\\b(?:AS|BEEP|BLOAD|BSAVE|CALL(?: ABSOLUTE)?|CASE|CHAIN|CHDIR|CLEAR|CLOSE|CLS|COM|COMMON|CONST|DATA|DECLARE|DEF(?: FN| SEG|DBL|INT|LNG|SNG|STR)|DIM|DO|DOUBLE|ELSE|ELSEIF|END|ENVIRON|ERASE|ERROR|EXIT|FIELD|FILES|FOR|FUNCTION|GET|GOSUB|GOTO|IF|INPUT|INTEGER|IOCTL|KEY|KILL|LINE INPUT|LOCATE|LOCK|LONG|LOOP|LSET|MKDIR|NAME|NEXT|OFF|ON(?: COM| ERROR| KEY| TIMER)?|OPEN|OPTION BASE|OUT|POKE|PUT|READ|REDIM|REM|RESTORE|RESUME|RETURN|RMDIR|RSET|RUN|SELECT CASE|SHARED|SHELL|SINGLE|SLEEP|STATIC|STEP|STOP|STRING|SUB|SWAP|SYSTEM|THEN|TIMER|TO|TROFF|TRON|TYPE|UNLOCK|UNTIL|USING|VIEW PRINT|WAIT|WEND|WHILE|WRITE)(?:\\$|\\b)/i,\n    function:\n      /\\b(?:ABS|ACCESS|ACOS|ANGLE|AREA|ARITHMETIC|ARRAY|ASIN|ASK|AT|ATN|BASE|BEGIN|BREAK|CAUSE|CEIL|CHR|CLIP|COLLATE|COLOR|CON|COS|COSH|COT|CSC|DATE|DATUM|DEBUG|DECIMAL|DEF|DEG|DEGREES|DELETE|DET|DEVICE|DISPLAY|DOT|ELAPSED|EPS|ERASABLE|EXLINE|EXP|EXTERNAL|EXTYPE|FILETYPE|FIXED|FP|GO|GRAPH|HANDLER|IDN|IMAGE|IN|INT|INTERNAL|IP|IS|KEYED|LBOUND|LCASE|LEFT|LEN|LENGTH|LET|LINE|LINES|LOG|LOG10|LOG2|LTRIM|MARGIN|MAT|MAX|MAXNUM|MID|MIN|MISSING|MOD|NATIVE|NUL|NUMERIC|OF|OPTION|ORD|ORGANIZATION|OUTIN|OUTPUT|PI|POINT|POINTER|POINTS|POS|PRINT|PROGRAM|PROMPT|RAD|RADIANS|RANDOMIZE|RECORD|RECSIZE|RECTYPE|RELATIVE|REMAINDER|REPEAT|REST|RETRY|REWRITE|RIGHT|RND|ROUND|RTRIM|SAME|SEC|SELECT|SEQUENTIAL|SET|SETTER|SGN|SIN|SINH|SIZE|SKIP|SQR|STANDARD|STATUS|STR|STREAM|STYLE|TAB|TAN|TANH|TEMPLATE|TEXT|THERE|TIME|TIMEOUT|TRACE|TRANSFORM|TRUNCATE|UBOUND|UCASE|USE|VAL|VARIABLE|VIEWPORT|WHEN|WINDOW|WITH|ZER|ZONEWIDTH)(?:\\$|\\b)/i,\n    operator: /<[=>]?|>=?|[+\\-*\\/^=&]|\\b(?:AND|EQV|IMP|NOT|OR|XOR)\\b/i,\n    punctuation: /[,;:()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/basic.js\n"));

/***/ })

}]);