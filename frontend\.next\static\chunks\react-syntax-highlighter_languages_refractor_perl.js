"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_perl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/perl.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/perl.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = perl\nperl.displayName = 'perl'\nperl.aliases = []\nfunction perl(Prism) {\n  ;(function (Prism) {\n    var brackets =\n      /(?:\\((?:[^()\\\\]|\\\\[\\s\\S])*\\)|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\}|\\[(?:[^[\\]\\\\]|\\\\[\\s\\S])*\\]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)/\n        .source\n    Prism.languages.perl = {\n      comment: [\n        {\n          // POD\n          pattern: /(^\\s*)=\\w[\\s\\S]*?=cut.*/m,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // TODO Could be nice to handle Heredoc too.\n      string: [\n        {\n          pattern: RegExp(\n            /\\b(?:q|qq|qw|qx)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // q/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // q a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // q(...)\n                // q{...}\n                // q[...]\n                // q<...>\n                brackets\n              ].join('|') +\n              ')'\n          ),\n          greedy: true\n        }, // \"...\", `...`\n        {\n          pattern: /(\"|`)(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/,\n          greedy: true\n        }, // '...'\n        // FIXME Multi-line single-quoted strings are not supported as they would break variables containing '\n        {\n          pattern: /'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n          greedy: true\n        }\n      ],\n      regex: [\n        {\n          pattern: RegExp(\n            /\\b(?:m|qr)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // m/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // m a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // m(...)\n                // m{...}\n                // m[...]\n                // m<...>\n                brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngc]*/.source\n          ),\n          greedy: true\n        }, // The lookbehinds prevent -s from breaking\n        {\n          pattern: RegExp(\n            /(^|[^-])\\b(?:s|tr|y)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // s/.../.../\n                // eslint-disable-next-line regexp/strict\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/\n                  .source, // s a...a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3/\n                  .source, // s(...)(...)\n                // s{...}{...}\n                // s[...][...]\n                // s<...><...>\n                // s(...)[...]\n                brackets + /\\s*/.source + brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngcer]*/.source\n          ),\n          lookbehind: true,\n          greedy: true\n        }, // /.../\n        // The look-ahead tries to prevent two divisions on\n        // the same line from being highlighted as regex.\n        // This does not support multi-line regex.\n        {\n          pattern:\n            /\\/(?:[^\\/\\\\\\r\\n]|\\\\.)*\\/[msixpodualngc]*(?=\\s*(?:$|[\\r\\n,.;})&|\\-+*~<>!?^]|(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|x|xor)\\b))/,\n          greedy: true\n        }\n      ],\n      // FIXME Not sure about the handling of ::, ', and #\n      variable: [\n        // ${^POSTMATCH}\n        /[&*$@%]\\{\\^[A-Z]+\\}/, // $^V\n        /[&*$@%]\\^[A-Z_]/, // ${...}\n        /[&*$@%]#?(?=\\{)/, // $foo\n        /[&*$@%]#?(?:(?:::)*'?(?!\\d)[\\w$]+(?![\\w$]))+(?:::)*/, // $1\n        /[&*$@%]\\d+/, // $_, @_, %!\n        // The negative lookahead prevents from breaking the %= operator\n        /(?!%=)[$@%][!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~]/\n      ],\n      filehandle: {\n        // <>, <FOO>, _\n        pattern: /<(?![<=])\\S*?>|\\b_\\b/,\n        alias: 'symbol'\n      },\n      'v-string': {\n        // v1.2, 1.2.3\n        pattern: /v\\d+(?:\\.\\d+)*|\\d+(?:\\.\\d+){2,}/,\n        alias: 'string'\n      },\n      function: {\n        pattern: /(\\bsub[ \\t]+)\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while)\\b/,\n      number:\n        /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)\\b/,\n      operator:\n        /-[rwxoRWXOezsfdlpSbctugkTBMAC]\\b|\\+[+=]?|-[-=>]?|\\*\\*?=?|\\/\\/?=?|=[=~>]?|~[~=]?|\\|\\|?=?|&&?=?|<(?:=>?|<=?)?|>>?=?|![~=]?|[%^]=?|\\.(?:=|\\.\\.?)?|[\\\\?]|\\bx(?:=|\\b)|\\b(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|xor)\\b/,\n      punctuation: /[{}[\\];(),:]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/perl.js\n"));

/***/ })

}]);