"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.2_77fd30315bd1adbf71ffcb3d3c7056c6";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.2_77fd30315bd1adbf71ffcb3d3c7056c6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_77fd30315bd1adbf71ffcb3d3c7056c6/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_77fd30315bd1adbf71ffcb3d3c7056c6/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2de78f3af8662305a585be516d601297/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_f1c66a21136a6a7eef779b85a6d8e78b/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_6fa579b84aec5e61a5b40d10bb671609/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._d5459adb46993a50615f22c9197e0a71/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._e792b184a2ca1dccf9b400aadd0fc7f8/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._0d0cd24fc19ea4534d390a6089f37aea/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.2_@types+react@19.1.4_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_aba99c23fedbfe0aa9cdb40a73f261a1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipProvider.useEffect\": ()=>{\n            const skipDelayTimer = skipDelayTimerRef.current;\n            return ({\n                \"TooltipProvider.useEffect\": ()=>window.clearTimeout(skipDelayTimer)\n            })[\"TooltipProvider.useEffect\"];\n        }\n    }[\"TooltipProvider.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                isOpenDelayedRef.current = false;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout({\n                    \"TooltipProvider.useCallback\": ()=>isOpenDelayedRef.current = true\n                }[\"TooltipProvider.useCallback\"], skipDelayDuration);\n            }\n        }[\"TooltipProvider.useCallback\"], [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": (inTransit)=>{\n                isPointerInTransitRef.current = inTransit;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: {\n            \"Tooltip.useControllableState\": (open2)=>{\n                if (open2) {\n                    providerContext.onOpen();\n                    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n                } else {\n                    providerContext.onClose();\n                }\n                onOpenChange?.(open2);\n            }\n        }[\"Tooltip.useControllableState\"],\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Tooltip.useMemo[stateAttribute]\": ()=>{\n            return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n        }\n    }[\"Tooltip.useMemo[stateAttribute]\"], [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            wasOpenDelayedRef.current = false;\n            setOpen(true);\n        }\n    }[\"Tooltip.useCallback[handleOpen]\"], [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleClose]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            setOpen(false);\n        }\n    }[\"Tooltip.useCallback[handleClose]\"], [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = window.setTimeout({\n                \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n                    wasOpenDelayedRef.current = true;\n                    setOpen(true);\n                    openTimerRef.current = 0;\n                }\n            }[\"Tooltip.useCallback[handleDelayedOpen]\"], delayDuration);\n        }\n    }[\"Tooltip.useCallback[handleDelayedOpen]\"], [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    if (openTimerRef.current) {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                    else handleOpen();\n                }\n            }[\"Tooltip.useCallback\"], [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (disableHoverableContent) {\n                        handleClose();\n                    } else {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            }[\"Tooltip.useCallback\"], [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipTrigger.useCallback[handlePointerUp]\": ()=>isPointerDownRef.current = false\n    }[\"TooltipTrigger.useCallback[handlePointerUp]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipTrigger.useEffect\": ()=>{\n            return ({\n                \"TooltipTrigger.useEffect\": ()=>document.removeEventListener(\"pointerup\", handlePointerUp)\n            })[\"TooltipTrigger.useEffect\"];\n        }\n    }[\"TooltipTrigger.useEffect\"], [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\": ()=>{\n            setPointerGraceArea(null);\n            onPointerInTransitChange(false);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleCreateGraceArea]\": (event, hoverTarget)=>{\n            const currentTarget = event.currentTarget;\n            const exitPoint = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n            const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n            const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n            const graceArea = getHull([\n                ...paddedExitPoints,\n                ...hoverTargetPoints\n            ]);\n            setPointerGraceArea(graceArea);\n            onPointerInTransitChange(true);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleCreateGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            return ({\n                \"TooltipContentHoverable.useEffect\": ()=>handleRemoveGraceArea()\n            })[\"TooltipContentHoverable.useEffect\"];\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (trigger && content) {\n                const handleTriggerLeave = {\n                    \"TooltipContentHoverable.useEffect.handleTriggerLeave\": (event)=>handleCreateGraceArea(event, content)\n                }[\"TooltipContentHoverable.useEffect.handleTriggerLeave\"];\n                const handleContentLeave = {\n                    \"TooltipContentHoverable.useEffect.handleContentLeave\": (event)=>handleCreateGraceArea(event, trigger)\n                }[\"TooltipContentHoverable.useEffect.handleContentLeave\"];\n                trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n                content.addEventListener(\"pointerleave\", handleContentLeave);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>{\n                        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                        content.removeEventListener(\"pointerleave\", handleContentLeave);\n                    }\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (pointerGraceArea) {\n                const handleTrackPointerGrace = {\n                    \"TooltipContentHoverable.useEffect.handleTrackPointerGrace\": (event)=>{\n                        const target = event.target;\n                        const pointerPosition = {\n                            x: event.clientX,\n                            y: event.clientY\n                        };\n                        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                        if (hasEnteredTarget) {\n                            handleRemoveGraceArea();\n                        } else if (isPointerOutsideGraceArea) {\n                            handleRemoveGraceArea();\n                            onClose();\n                        }\n                    }\n                }[\"TooltipContentHoverable.useEffect.handleTrackPointerGrace\"];\n                document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace)\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            document.addEventListener(TOOLTIP_OPEN, onClose);\n            return ({\n                \"TooltipContentImpl.useEffect\": ()=>document.removeEventListener(TOOLTIP_OPEN, onClose)\n            })[\"TooltipContentImpl.useEffect\"];\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            if (context.trigger) {\n                const handleScroll = {\n                    \"TooltipContentImpl.useEffect.handleScroll\": (event)=>{\n                        const target = event.target;\n                        if (target?.contains(context.trigger)) onClose();\n                    }\n                }[\"TooltipContentImpl.useEffect.handleScroll\"];\n                window.addEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n                return ({\n                    \"TooltipContentImpl.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                            capture: true\n                        })\n                })[\"TooltipContentImpl.useEffect\"];\n            }\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_77fd30315bd1adbf71ffcb3d3c7056c6/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;