"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_vala"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vala.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vala.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = vala\nvala.displayName = 'vala'\nvala.aliases = []\nfunction vala(Prism) {\n  Prism.languages.vala = Prism.languages.extend('clike', {\n    // Classes copied from prism-csharp\n    'class-name': [\n      {\n        // (Foo bar, Bar baz)\n        pattern: /\\b[A-Z]\\w*(?:\\.\\w+)*\\b(?=(?:\\?\\s+|\\*?\\s+\\*?)\\w)/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // [Foo]\n        pattern: /(\\[)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo : Bar\n        pattern:\n          /(\\b(?:class|interface)\\s+[A-Z]\\w*(?:\\.\\w+)*\\s*:\\s*)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo\n        pattern:\n          /((?:\\b(?:class|enum|interface|new|struct)\\s+)|(?:catch\\s+\\())[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|async|base|bool|break|case|catch|char|class|const|construct|continue|default|delegate|delete|do|double|dynamic|else|ensures|enum|errordomain|extern|finally|float|for|foreach|get|if|in|inline|int|int16|int32|int64|int8|interface|internal|is|lock|long|namespace|new|null|out|override|owned|params|private|protected|public|ref|requires|return|set|short|signal|sizeof|size_t|ssize_t|static|string|struct|switch|this|throw|throws|try|typeof|uchar|uint|uint16|uint32|uint64|uint8|ulong|unichar|unowned|ushort|using|value|var|virtual|void|volatile|weak|while|yield)\\b/i,\n    function: /\\b\\w+(?=\\s*\\()/,\n    number:\n      /(?:\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?:f|u?l?)?/i,\n    operator:\n      /\\+\\+|--|&&|\\|\\||<<=?|>>=?|=>|->|~|[+\\-*\\/%&^|=!<>]=?|\\?\\??|\\.\\.\\./,\n    punctuation: /[{}[\\];(),.:]/,\n    constant: /\\b[A-Z0-9_]+\\b/\n  })\n  Prism.languages.insertBefore('vala', 'string', {\n    'raw-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    'template-string': {\n      pattern: /@\"[\\s\\S]*?\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$(?:\\([^)]*\\)|[a-zA-Z]\\w*)/,\n          inside: {\n            delimiter: {\n              pattern: /^\\$\\(?|\\)$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.vala\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('vala', 'keyword', {\n    regex: {\n      pattern:\n        /\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[imsx]{0,4}(?=\\s*(?:$|[\\r\\n,.;})\\]]))/,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\//,\n        'regex-flags': /^[a-z]+$/\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vala.js\n"));

/***/ })

}]);