"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_csp"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csp.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csp.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = csp\ncsp.displayName = 'csp'\ncsp.aliases = []\nfunction csp(Prism) {\n  /**\n   * Original by Scott Helme.\n   *\n   * Reference: https://scotthelme.co.uk/csp-cheat-sheet/\n   *\n   * Supports the following:\n   *  - https://www.w3.org/TR/CSP1/\n   *  - https://www.w3.org/TR/CSP2/\n   *  - https://www.w3.org/TR/CSP3/\n   */\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @returns {RegExp}\n     */\n    function value(source) {\n      return RegExp(\n        /([ \\t])/.source + '(?:' + source + ')' + /(?=[\\s;]|$)/.source,\n        'i'\n      )\n    }\n    Prism.languages.csp = {\n      directive: {\n        pattern:\n          /(^|[\\s;])(?:base-uri|block-all-mixed-content|(?:child|connect|default|font|frame|img|manifest|media|object|prefetch|script|style|worker)-src|disown-opener|form-action|frame-(?:ancestors|options)|input-protection(?:-(?:clip|selectors))?|navigate-to|plugin-types|policy-uri|referrer|reflected-xss|report-(?:to|uri)|require-sri-for|sandbox|(?:script|style)-src-(?:attr|elem)|upgrade-insecure-requests)(?=[\\s;]|$)/i,\n        lookbehind: true,\n        alias: 'property'\n      },\n      scheme: {\n        pattern: value(/[a-z][a-z0-9.+-]*:/.source),\n        lookbehind: true\n      },\n      none: {\n        pattern: value(/'none'/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      nonce: {\n        pattern: value(/'nonce-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      hash: {\n        pattern: value(/'sha(?:256|384|512)-[-+/\\w=]+'/.source),\n        lookbehind: true,\n        alias: 'number'\n      },\n      host: {\n        pattern: value(\n          /[a-z][a-z0-9.+-]*:\\/\\/[^\\s;,']*/.source +\n            '|' +\n            /\\*[^\\s;,']*/.source +\n            '|' +\n            /[a-z0-9-]+(?:\\.[a-z0-9-]+)+(?::[\\d*]+)?(?:\\/[^\\s;,']*)?/.source\n        ),\n        lookbehind: true,\n        alias: 'url',\n        inside: {\n          important: /\\*/\n        }\n      },\n      keyword: [\n        {\n          pattern: value(/'unsafe-[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'unsafe'\n        },\n        {\n          pattern: value(/'[a-z-]+'/.source),\n          lookbehind: true,\n          alias: 'safe'\n        }\n      ],\n      punctuation: /;/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/csp.js\n"));

/***/ })

}]);