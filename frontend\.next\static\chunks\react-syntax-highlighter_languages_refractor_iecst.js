"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_iecst"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/iecst.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/iecst.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = iecst\niecst.displayName = 'iecst'\niecst.aliases = []\nfunction iecst(Prism) {\n  Prism.languages.iecst = {\n    comment: [\n      {\n        pattern:\n          /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\(\\*[\\s\\S]*?(?:\\*\\)|$)|\\{[\\s\\S]*?(?:\\}|$))/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: [\n      /\\b(?:END_)?(?:PROGRAM|CONFIGURATION|INTERFACE|FUNCTION_BLOCK|FUNCTION|ACTION|TRANSITION|TYPE|STRUCT|(?:INITIAL_)?STEP|NAMESPACE|LIBRARY|CHANNEL|FOLDER|RESOURCE|VAR_(?:ACCESS|CONFIG|EXTERNAL|GLOBAL|INPUT|IN_OUT|OUTPUT|TEMP)|VAR|METHOD|PROPERTY)\\b/i,\n      /\\b(?:AT|BY|(?:END_)?(?:CASE|FOR|IF|REPEAT|WHILE)|CONSTANT|CONTINUE|DO|ELSE|ELSIF|EXIT|EXTENDS|FROM|GET|GOTO|IMPLEMENTS|JMP|NON_RETAIN|OF|PRIVATE|PROTECTED|PUBLIC|RETAIN|RETURN|SET|TASK|THEN|TO|UNTIL|USING|WITH|__CATCH|__ENDTRY|__FINALLY|__TRY)\\b/\n    ],\n    'class-name':\n      /\\b(?:ANY|ARRAY|BOOL|BYTE|U?(?:D|L|S)?INT|(?:D|L)?WORD|DATE(?:_AND_TIME)?|DT|L?REAL|POINTER|STRING|TIME(?:_OF_DAY)?|TOD)\\b/,\n    address: {\n      pattern: /%[IQM][XBWDL][\\d.]*|%[IQ][\\d.]*/,\n      alias: 'symbol'\n    },\n    number:\n      /\\b(?:16#[\\da-f]+|2#[01_]+|0x[\\da-f]+)\\b|\\b(?:D|DT|T|TOD)#[\\d_shmd:]*|\\b[A-Z]*#[\\d.,_]*|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:FALSE|NULL|TRUE)\\b/,\n    operator:\n      /S?R?:?=>?|&&?|\\*\\*?|<[=>]?|>=?|[-:^/+#]|\\b(?:AND|EQ|EXPT|GE|GT|LE|LT|MOD|NE|NOT|OR|XOR)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    punctuation: /[()[\\].,;]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/iecst.js\n"));

/***/ })

}]);