"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jsExtras"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-extras.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-extras.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = jsExtras\njsExtras.displayName = 'jsExtras'\njsExtras.aliases = []\nfunction jsExtras(Prism) {\n  ;(function (Prism) {\n    Prism.languages.insertBefore('javascript', 'function-variable', {\n      'method-variable': {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' +\n            Prism.languages.javascript['function-variable'].pattern.source\n        ),\n        lookbehind: true,\n        alias: ['function-variable', 'method', 'function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'function', {\n      method: {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' + Prism.languages.javascript['function'].source\n        ),\n        lookbehind: true,\n        alias: ['function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'constant', {\n      'known-class-name': [\n        {\n          // standard built-ins\n          // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\n          pattern:\n            /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,\n          alias: 'class-name'\n        },\n        {\n          // errors\n          pattern: /\\b(?:[A-Z]\\w*)Error\\b/,\n          alias: 'class-name'\n        }\n      ]\n    })\n    /**\n     * Replaces the `<ID>` placeholder in the given pattern with a pattern for general JS identifiers.\n     *\n     * @param {string} source\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function withId(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source\n        }),\n        flags\n      )\n    }\n    Prism.languages.insertBefore('javascript', 'keyword', {\n      imports: {\n        // https://tc39.es/ecma262/#sec-imports\n        pattern: withId(\n          /(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      exports: {\n        // https://tc39.es/ecma262/#sec-exports\n        pattern: withId(\n          /(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    })\n    Prism.languages.javascript['keyword'].unshift(\n      {\n        pattern: /\\b(?:as|default|export|from|import)\\b/,\n        alias: 'module'\n      },\n      {\n        pattern:\n          /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,\n        alias: 'control-flow'\n      },\n      {\n        pattern: /\\bnull\\b/,\n        alias: ['null', 'nil']\n      },\n      {\n        pattern: /\\bundefined\\b/,\n        alias: 'nil'\n      }\n    )\n    Prism.languages.insertBefore('javascript', 'operator', {\n      spread: {\n        pattern: /\\.{3}/,\n        alias: 'operator'\n      },\n      arrow: {\n        pattern: /=>/,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'punctuation', {\n      'property-access': {\n        pattern: withId(/(\\.\\s*)#?<ID>/.source),\n        lookbehind: true\n      },\n      'maybe-class-name': {\n        pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,\n        lookbehind: true\n      },\n      dom: {\n        // this contains only a few commonly used DOM variables\n        pattern:\n          /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,\n        alias: 'variable'\n      },\n      console: {\n        pattern: /\\bconsole(?=\\s*\\.)/,\n        alias: 'class-name'\n      }\n    }) // add 'maybe-class-name' to tokens which might be a class name\n    var maybeClassNameTokens = [\n      'function',\n      'function-variable',\n      'method',\n      'method-variable',\n      'property-access'\n    ]\n    for (var i = 0; i < maybeClassNameTokens.length; i++) {\n      var token = maybeClassNameTokens[i]\n      var value = Prism.languages.javascript[token] // convert regex to object\n      if (Prism.util.type(value) === 'RegExp') {\n        value = Prism.languages.javascript[token] = {\n          pattern: value\n        }\n      } // keep in mind that we don't support arrays\n      var inside = value.inside || {}\n      value.inside = inside\n      inside['maybe-class-name'] = /^[A-Z][\\s\\S]*/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2pzLWV4dHJhcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELElBQUksSUFBSSxxQkFBcUIsSUFBSSxJQUFJO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSxJQUFJLElBQUk7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsRUFBRTtBQUN2QjtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixpQ0FBaUM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGpzLWV4dHJhcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBqc0V4dHJhc1xuanNFeHRyYXMuZGlzcGxheU5hbWUgPSAnanNFeHRyYXMnXG5qc0V4dHJhcy5hbGlhc2VzID0gW11cbmZ1bmN0aW9uIGpzRXh0cmFzKFByaXNtKSB7XG4gIDsoZnVuY3Rpb24gKFByaXNtKSB7XG4gICAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnamF2YXNjcmlwdCcsICdmdW5jdGlvbi12YXJpYWJsZScsIHtcbiAgICAgICdtZXRob2QtdmFyaWFibGUnOiB7XG4gICAgICAgIHBhdHRlcm46IFJlZ0V4cChcbiAgICAgICAgICAnKFxcXFwuXFxcXHMqKScgK1xuICAgICAgICAgICAgUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRbJ2Z1bmN0aW9uLXZhcmlhYmxlJ10ucGF0dGVybi5zb3VyY2VcbiAgICAgICAgKSxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgYWxpYXM6IFsnZnVuY3Rpb24tdmFyaWFibGUnLCAnbWV0aG9kJywgJ2Z1bmN0aW9uJywgJ3Byb3BlcnR5LWFjY2VzcyddXG4gICAgICB9XG4gICAgfSlcbiAgICBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCdqYXZhc2NyaXB0JywgJ2Z1bmN0aW9uJywge1xuICAgICAgbWV0aG9kOiB7XG4gICAgICAgIHBhdHRlcm46IFJlZ0V4cChcbiAgICAgICAgICAnKFxcXFwuXFxcXHMqKScgKyBQcmlzbS5sYW5ndWFnZXMuamF2YXNjcmlwdFsnZnVuY3Rpb24nXS5zb3VyY2VcbiAgICAgICAgKSxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgYWxpYXM6IFsnZnVuY3Rpb24nLCAncHJvcGVydHktYWNjZXNzJ11cbiAgICAgIH1cbiAgICB9KVxuICAgIFByaXNtLmxhbmd1YWdlcy5pbnNlcnRCZWZvcmUoJ2phdmFzY3JpcHQnLCAnY29uc3RhbnQnLCB7XG4gICAgICAna25vd24tY2xhc3MtbmFtZSc6IFtcbiAgICAgICAge1xuICAgICAgICAgIC8vIHN0YW5kYXJkIGJ1aWx0LWluc1xuICAgICAgICAgIC8vIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL0dsb2JhbF9PYmplY3RzXG4gICAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAgIC9cXGIoPzooPzpGbG9hdCg/OjMyfDY0KXwoPzpJbnR8VWludCkoPzo4fDE2fDMyKXxVaW50OENsYW1wZWQpP0FycmF5fEFycmF5QnVmZmVyfEJpZ0ludHxCb29sZWFufERhdGFWaWV3fERhdGV8RXJyb3J8RnVuY3Rpb258SW50bHxKU09OfCg/OldlYWspPyg/Ok1hcHxTZXQpfE1hdGh8TnVtYmVyfE9iamVjdHxQcm9taXNlfFByb3h5fFJlZmxlY3R8UmVnRXhwfFN0cmluZ3xTeW1ib2x8V2ViQXNzZW1ibHkpXFxiLyxcbiAgICAgICAgICBhbGlhczogJ2NsYXNzLW5hbWUnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICAvLyBlcnJvcnNcbiAgICAgICAgICBwYXR0ZXJuOiAvXFxiKD86W0EtWl1cXHcqKUVycm9yXFxiLyxcbiAgICAgICAgICBhbGlhczogJ2NsYXNzLW5hbWUnXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9KVxuICAgIC8qKlxuICAgICAqIFJlcGxhY2VzIHRoZSBgPElEPmAgcGxhY2Vob2xkZXIgaW4gdGhlIGdpdmVuIHBhdHRlcm4gd2l0aCBhIHBhdHRlcm4gZm9yIGdlbmVyYWwgSlMgaWRlbnRpZmllcnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc291cmNlXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IFtmbGFnc11cbiAgICAgKiBAcmV0dXJucyB7UmVnRXhwfVxuICAgICAqL1xuICAgIGZ1bmN0aW9uIHdpdGhJZChzb3VyY2UsIGZsYWdzKSB7XG4gICAgICByZXR1cm4gUmVnRXhwKFxuICAgICAgICBzb3VyY2UucmVwbGFjZSgvPElEPi9nLCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgcmV0dXJuIC8oPyFcXHMpW18kYS16QS1aXFx4QTAtXFx1RkZGRl0oPzooPyFcXHMpWyRcXHdcXHhBMC1cXHVGRkZGXSkqLy5zb3VyY2VcbiAgICAgICAgfSksXG4gICAgICAgIGZsYWdzXG4gICAgICApXG4gICAgfVxuICAgIFByaXNtLmxhbmd1YWdlcy5pbnNlcnRCZWZvcmUoJ2phdmFzY3JpcHQnLCAna2V5d29yZCcsIHtcbiAgICAgIGltcG9ydHM6IHtcbiAgICAgICAgLy8gaHR0cHM6Ly90YzM5LmVzL2VjbWEyNjIvI3NlYy1pbXBvcnRzXG4gICAgICAgIHBhdHRlcm46IHdpdGhJZChcbiAgICAgICAgICAvKFxcYmltcG9ydFxcYlxccyopKD86PElEPig/OlxccyosXFxzKig/OlxcKlxccyphc1xccys8SUQ+fFxce1tee31dKlxcfSkpP3xcXCpcXHMqYXNcXHMrPElEPnxcXHtbXnt9XSpcXH0pKD89XFxzKlxcYmZyb21cXGIpL1xuICAgICAgICAgICAgLnNvdXJjZVxuICAgICAgICApLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICBpbnNpZGU6IFByaXNtLmxhbmd1YWdlcy5qYXZhc2NyaXB0XG4gICAgICB9LFxuICAgICAgZXhwb3J0czoge1xuICAgICAgICAvLyBodHRwczovL3RjMzkuZXMvZWNtYTI2Mi8jc2VjLWV4cG9ydHNcbiAgICAgICAgcGF0dGVybjogd2l0aElkKFxuICAgICAgICAgIC8oXFxiZXhwb3J0XFxiXFxzKikoPzpcXCooPzpcXHMqYXNcXHMrPElEPik/KD89XFxzKlxcYmZyb21cXGIpfFxce1tee31dKlxcfSkvXG4gICAgICAgICAgICAuc291cmNlXG4gICAgICAgICksXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZTogUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRcbiAgICAgIH1cbiAgICB9KVxuICAgIFByaXNtLmxhbmd1YWdlcy5qYXZhc2NyaXB0WydrZXl3b3JkJ10udW5zaGlmdChcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjogL1xcYig/OmFzfGRlZmF1bHR8ZXhwb3J0fGZyb218aW1wb3J0KVxcYi8sXG4gICAgICAgIGFsaWFzOiAnbW9kdWxlJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAvXFxiKD86YXdhaXR8YnJlYWt8Y2F0Y2h8Y29udGludWV8ZG98ZWxzZXxmaW5hbGx5fGZvcnxpZnxyZXR1cm58c3dpdGNofHRocm93fHRyeXx3aGlsZXx5aWVsZClcXGIvLFxuICAgICAgICBhbGlhczogJ2NvbnRyb2wtZmxvdydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46IC9cXGJudWxsXFxiLyxcbiAgICAgICAgYWxpYXM6IFsnbnVsbCcsICduaWwnXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjogL1xcYnVuZGVmaW5lZFxcYi8sXG4gICAgICAgIGFsaWFzOiAnbmlsJ1xuICAgICAgfVxuICAgIClcbiAgICBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCdqYXZhc2NyaXB0JywgJ29wZXJhdG9yJywge1xuICAgICAgc3ByZWFkOiB7XG4gICAgICAgIHBhdHRlcm46IC9cXC57M30vLFxuICAgICAgICBhbGlhczogJ29wZXJhdG9yJ1xuICAgICAgfSxcbiAgICAgIGFycm93OiB7XG4gICAgICAgIHBhdHRlcm46IC89Pi8sXG4gICAgICAgIGFsaWFzOiAnb3BlcmF0b3InXG4gICAgICB9XG4gICAgfSlcbiAgICBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCdqYXZhc2NyaXB0JywgJ3B1bmN0dWF0aW9uJywge1xuICAgICAgJ3Byb3BlcnR5LWFjY2Vzcyc6IHtcbiAgICAgICAgcGF0dGVybjogd2l0aElkKC8oXFwuXFxzKikjPzxJRD4vLnNvdXJjZSksXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgIH0sXG4gICAgICAnbWF5YmUtY2xhc3MtbmFtZSc6IHtcbiAgICAgICAgcGF0dGVybjogLyhefFteJFxcd1xceEEwLVxcdUZGRkZdKVtBLVpdWyRcXHdcXHhBMC1cXHVGRkZGXSsvLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICB9LFxuICAgICAgZG9tOiB7XG4gICAgICAgIC8vIHRoaXMgY29udGFpbnMgb25seSBhIGZldyBjb21tb25seSB1c2VkIERPTSB2YXJpYWJsZXNcbiAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAvXFxiKD86ZG9jdW1lbnR8KD86bG9jYWx8c2Vzc2lvbilTdG9yYWdlfGxvY2F0aW9ufG5hdmlnYXRvcnxwZXJmb3JtYW5jZXx3aW5kb3cpXFxiLyxcbiAgICAgICAgYWxpYXM6ICd2YXJpYWJsZSdcbiAgICAgIH0sXG4gICAgICBjb25zb2xlOiB7XG4gICAgICAgIHBhdHRlcm46IC9cXGJjb25zb2xlKD89XFxzKlxcLikvLFxuICAgICAgICBhbGlhczogJ2NsYXNzLW5hbWUnXG4gICAgICB9XG4gICAgfSkgLy8gYWRkICdtYXliZS1jbGFzcy1uYW1lJyB0byB0b2tlbnMgd2hpY2ggbWlnaHQgYmUgYSBjbGFzcyBuYW1lXG4gICAgdmFyIG1heWJlQ2xhc3NOYW1lVG9rZW5zID0gW1xuICAgICAgJ2Z1bmN0aW9uJyxcbiAgICAgICdmdW5jdGlvbi12YXJpYWJsZScsXG4gICAgICAnbWV0aG9kJyxcbiAgICAgICdtZXRob2QtdmFyaWFibGUnLFxuICAgICAgJ3Byb3BlcnR5LWFjY2VzcydcbiAgICBdXG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBtYXliZUNsYXNzTmFtZVRva2Vucy5sZW5ndGg7IGkrKykge1xuICAgICAgdmFyIHRva2VuID0gbWF5YmVDbGFzc05hbWVUb2tlbnNbaV1cbiAgICAgIHZhciB2YWx1ZSA9IFByaXNtLmxhbmd1YWdlcy5qYXZhc2NyaXB0W3Rva2VuXSAvLyBjb252ZXJ0IHJlZ2V4IHRvIG9iamVjdFxuICAgICAgaWYgKFByaXNtLnV0aWwudHlwZSh2YWx1ZSkgPT09ICdSZWdFeHAnKSB7XG4gICAgICAgIHZhbHVlID0gUHJpc20ubGFuZ3VhZ2VzLmphdmFzY3JpcHRbdG9rZW5dID0ge1xuICAgICAgICAgIHBhdHRlcm46IHZhbHVlXG4gICAgICAgIH1cbiAgICAgIH0gLy8ga2VlcCBpbiBtaW5kIHRoYXQgd2UgZG9uJ3Qgc3VwcG9ydCBhcnJheXNcbiAgICAgIHZhciBpbnNpZGUgPSB2YWx1ZS5pbnNpZGUgfHwge31cbiAgICAgIHZhbHVlLmluc2lkZSA9IGluc2lkZVxuICAgICAgaW5zaWRlWydtYXliZS1jbGFzcy1uYW1lJ10gPSAvXltBLVpdW1xcc1xcU10qL1xuICAgIH1cbiAgfSkoUHJpc20pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-extras.js\n"));

/***/ })

}]);