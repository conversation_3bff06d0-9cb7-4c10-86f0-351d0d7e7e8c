"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_cypher"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cypher.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cypher.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = cypher\ncypher.displayName = 'cypher'\ncypher.aliases = []\nfunction cypher(Prism) {\n  Prism.languages.cypher = {\n    // https://neo4j.com/docs/cypher-manual/current/syntax/comments/\n    comment: /\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)(?=\\s*[{):])/,\n      lookbehind: true,\n      greedy: true\n    },\n    relationship: {\n      pattern:\n        /(-\\[\\s*(?:\\w+\\s*|`(?:[^`\\\\\\r\\n])*`\\s*)?:\\s*|\\|\\s*:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    identifier: {\n      pattern: /`(?:[^`\\\\\\r\\n])*`/,\n      greedy: true\n    },\n    variable: /\\$\\w+/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/reserved/\n    keyword:\n      /\\b(?:ADD|ALL|AND|AS|ASC|ASCENDING|ASSERT|BY|CALL|CASE|COMMIT|CONSTRAINT|CONTAINS|CREATE|CSV|DELETE|DESC|DESCENDING|DETACH|DISTINCT|DO|DROP|ELSE|END|ENDS|EXISTS|FOR|FOREACH|IN|INDEX|IS|JOIN|KEY|LIMIT|LOAD|MANDATORY|MATCH|MERGE|NODE|NOT|OF|ON|OPTIONAL|OR|ORDER(?=\\s+BY)|PERIODIC|REMOVE|REQUIRE|RETURN|SCALAR|SCAN|SET|SKIP|START|STARTS|THEN|UNION|UNIQUE|UNWIND|USING|WHEN|WHERE|WITH|XOR|YIELD)\\b/i,\n    function: /\\b\\w+\\b(?=\\s*\\()/,\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: /\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)\\b/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/operators/\n    operator: /:|<--?|--?>?|<>|=~?|[<>]=?|[+*/%^|]|\\.\\.\\.?/,\n    punctuation: /[()[\\]{},;.]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cypher.js\n"));

/***/ })

}]);