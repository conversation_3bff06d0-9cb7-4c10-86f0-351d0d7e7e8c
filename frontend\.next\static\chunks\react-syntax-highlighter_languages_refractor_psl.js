"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_psl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/psl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/psl.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = psl\npsl.displayName = 'psl'\npsl.aliases = []\nfunction psl(Prism) {\n  Prism.languages.psl = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"])*\"/,\n      greedy: true,\n      inside: {\n        symbol: /\\\\[ntrbA-Z\"\\\\]/\n      }\n    },\n    'heredoc-string': {\n      pattern: /<<<([a-zA-Z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\1\\b/,\n      alias: 'string',\n      greedy: true\n    },\n    keyword:\n      /\\b(?:__multi|__single|case|default|do|else|elsif|exit|export|for|foreach|function|if|last|line|local|next|requires|return|switch|until|while|word)\\b/,\n    constant:\n      /\\b(?:ALARM|CHART_ADD_GRAPH|CHART_DELETE_GRAPH|CHART_DESTROY|CHART_LOAD|CHART_PRINT|EOF|OFFLINE|OK|PSL_PROF_LOG|R_CHECK_HORIZ|R_CHECK_VERT|R_CLICKER|R_COLUMN|R_FRAME|R_ICON|R_LABEL|R_LABEL_CENTER|R_LIST_MULTIPLE|R_LIST_MULTIPLE_ND|R_LIST_SINGLE|R_LIST_SINGLE_ND|R_MENU|R_POPUP|R_POPUP_SCROLLED|R_RADIO_HORIZ|R_RADIO_VERT|R_ROW|R_SCALE_HORIZ|R_SCALE_VERT|R_SEP_HORIZ|R_SEP_VERT|R_SPINNER|R_TEXT_FIELD|R_TEXT_FIELD_LABEL|R_TOGGLE|TRIM_LEADING|TRIM_LEADING_AND_TRAILING|TRIM_REDUNDANT|TRIM_TRAILING|VOID|WARN)\\b/,\n    boolean: /\\b(?:FALSE|False|NO|No|TRUE|True|YES|Yes|false|no|true|yes)\\b/,\n    variable: /\\b(?:PslDebug|errno|exit_status)\\b/,\n    builtin: {\n      pattern:\n        /\\b(?:PslExecute|PslFunctionCall|PslFunctionExists|PslSetOptions|_snmp_debug|acos|add_diary|annotate|annotate_get|ascii_to_ebcdic|asctime|asin|atan|atexit|batch_set|blackout|cat|ceil|chan_exists|change_state|close|code_cvt|cond_signal|cond_wait|console_type|convert_base|convert_date|convert_locale_date|cos|cosh|create|date|dcget_text|destroy|destroy_lock|dget_text|difference|dump_hist|ebcdic_to_ascii|encrypt|event_archive|event_catalog_get|event_check|event_query|event_range_manage|event_range_query|event_report|event_schedule|event_trigger|event_trigger2|execute|exists|exp|fabs|file|floor|fmod|fopen|fseek|ftell|full_discovery|get|get_chan_info|get_ranges|get_text|get_vars|getenv|gethostinfo|getpid|getpname|grep|history|history_get_retention|in_transition|index|int|internal|intersection|is_var|isnumber|join|kill|length|lines|lock|lock_info|log|log10|loge|matchline|msg_check|msg_get_format|msg_get_severity|msg_printf|msg_sprintf|ntharg|nthargf|nthline|nthlinef|num_bytes|num_consoles|pconfig|popen|poplines|pow|print|printf|proc_exists|process|random|read|readln|refresh_parameters|remote_check|remote_close|remote_event_query|remote_event_trigger|remote_file_send|remote_open|remove|replace|rindex|sec_check_priv|sec_store_get|sec_store_set|set|set_alarm_ranges|set_locale|share|sin|sinh|sleep|snmp_agent_config|snmp_agent_start|snmp_agent_stop|snmp_close|snmp_config|snmp_get|snmp_get_next|snmp_h_get|snmp_h_get_next|snmp_h_set|snmp_open|snmp_set|snmp_trap_ignore|snmp_trap_listen|snmp_trap_raise_std_trap|snmp_trap_receive|snmp_trap_register_im|snmp_trap_send|snmp_walk|sopen|sort|splitline|sprintf|sqrt|srandom|str_repeat|strcasecmp|subset|substr|system|tail|tan|tanh|text_domain|time|tmpnam|tolower|toupper|trace_psl_process|trim|union|unique|unlock|unset|va_arg|va_start|write)\\b/,\n      alias: 'builtin-function'\n    },\n    'foreach-variable': {\n      pattern:\n        /(\\bforeach\\s+(?:(?:\\w+\\b|\"(?:\\\\.|[^\\\\\"])*\")\\s+){0,2})[_a-zA-Z]\\w*(?=\\s*\\()/,\n      lookbehind: true,\n      greedy: true\n    },\n    function: /\\b[_a-z]\\w*\\b(?=\\s*\\()/i,\n    number: /\\b(?:0x[0-9a-f]+|\\d+(?:\\.\\d+)?)\\b/i,\n    operator: /--|\\+\\+|&&=?|\\|\\|=?|<<=?|>>=?|[=!]~|[-+*/%&|^!=<>]=?|\\.|[:?]/,\n    punctuation: /[(){}\\[\\];,]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/psl.js\n"));

/***/ })

}]);