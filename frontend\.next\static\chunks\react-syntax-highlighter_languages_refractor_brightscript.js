"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_brightscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/brightscript.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/brightscript.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = brightscript\nbrightscript.displayName = 'brightscript'\nbrightscript.aliases = []\nfunction brightscript(Prism) {\n  Prism.languages.brightscript = {\n    comment: /(?:\\brem|').*/i,\n    'directive-statement': {\n      pattern: /(^[\\t ]*)#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if).*/im,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        'error-message': {\n          pattern: /(^#error).+/,\n          lookbehind: true\n        },\n        directive: {\n          pattern: /^#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if)/,\n          alias: 'keyword'\n        },\n        expression: {\n          pattern: /[\\s\\S]+/,\n          inside: null // see below\n        }\n      }\n    },\n    property: {\n      pattern:\n        /([\\r\\n{,][\\t ]*)(?:(?!\\d)\\w+|\"(?:[^\"\\r\\n]|\"\")*\"(?!\"))(?=[ \\t]*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\bAs[\\t ]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:As|Dim|Each|Else|Elseif|End|Exit|For|Function|Goto|If|In|Print|Return|Step|Stop|Sub|Then|To|While)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\b(?!\\d)\\w+(?=[\\t ]*\\()/,\n    number: /(?:\\b\\d+(?:\\.\\d+)?(?:[ed][+-]\\d+)?|&h[a-f\\d]+)\\b[%&!#]?/i,\n    operator:\n      /--|\\+\\+|>>=?|<<=?|<>|[-+*/\\\\<>]=?|[:^=?]|\\b(?:and|mod|not|or)\\b/i,\n    punctuation: /[.,;()[\\]{}]/,\n    constant: /\\b(?:LINE_NUM)\\b/i\n  }\n  Prism.languages.brightscript['directive-statement'].inside.expression.inside =\n    Prism.languages.brightscript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/brightscript.js\n"));

/***/ })

}]);