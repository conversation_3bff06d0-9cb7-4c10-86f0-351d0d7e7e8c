"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_wolfram"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wolfram.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wolfram.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = wolfram\nwolfram.displayName = 'wolfram'\nwolfram.aliases = ['mathematica', 'wl', 'nb']\nfunction wolfram(Prism) {\n  Prism.languages.wolfram = {\n    // Allow one level of nesting - note: regex taken from applescipt\n    comment: /\\(\\*(?:\\(\\*(?:[^*]|\\*(?!\\)))*\\*\\)|(?!\\(\\*)[\\s\\S])*?\\*\\)/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:Abs|AbsArg|Accuracy|Block|Do|For|Function|If|Manipulate|Module|Nest|NestList|None|Return|Switch|Table|Which|While)\\b/,\n    context: {\n      pattern: /\\b\\w+`+\\w*/,\n      alias: 'class-name'\n    },\n    blank: {\n      pattern: /\\b\\w+_\\b/,\n      alias: 'regex'\n    },\n    'global-variable': {\n      pattern: /\\$\\w+/,\n      alias: 'variable'\n    },\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[bo])?(?:(?:\\d|0x[\\da-f])[\\da-f]*(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?j?\\b/i,\n    operator:\n      /\\/\\.|;|=\\.|\\^=|\\^:=|:=|<<|>>|<\\||\\|>|:>|\\|->|->|<-|@@@|@@|@|\\/@|=!=|===|==|=|\\+|-|\\^|\\[\\/-+%=\\]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.mathematica = Prism.languages.wolfram\n  Prism.languages.wl = Prism.languages.wolfram\n  Prism.languages.nb = Prism.languages.wolfram\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wolfram.js\n"));

/***/ })

}]);