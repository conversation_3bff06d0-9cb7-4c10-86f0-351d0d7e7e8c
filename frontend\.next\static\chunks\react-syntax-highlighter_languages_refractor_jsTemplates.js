"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jsTemplates"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-templates.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-templates.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = jsTemplates\njsTemplates.displayName = 'jsTemplates'\njsTemplates.aliases = []\nfunction jsTemplates(Prism) {\n  ;(function (Prism) {\n    var templateString = Prism.languages.javascript['template-string'] // see the pattern in prism-javascript.js\n    var templateLiteralPattern = templateString.pattern.source\n    var interpolationObject = templateString.inside['interpolation']\n    var interpolationPunctuationObject =\n      interpolationObject.inside['interpolation-punctuation']\n    var interpolationPattern = interpolationObject.pattern.source\n    /**\n     * Creates a new pattern to match a template string with a special tag.\n     *\n     * This will return `undefined` if there is no grammar with the given language id.\n     *\n     * @param {string} language The language id of the embedded language. E.g. `markdown`.\n     * @param {string} tag The regex pattern to match the tag.\n     * @returns {object | undefined}\n     * @example\n     * createTemplate('css', /\\bcss/.source);\n     */\n    function createTemplate(language, tag) {\n      if (!Prism.languages[language]) {\n        return undefined\n      }\n      return {\n        pattern: RegExp('((?:' + tag + ')\\\\s*)' + templateLiteralPattern),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'template-punctuation': {\n            pattern: /^`|`$/,\n            alias: 'string'\n          },\n          'embedded-code': {\n            pattern: /[\\s\\S]+/,\n            alias: language\n          }\n        }\n      }\n    }\n    Prism.languages.javascript['template-string'] = [\n      // styled-jsx:\n      //   css`a { color: #25F; }`\n      // styled-components:\n      //   styled.h1`color: red;`\n      createTemplate(\n        'css',\n        /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/\n          .source\n      ), // html`<p></p>`\n      // div.innerHTML = `<p></p>`\n      createTemplate('html', /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source), // svg`<path fill=\"#fff\" d=\"M55.37 ...\"/>`\n      createTemplate('svg', /\\bsvg/.source), // md`# h1`, markdown`## h2`\n      createTemplate('markdown', /\\b(?:markdown|md)/.source), // gql`...`, graphql`...`, graphql.experimental`...`\n      createTemplate(\n        'graphql',\n        /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source\n      ), // sql`...`\n      createTemplate('sql', /\\bsql/.source), // vanilla template string\n      templateString\n    ].filter(Boolean)\n    /**\n     * Returns a specific placeholder literal for the given language.\n     *\n     * @param {number} counter\n     * @param {string} language\n     * @returns {string}\n     */\n    function getPlaceholder(counter, language) {\n      return '___' + language.toUpperCase() + '_' + counter + '___'\n    }\n    /**\n     * Returns the tokens of `Prism.tokenize` but also runs the `before-tokenize` and `after-tokenize` hooks.\n     *\n     * @param {string} code\n     * @param {any} grammar\n     * @param {string} language\n     * @returns {(string|Token)[]}\n     */\n    function tokenizeWithHooks(code, grammar, language) {\n      var env = {\n        code: code,\n        grammar: grammar,\n        language: language\n      }\n      Prism.hooks.run('before-tokenize', env)\n      env.tokens = Prism.tokenize(env.code, env.grammar)\n      Prism.hooks.run('after-tokenize', env)\n      return env.tokens\n    }\n    /**\n     * Returns the token of the given JavaScript interpolation expression.\n     *\n     * @param {string} expression The code of the expression. E.g. `\"${42}\"`\n     * @returns {Token}\n     */\n    function tokenizeInterpolationExpression(expression) {\n      var tempGrammar = {}\n      tempGrammar['interpolation-punctuation'] = interpolationPunctuationObject\n      /** @type {Array} */\n      var tokens = Prism.tokenize(expression, tempGrammar)\n      if (tokens.length === 3) {\n        /**\n         * The token array will look like this\n         * [\n         *     [\"interpolation-punctuation\", \"${\"]\n         *     \"...\" // JavaScript expression of the interpolation\n         *     [\"interpolation-punctuation\", \"}\"]\n         * ]\n         */\n        var args = [1, 1]\n        args.push.apply(\n          args,\n          tokenizeWithHooks(tokens[1], Prism.languages.javascript, 'javascript')\n        )\n        tokens.splice.apply(tokens, args)\n      }\n      return new Prism.Token(\n        'interpolation',\n        tokens,\n        interpolationObject.alias,\n        expression\n      )\n    }\n    /**\n     * Tokenizes the given code with support for JavaScript interpolation expressions mixed in.\n     *\n     * This function has 3 phases:\n     *\n     * 1. Replace all JavaScript interpolation expression with a placeholder.\n     *    The placeholder will have the syntax of a identify of the target language.\n     * 2. Tokenize the code with placeholders.\n     * 3. Tokenize the interpolation expressions and re-insert them into the tokenize code.\n     *    The insertion only works if a placeholder hasn't been \"ripped apart\" meaning that the placeholder has been\n     *    tokenized as two tokens by the grammar of the embedded language.\n     *\n     * @param {string} code\n     * @param {object} grammar\n     * @param {string} language\n     * @returns {Token}\n     */\n    function tokenizeEmbedded(code, grammar, language) {\n      // 1. First filter out all interpolations\n      // because they might be escaped, we need a lookbehind, so we use Prism\n      /** @type {(Token|string)[]} */\n      var _tokens = Prism.tokenize(code, {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          lookbehind: true\n        }\n      }) // replace all interpolations with a placeholder which is not in the code already\n      var placeholderCounter = 0\n      /** @type {Object<string, string>} */\n      var placeholderMap = {}\n      var embeddedCode = _tokens\n        .map(function (token) {\n          if (typeof token === 'string') {\n            return token\n          } else {\n            var interpolationExpression = token.content\n            var placeholder\n            while (\n              code.indexOf(\n                (placeholder = getPlaceholder(placeholderCounter++, language))\n              ) !== -1\n            ) {\n              /* noop */\n            }\n            placeholderMap[placeholder] = interpolationExpression\n            return placeholder\n          }\n        })\n        .join('') // 2. Tokenize the embedded code\n      var embeddedTokens = tokenizeWithHooks(embeddedCode, grammar, language) // 3. Re-insert the interpolation\n      var placeholders = Object.keys(placeholderMap)\n      placeholderCounter = 0\n      /**\n       *\n       * @param {(Token|string)[]} tokens\n       * @returns {void}\n       */\n      function walkTokens(tokens) {\n        for (var i = 0; i < tokens.length; i++) {\n          if (placeholderCounter >= placeholders.length) {\n            return\n          }\n          var token = tokens[i]\n          if (typeof token === 'string' || typeof token.content === 'string') {\n            var placeholder = placeholders[placeholderCounter]\n            var s =\n              typeof token === 'string'\n                ? token\n                : /** @type {string} */\n                  token.content\n            var index = s.indexOf(placeholder)\n            if (index !== -1) {\n              ++placeholderCounter\n              var before = s.substring(0, index)\n              var middle = tokenizeInterpolationExpression(\n                placeholderMap[placeholder]\n              )\n              var after = s.substring(index + placeholder.length)\n              var replacement = []\n              if (before) {\n                replacement.push(before)\n              }\n              replacement.push(middle)\n              if (after) {\n                var afterTokens = [after]\n                walkTokens(afterTokens)\n                replacement.push.apply(replacement, afterTokens)\n              }\n              if (typeof token === 'string') {\n                tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                i += replacement.length - 1\n              } else {\n                token.content = replacement\n              }\n            }\n          } else {\n            var content = token.content\n            if (Array.isArray(content)) {\n              walkTokens(content)\n            } else {\n              walkTokens([content])\n            }\n          }\n        }\n      }\n      walkTokens(embeddedTokens)\n      return new Prism.Token(\n        language,\n        embeddedTokens,\n        'language-' + language,\n        code\n      )\n    }\n    /**\n     * The languages for which JS templating will handle tagged template literals.\n     *\n     * JS templating isn't active for only JavaScript but also related languages like TypeScript, JSX, and TSX.\n     */\n    var supportedLanguages = {\n      javascript: true,\n      js: true,\n      typescript: true,\n      ts: true,\n      jsx: true,\n      tsx: true\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (!(env.language in supportedLanguages)) {\n        return\n      }\n      /**\n       * Finds and tokenizes all template strings with an embedded languages.\n       *\n       * @param {(Token | string)[]} tokens\n       * @returns {void}\n       */\n      function findTemplateStrings(tokens) {\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i]\n          if (typeof token === 'string') {\n            continue\n          }\n          var content = token.content\n          if (!Array.isArray(content)) {\n            if (typeof content !== 'string') {\n              findTemplateStrings([content])\n            }\n            continue\n          }\n          if (token.type === 'template-string') {\n            /**\n             * A JavaScript template-string token will look like this:\n             *\n             * [\"template-string\", [\n             *     [\"template-punctuation\", \"`\"],\n             *     (\n             *         An array of \"string\" and \"interpolation\" tokens. This is the simple string case.\n             *         or\n             *         [\"embedded-code\", \"...\"] This is the token containing the embedded code.\n             *                                  It also has an alias which is the language of the embedded code.\n             *     ),\n             *     [\"template-punctuation\", \"`\"]\n             * ]]\n             */\n            var embedded = content[1]\n            if (\n              content.length === 3 &&\n              typeof embedded !== 'string' &&\n              embedded.type === 'embedded-code'\n            ) {\n              // get string content\n              var code = stringContent(embedded)\n              var alias = embedded.alias\n              var language = Array.isArray(alias) ? alias[0] : alias\n              var grammar = Prism.languages[language]\n              if (!grammar) {\n                // the embedded language isn't registered.\n                continue\n              }\n              content[1] = tokenizeEmbedded(code, grammar, language)\n            }\n          } else {\n            findTemplateStrings(content)\n          }\n        }\n      }\n      findTemplateStrings(env.tokens)\n    })\n    /**\n     * Returns the string content of a token or token stream.\n     *\n     * @param {string | Token | (string | Token)[]} value\n     * @returns {string}\n     */\n    function stringContent(value) {\n      if (typeof value === 'string') {\n        return value\n      } else if (Array.isArray(value)) {\n        return value.map(stringContent).join('')\n      } else {\n        return stringContent(value.content)\n      }\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/js-templates.js\n"));

/***/ })

}]);