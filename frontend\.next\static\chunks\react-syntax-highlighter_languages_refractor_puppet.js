"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_puppet"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/puppet.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/puppet.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = puppet\npuppet.displayName = 'puppet'\npuppet.aliases = []\nfunction puppet(Prism) {\n  ;(function (Prism) {\n    Prism.languages.puppet = {\n      heredoc: [\n        // Matches the content of a quoted heredoc string (subject to interpolation)\n        {\n          pattern:\n            /(@\\(\"([^\"\\r\\n\\/):]+)\"(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/ // See interpolation below\n          }\n        }, // Matches the content of an unquoted heredoc string (no interpolation)\n        {\n          pattern:\n            /(@\\(([^\"\\r\\n\\/):]+)(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/\n          }\n        }, // Matches the start tag of heredoc strings\n        {\n          pattern: /@\\(\"?(?:[^\"\\r\\n\\/):]+)\"?(?:\\/[nrts$uL]*)?\\)/,\n          alias: 'string',\n          inside: {\n            punctuation: {\n              pattern: /(\\().+?(?=\\))/,\n              lookbehind: true\n            }\n          }\n        }\n      ],\n      'multiline-comment': {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      },\n      regex: {\n        // Must be prefixed with the keyword \"node\" or a non-word char\n        pattern:\n          /((?:\\bnode\\s+|[~=\\(\\[\\{,]\\s*|[=+]>\\s*|^\\s*))\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/(?:[imx]+\\b|\\B)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          // Extended regexes must have the x flag. They can contain single-line comments.\n          'extended-regex': {\n            pattern: /^\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/[im]*x[im]*$/,\n            inside: {\n              comment: /#.*/\n            }\n          }\n        }\n      },\n      comment: {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      string: {\n        // Allow for one nested level of double quotes inside interpolation\n        pattern:\n          /([\"'])(?:\\$\\{(?:[^'\"}]|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}|\\$(?!\\{)|(?!\\1)[^\\\\$]|\\\\[\\s\\S])*\\1/,\n        greedy: true,\n        inside: {\n          'double-quoted': {\n            pattern: /^\"[\\s\\S]*\"$/,\n            inside: {\n              // See interpolation below\n            }\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$(?:::)?\\w+(?:::\\w+)*/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      'attr-name': /(?:\\b\\w+|\\*)(?=\\s*=>)/,\n      function: [\n        {\n          pattern: /(\\.)(?!\\d)\\w+/,\n          lookbehind: true\n        },\n        /\\b(?:contain|debug|err|fail|include|info|notice|realize|require|tag|warning)\\b|\\b(?!\\d)\\w+(?=\\()/\n      ],\n      number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d+)?(?:e-?\\d+)?)\\b/i,\n      boolean: /\\b(?:false|true)\\b/,\n      // Includes words reserved for future use\n      keyword:\n        /\\b(?:application|attr|case|class|consumes|default|define|else|elsif|function|if|import|inherits|node|private|produces|type|undef|unless)\\b/,\n      datatype: {\n        pattern:\n          /\\b(?:Any|Array|Boolean|Callable|Catalogentry|Class|Collection|Data|Default|Enum|Float|Hash|Integer|NotUndef|Numeric|Optional|Pattern|Regexp|Resource|Runtime|Scalar|String|Struct|Tuple|Type|Undef|Variant)\\b/,\n        alias: 'symbol'\n      },\n      operator:\n        /=[=~>]?|![=~]?|<(?:<\\|?|[=~|-])?|>[>=]?|->?|~>|\\|>?>?|[*\\/%+?]|\\b(?:and|in|or)\\b/,\n      punctuation: /[\\[\\]{}().,;]|:+/\n    }\n    var interpolation = [\n      {\n        // Allow for one nested level of braces inside interpolation\n        pattern:\n          /(^|[^\\\\])\\$\\{(?:[^'\"{}]|\\{[^}]*\\}|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}/,\n        lookbehind: true,\n        inside: {\n          'short-variable': {\n            // Negative look-ahead prevent wrong highlighting of functions\n            pattern: /(^\\$\\{)(?!\\w+\\()(?:::)?\\w+(?:::\\w+)*/,\n            lookbehind: true,\n            alias: 'variable',\n            inside: {\n              punctuation: /::/\n            }\n          },\n          delimiter: {\n            pattern: /^\\$/,\n            alias: 'variable'\n          },\n          rest: Prism.languages.puppet\n        }\n      },\n      {\n        pattern: /(^|[^\\\\])\\$(?:::)?\\w+(?:::\\w+)*/,\n        lookbehind: true,\n        alias: 'variable',\n        inside: {\n          punctuation: /::/\n        }\n      }\n    ]\n    Prism.languages.puppet['heredoc'][0].inside.interpolation = interpolation\n    Prism.languages.puppet['string'].inside[\n      'double-quoted'\n    ].inside.interpolation = interpolation\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/puppet.js\n"));

/***/ })

}]);