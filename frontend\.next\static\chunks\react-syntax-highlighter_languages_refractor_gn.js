"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_gn"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gn.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gn.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = gn\ngn.displayName = 'gn'\ngn.aliases = ['gni']\nfunction gn(Prism) {\n  // https://gn.googlesource.com/gn/+/refs/heads/main/docs/reference.md#grammar\n  Prism.languages.gn = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'string-literal': {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[\\s\\S]*?\\}|[a-zA-Z_]\\w*|0x[a-fA-F0-9]{2})/,\n          lookbehind: true,\n          inside: {\n            number: /^\\$0x[\\s\\S]{2}$/,\n            variable: /^\\$\\w+$/,\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: null // see below\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    keyword: /\\b(?:else|if)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'builtin-function': {\n      // a few functions get special highlighting to improve readability\n      pattern:\n        /\\b(?:assert|defined|foreach|import|pool|print|template|tool|toolchain)(?=\\s*\\()/i,\n      alias: 'keyword'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant:\n      /\\b(?:current_cpu|current_os|current_toolchain|default_toolchain|host_cpu|host_os|root_build_dir|root_gen_dir|root_out_dir|target_cpu|target_gen_dir|target_os|target_out_dir)\\b/,\n    number: /-?\\b\\d+\\b/,\n    operator: /[-+!=<>]=?|&&|\\|\\|/,\n    punctuation: /[(){}[\\],.]/\n  }\n  Prism.languages.gn['string-literal'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.gn\n  Prism.languages.gni = Prism.languages.gn\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gn.js\n"));

/***/ })

}]);