"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_purescript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = haskell\nhaskell.displayName = 'haskell'\nhaskell.aliases = ['hs']\nfunction haskell(Prism) {\n  Prism.languages.haskell = {\n    comment: {\n      pattern:\n        /(^|[^-!#$%*+=?&@|~.:<>^\\\\\\/])(?:--(?:(?=.)[^-!#$%*+=?&@|~.:<>^\\\\\\/].*|$)|\\{-[\\s\\S]*?-\\})/m,\n      lookbehind: true\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:[abfnrtv\\\\\"'&]|\\^[A-Z@[\\]^_]|ACK|BEL|BS|CAN|CR|DC1|DC2|DC3|DC4|DEL|DLE|EM|ENQ|EOT|ESC|ETB|ETX|FF|FS|GS|HT|LF|NAK|NUL|RS|SI|SO|SOH|SP|STX|SUB|SYN|US|VT|\\d+|o[0-7]+|x[0-9a-fA-F]+))'/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"]|\\\\(?:\\S|\\s+\\\\))*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:case|class|data|deriving|do|else|if|in|infixl|infixr|instance|let|module|newtype|of|primitive|then|type|where)\\b/,\n    'import-statement': {\n      // The imported or hidden names are not included in this import\n      // statement. This is because we want to highlight those exactly like\n      // we do for the names in the program.\n      pattern:\n        /(^[\\t ]*)import\\s+(?:qualified\\s+)?(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*(?:\\s+as\\s+(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*)?(?:\\s+hiding\\b)?/m,\n      lookbehind: true,\n      inside: {\n        keyword: /\\b(?:as|hiding|import|qualified)\\b/,\n        punctuation: /\\./\n      }\n    },\n    // These are builtin variables only. Constructors are highlighted later as a constant.\n    builtin:\n      /\\b(?:abs|acos|acosh|all|and|any|appendFile|approxRational|asTypeOf|asin|asinh|atan|atan2|atanh|basicIORun|break|catch|ceiling|chr|compare|concat|concatMap|const|cos|cosh|curry|cycle|decodeFloat|denominator|digitToInt|div|divMod|drop|dropWhile|either|elem|encodeFloat|enumFrom|enumFromThen|enumFromThenTo|enumFromTo|error|even|exp|exponent|fail|filter|flip|floatDigits|floatRadix|floatRange|floor|fmap|foldl|foldl1|foldr|foldr1|fromDouble|fromEnum|fromInt|fromInteger|fromIntegral|fromRational|fst|gcd|getChar|getContents|getLine|group|head|id|inRange|index|init|intToDigit|interact|ioError|isAlpha|isAlphaNum|isAscii|isControl|isDenormalized|isDigit|isHexDigit|isIEEE|isInfinite|isLower|isNaN|isNegativeZero|isOctDigit|isPrint|isSpace|isUpper|iterate|last|lcm|length|lex|lexDigits|lexLitChar|lines|log|logBase|lookup|map|mapM|mapM_|max|maxBound|maximum|maybe|min|minBound|minimum|mod|negate|not|notElem|null|numerator|odd|or|ord|otherwise|pack|pi|pred|primExitWith|print|product|properFraction|putChar|putStr|putStrLn|quot|quotRem|range|rangeSize|read|readDec|readFile|readFloat|readHex|readIO|readInt|readList|readLitChar|readLn|readOct|readParen|readSigned|reads|readsPrec|realToFrac|recip|rem|repeat|replicate|return|reverse|round|scaleFloat|scanl|scanl1|scanr|scanr1|seq|sequence|sequence_|show|showChar|showInt|showList|showLitChar|showParen|showSigned|showString|shows|showsPrec|significand|signum|sin|sinh|snd|sort|span|splitAt|sqrt|subtract|succ|sum|tail|take|takeWhile|tan|tanh|threadToIOResult|toEnum|toInt|toInteger|toLower|toRational|toUpper|truncate|uncurry|undefined|unlines|until|unwords|unzip|unzip3|userError|words|writeFile|zip|zip3|zipWith|zipWith3)\\b/,\n    // decimal integers and floating point numbers | octal integers | hexadecimal integers\n    number: /\\b(?:\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?|0o[0-7]+|0x[0-9a-f]+)\\b/i,\n    operator: [\n      {\n        // infix operator\n        pattern: /`(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*`/,\n        greedy: true\n      },\n      {\n        // function composition\n        pattern: /(\\s)\\.(?=\\s)/,\n        lookbehind: true\n      }, // Most of this is needed because of the meaning of a single '.'.\n      // If it stands alone freely, it is the function composition.\n      // It may also be a separator between a module name and an identifier => no\n      // operator. If it comes together with other special characters it is an\n      // operator too.\n      //\n      // This regex means: /[-!#$%*+=?&@|~.:<>^\\\\\\/]+/ without /\\./.\n      /[-!#$%*+=?&@|~:<>^\\\\\\/][-!#$%*+=?&@|~.:<>^\\\\\\/]*|\\.[-!#$%*+=?&@|~.:<>^\\\\\\/]+/\n    ],\n    // In Haskell, nearly everything is a variable, do not highlight these.\n    hvariable: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    constant: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[A-Z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.hs = Prism.languages.haskell\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/purescript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/purescript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorHaskell = __webpack_require__(/*! ./haskell.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js\")\nmodule.exports = purescript\npurescript.displayName = 'purescript'\npurescript.aliases = ['purs']\nfunction purescript(Prism) {\n  Prism.register(refractorHaskell)\n  Prism.languages.purescript = Prism.languages.extend('haskell', {\n    keyword:\n      /\\b(?:ado|case|class|data|derive|do|else|forall|if|in|infixl|infixr|instance|let|module|newtype|of|primitive|then|type|where)\\b|∀/,\n    'import-statement': {\n      // The imported or hidden names are not included in this import\n      // statement. This is because we want to highlight those exactly like\n      // we do for the names in the program.\n      pattern:\n        /(^[\\t ]*)import\\s+[A-Z][\\w']*(?:\\.[A-Z][\\w']*)*(?:\\s+as\\s+[A-Z][\\w']*(?:\\.[A-Z][\\w']*)*)?(?:\\s+hiding\\b)?/m,\n      lookbehind: true,\n      inside: {\n        keyword: /\\b(?:as|hiding|import)\\b/,\n        punctuation: /\\./\n      }\n    },\n    // These are builtin functions only. Constructors are highlighted later as a constant.\n    builtin:\n      /\\b(?:absurd|add|ap|append|apply|between|bind|bottom|clamp|compare|comparing|compose|conj|const|degree|discard|disj|div|eq|flap|flip|gcd|identity|ifM|join|lcm|liftA1|liftM1|map|max|mempty|min|mod|mul|negate|not|notEq|one|otherwise|recip|show|sub|top|unit|unless|unlessM|void|when|whenM|zero)\\b/,\n    operator: [\n      // Infix operators\n      Prism.languages.haskell.operator[0], // ASCII operators\n      Prism.languages.haskell.operator[2], // All UTF16 Unicode operator symbols\n      // This regex is equivalent to /(?=[\\x80-\\uFFFF])[\\p{gc=Math_Symbol}\\p{gc=Currency_Symbol}\\p{Modifier_Symbol}\\p{Other_Symbol}]/u\n      // See https://github.com/PrismJS/prism/issues/3006 for more details.\n      /[\\xa2-\\xa6\\xa8\\xa9\\xac\\xae-\\xb1\\xb4\\xb8\\xd7\\xf7\\u02c2-\\u02c5\\u02d2-\\u02df\\u02e5-\\u02eb\\u02ed\\u02ef-\\u02ff\\u0375\\u0384\\u0385\\u03f6\\u0482\\u058d-\\u058f\\u0606-\\u0608\\u060b\\u060e\\u060f\\u06de\\u06e9\\u06fd\\u06fe\\u07f6\\u07fe\\u07ff\\u09f2\\u09f3\\u09fa\\u09fb\\u0af1\\u0b70\\u0bf3-\\u0bfa\\u0c7f\\u0d4f\\u0d79\\u0e3f\\u0f01-\\u0f03\\u0f13\\u0f15-\\u0f17\\u0f1a-\\u0f1f\\u0f34\\u0f36\\u0f38\\u0fbe-\\u0fc5\\u0fc7-\\u0fcc\\u0fce\\u0fcf\\u0fd5-\\u0fd8\\u109e\\u109f\\u1390-\\u1399\\u166d\\u17db\\u1940\\u19de-\\u19ff\\u1b61-\\u1b6a\\u1b74-\\u1b7c\\u1fbd\\u1fbf-\\u1fc1\\u1fcd-\\u1fcf\\u1fdd-\\u1fdf\\u1fed-\\u1fef\\u1ffd\\u1ffe\\u2044\\u2052\\u207a-\\u207c\\u208a-\\u208c\\u20a0-\\u20bf\\u2100\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211e-\\u2123\\u2125\\u2127\\u2129\\u212e\\u213a\\u213b\\u2140-\\u2144\\u214a-\\u214d\\u214f\\u218a\\u218b\\u2190-\\u2307\\u230c-\\u2328\\u232b-\\u2426\\u2440-\\u244a\\u249c-\\u24e9\\u2500-\\u2767\\u2794-\\u27c4\\u27c7-\\u27e5\\u27f0-\\u2982\\u2999-\\u29d7\\u29dc-\\u29fb\\u29fe-\\u2b73\\u2b76-\\u2b95\\u2b97-\\u2bff\\u2ce5-\\u2cea\\u2e50\\u2e51\\u2e80-\\u2e99\\u2e9b-\\u2ef3\\u2f00-\\u2fd5\\u2ff0-\\u2ffb\\u3004\\u3012\\u3013\\u3020\\u3036\\u3037\\u303e\\u303f\\u309b\\u309c\\u3190\\u3191\\u3196-\\u319f\\u31c0-\\u31e3\\u3200-\\u321e\\u322a-\\u3247\\u3250\\u3260-\\u327f\\u328a-\\u32b0\\u32c0-\\u33ff\\u4dc0-\\u4dff\\ua490-\\ua4c6\\ua700-\\ua716\\ua720\\ua721\\ua789\\ua78a\\ua828-\\ua82b\\ua836-\\ua839\\uaa77-\\uaa79\\uab5b\\uab6a\\uab6b\\ufb29\\ufbb2-\\ufbc1\\ufdfc\\ufdfd\\ufe62\\ufe64-\\ufe66\\ufe69\\uff04\\uff0b\\uff1c-\\uff1e\\uff3e\\uff40\\uff5c\\uff5e\\uffe0-\\uffe6\\uffe8-\\uffee\\ufffc\\ufffd]/\n    ]\n  })\n  Prism.languages.purs = Prism.languages.purescript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/purescript.js\n"));

/***/ })

}]);