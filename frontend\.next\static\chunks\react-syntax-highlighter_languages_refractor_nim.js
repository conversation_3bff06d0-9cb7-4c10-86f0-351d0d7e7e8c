"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nim"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nim.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nim.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nim\nnim.displayName = 'nim'\nnim.aliases = []\nfunction nim(Prism) {\n  Prism.languages.nim = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      // Double-quoted strings can be prefixed by an identifier (Generalized raw string literals)\n      pattern:\n        /(?:\\b(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+)?(?:\"\"\"[\\s\\S]*?\"\"\"(?!\")|\"(?:\\\\[\\s\\S]|\"\"|[^\"\\\\])*\")/,\n      greedy: true\n    },\n    char: {\n      // Character literals are handled specifically to prevent issues with numeric type suffixes\n      pattern: /'(?:\\\\(?:\\d+|x[\\da-fA-F]{0,2}|.)|[^'])'/,\n      greedy: true\n    },\n    function: {\n      pattern:\n        /(?:(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+|`[^`\\r\\n]+`)\\*?(?:\\[[^\\]]+\\])?(?=\\s*\\()/,\n      greedy: true,\n      inside: {\n        operator: /\\*$/\n      }\n    },\n    // We don't want to highlight operators (and anything really) inside backticks\n    identifier: {\n      pattern: /`[^`\\r\\n]+`/,\n      greedy: true,\n      inside: {\n        punctuation: /`/\n      }\n    },\n    // The negative look ahead prevents wrong highlighting of the .. operator\n    number:\n      /\\b(?:0[xXoObB][\\da-fA-F_]+|\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:[eE][+-]?\\d[\\d_]*)?)(?:'?[iuf]\\d*)?/,\n    keyword:\n      /\\b(?:addr|as|asm|atomic|bind|block|break|case|cast|concept|const|continue|converter|defer|discard|distinct|do|elif|else|end|enum|except|export|finally|for|from|func|generic|if|import|include|interface|iterator|let|macro|method|mixin|nil|object|out|proc|ptr|raise|ref|return|static|template|try|tuple|type|using|var|when|while|with|without|yield)\\b/,\n    operator: {\n      // Look behind and look ahead prevent wrong highlighting of punctuations [. .] {. .} (. .)\n      // but allow the slice operator .. to take precedence over them\n      // One can define his own operators in Nim so all combination of operators might be an operator.\n      pattern:\n        /(^|[({\\[](?=\\.\\.)|(?![({\\[]\\.).)(?:(?:[=+\\-*\\/<>@$~&%|!?^:\\\\]|\\.\\.|\\.(?![)}\\]]))+|\\b(?:and|div|in|is|isnot|mod|not|notin|of|or|shl|shr|xor)\\b)/m,\n      lookbehind: true\n    },\n    punctuation: /[({\\[]\\.|\\.[)}\\]]|[`(){}\\[\\],:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nim.js\n"));

/***/ })

}]);