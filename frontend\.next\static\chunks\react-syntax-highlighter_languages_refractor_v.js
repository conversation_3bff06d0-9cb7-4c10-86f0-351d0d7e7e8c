"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_v"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/v.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/v.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = v\nv.displayName = 'v'\nv.aliases = []\nfunction v(Prism) {\n  ;(function (Prism) {\n    var interpolationExpr = {\n      pattern: /[\\s\\S]+/,\n      inside: null\n    }\n    Prism.languages.v = Prism.languages.extend('clike', {\n      string: {\n        pattern: /r?([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        alias: 'quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[^{}]*\\}|\\w+(?:\\.\\w+(?:\\([^\\(\\)]*\\))?|\\[[^\\[\\]]+\\])*)/,\n            lookbehind: true,\n            inside: {\n              'interpolation-variable': {\n                pattern: /^\\$\\w[\\s\\S]*$/,\n                alias: 'variable'\n              },\n              'interpolation-punctuation': {\n                pattern: /^\\$\\{|\\}$/,\n                alias: 'punctuation'\n              },\n              'interpolation-expression': interpolationExpr\n            }\n          }\n        }\n      },\n      'class-name': {\n        pattern: /(\\b(?:enum|interface|struct|type)\\s+)(?:C\\.)?\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /(?:\\b(?:__global|as|asm|assert|atomic|break|chan|const|continue|defer|else|embed|enum|fn|for|go(?:to)?|if|import|in|interface|is|lock|match|module|mut|none|or|pub|return|rlock|select|shared|sizeof|static|struct|type(?:of)?|union|unsafe)|\\$(?:else|for|if)|#(?:flag|include))\\b/,\n      number:\n        /\\b(?:0x[a-f\\d]+(?:_[a-f\\d]+)*|0b[01]+(?:_[01]+)*|0o[0-7]+(?:_[0-7]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?)\\b/i,\n      operator:\n        /~|\\?|[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\.?/,\n      builtin:\n        /\\b(?:any(?:_float|_int)?|bool|byte(?:ptr)?|charptr|f(?:32|64)|i(?:8|16|64|128|nt)|rune|size_t|string|u(?:16|32|64|128)|voidptr)\\b/\n    })\n    interpolationExpr.inside = Prism.languages.v\n    Prism.languages.insertBefore('v', 'string', {\n      char: {\n        pattern: /`(?:\\\\`|\\\\?[^`]{1,2})`/,\n        // using {1,2} instead of `u` flag for compatibility\n        alias: 'rune'\n      }\n    })\n    Prism.languages.insertBefore('v', 'operator', {\n      attribute: {\n        pattern:\n          /(^[\\t ]*)\\[(?:deprecated|direct_array_access|flag|inline|live|ref_only|typedef|unsafe_fn|windows_stdcall)\\]/m,\n        lookbehind: true,\n        alias: 'annotation',\n        inside: {\n          punctuation: /[\\[\\]]/,\n          keyword: /\\w+/\n        }\n      },\n      generic: {\n        pattern: /<\\w+>(?=\\s*[\\)\\{])/,\n        inside: {\n          punctuation: /[<>]/,\n          'class-name': /\\w+/\n        }\n      }\n    })\n    Prism.languages.insertBefore('v', 'function', {\n      'generic-function': {\n        // e.g. foo<T>( ...\n        pattern: /\\b\\w+\\s*<\\w+>(?=\\()/,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<\\w+>/,\n            inside: Prism.languages.v.generic.inside\n          }\n        }\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/v.js\n"));

/***/ })

}]);