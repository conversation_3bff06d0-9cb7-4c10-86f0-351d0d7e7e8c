"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jsdoc"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = javadoclike\njavadoclike.displayName = 'javadoclike'\njavadoclike.aliases = []\nfunction javadoclike(Prism) {\n  ;(function (Prism) {\n    var javaDocLike = (Prism.languages.javadoclike = {\n      parameter: {\n        pattern:\n          /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    })\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment'\n      var grammar = Prism.languages[lang]\n      if (!grammar) {\n        return\n      }\n      var token = grammar[tokenName]\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {}\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        }\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition)\n        token = grammar[tokenName]\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        }\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            }\n          }\n          callback(token[i])\n        }\n      } else {\n        callback(token)\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages]\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {}\n          }\n          pattern.inside.rest = docLanguage\n        })\n      })\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    })\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2phdmFkb2NsaWtlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixFQUFFO0FBQzNCO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIsZ0NBQWdDLEVBQUUsaUJBQWlCO0FBQ25EO0FBQ0EsT0FBTztBQUNQLHVCQUF1QjtBQUN2QixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsV0FBVyxTQUFTLGlCQUFpQixXQUFXO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsT0FBTztBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlCQUFpQjtBQUNoQyxlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGphdmFkb2NsaWtlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGphdmFkb2NsaWtlXG5qYXZhZG9jbGlrZS5kaXNwbGF5TmFtZSA9ICdqYXZhZG9jbGlrZSdcbmphdmFkb2NsaWtlLmFsaWFzZXMgPSBbXVxuZnVuY3Rpb24gamF2YWRvY2xpa2UoUHJpc20pIHtcbiAgOyhmdW5jdGlvbiAoUHJpc20pIHtcbiAgICB2YXIgamF2YURvY0xpa2UgPSAoUHJpc20ubGFuZ3VhZ2VzLmphdmFkb2NsaWtlID0ge1xuICAgICAgcGFyYW1ldGVyOiB7XG4gICAgICAgIHBhdHRlcm46XG4gICAgICAgICAgLyheW1xcdCBdKig/OlxcL3szfXxcXCp8XFwvXFwqXFwqKVxccypAKD86YXJnfGFyZ3VtZW50c3xwYXJhbSlcXHMrKVxcdysvbSxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgICAgfSxcbiAgICAgIGtleXdvcmQ6IHtcbiAgICAgICAgLy8ga2V5d29yZHMgYXJlIHRoZSBmaXJzdCB3b3JkIGluIGEgbGluZSBwcmVjZWRlZCBiZSBhbiBgQGAgb3Igc3Vycm91bmRlZCBieSBjdXJseSBicmFjZXMuXG4gICAgICAgIC8vIEB3b3JkLCB7QHdvcmR9XG4gICAgICAgIHBhdHRlcm46IC8oXltcXHQgXSooPzpcXC97M318XFwqfFxcL1xcKlxcKilcXHMqfFxceylAW2Etel1bYS16QS1aLV0rXFxiL20sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgIH0sXG4gICAgICBwdW5jdHVhdGlvbjogL1t7fV0vXG4gICAgfSlcbiAgICAvKipcbiAgICAgKiBBZGRzIGRvYyBjb21tZW50IHN1cHBvcnQgdG8gdGhlIGdpdmVuIGxhbmd1YWdlIGFuZCBjYWxscyBhIGdpdmVuIGNhbGxiYWNrIG9uIGVhY2ggZG9jIGNvbW1lbnQgcGF0dGVybi5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBsYW5nIHRoZSBsYW5ndWFnZSBhZGQgZG9jIGNvbW1lbnQgc3VwcG9ydCB0by5cbiAgICAgKiBAcGFyYW0geyhwYXR0ZXJuOiB7aW5zaWRlOiB7cmVzdDogdW5kZWZpbmVkfX0pID0+IHZvaWR9IGNhbGxiYWNrIHRoZSBmdW5jdGlvbiBjYWxsZWQgd2l0aCBlYWNoIGRvYyBjb21tZW50IHBhdHRlcm4gYXMgYXJndW1lbnQuXG4gICAgICovXG4gICAgZnVuY3Rpb24gZG9jQ29tbWVudFN1cHBvcnQobGFuZywgY2FsbGJhY2spIHtcbiAgICAgIHZhciB0b2tlbk5hbWUgPSAnZG9jLWNvbW1lbnQnXG4gICAgICB2YXIgZ3JhbW1hciA9IFByaXNtLmxhbmd1YWdlc1tsYW5nXVxuICAgICAgaWYgKCFncmFtbWFyKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgdmFyIHRva2VuID0gZ3JhbW1hclt0b2tlbk5hbWVdXG4gICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgIC8vIGFkZCBkb2MgY29tbWVudDogLyoqICovXG4gICAgICAgIHZhciBkZWZpbml0aW9uID0ge31cbiAgICAgICAgZGVmaW5pdGlvblt0b2tlbk5hbWVdID0ge1xuICAgICAgICAgIHBhdHRlcm46IC8oXnxbXlxcXFxdKVxcL1xcKlxcKlteL11bXFxzXFxTXSo/KD86XFwqXFwvfCQpLyxcbiAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICAgIGFsaWFzOiAnY29tbWVudCdcbiAgICAgICAgfVxuICAgICAgICBncmFtbWFyID0gUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZShsYW5nLCAnY29tbWVudCcsIGRlZmluaXRpb24pXG4gICAgICAgIHRva2VuID0gZ3JhbW1hclt0b2tlbk5hbWVdXG4gICAgICB9XG4gICAgICBpZiAodG9rZW4gaW5zdGFuY2VvZiBSZWdFeHApIHtcbiAgICAgICAgLy8gY29udmVydCByZWdleCB0byBvYmplY3RcbiAgICAgICAgdG9rZW4gPSBncmFtbWFyW3Rva2VuTmFtZV0gPSB7XG4gICAgICAgICAgcGF0dGVybjogdG9rZW5cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKEFycmF5LmlzQXJyYXkodG9rZW4pKSB7XG4gICAgICAgIGZvciAodmFyIGkgPSAwLCBsID0gdG9rZW4ubGVuZ3RoOyBpIDwgbDsgaSsrKSB7XG4gICAgICAgICAgaWYgKHRva2VuW2ldIGluc3RhbmNlb2YgUmVnRXhwKSB7XG4gICAgICAgICAgICB0b2tlbltpXSA9IHtcbiAgICAgICAgICAgICAgcGF0dGVybjogdG9rZW5baV1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgY2FsbGJhY2sodG9rZW5baV0pXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNhbGxiYWNrKHRva2VuKVxuICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBBZGRzIGRvYy1jb21tZW50IHN1cHBvcnQgdG8gdGhlIGdpdmVuIGxhbmd1YWdlcyBmb3IgdGhlIGdpdmVuIGRvY3VtZW50YXRpb24gbGFuZ3VhZ2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ1tdfHN0cmluZ30gbGFuZ3VhZ2VzXG4gICAgICogQHBhcmFtIHtPYmplY3R9IGRvY0xhbmd1YWdlXG4gICAgICovXG4gICAgZnVuY3Rpb24gYWRkU3VwcG9ydChsYW5ndWFnZXMsIGRvY0xhbmd1YWdlKSB7XG4gICAgICBpZiAodHlwZW9mIGxhbmd1YWdlcyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgbGFuZ3VhZ2VzID0gW2xhbmd1YWdlc11cbiAgICAgIH1cbiAgICAgIGxhbmd1YWdlcy5mb3JFYWNoKGZ1bmN0aW9uIChsYW5nKSB7XG4gICAgICAgIGRvY0NvbW1lbnRTdXBwb3J0KGxhbmcsIGZ1bmN0aW9uIChwYXR0ZXJuKSB7XG4gICAgICAgICAgaWYgKCFwYXR0ZXJuLmluc2lkZSkge1xuICAgICAgICAgICAgcGF0dGVybi5pbnNpZGUgPSB7fVxuICAgICAgICAgIH1cbiAgICAgICAgICBwYXR0ZXJuLmluc2lkZS5yZXN0ID0gZG9jTGFuZ3VhZ2VcbiAgICAgICAgfSlcbiAgICAgIH0pXG4gICAgfVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShqYXZhRG9jTGlrZSwgJ2FkZFN1cHBvcnQnLCB7XG4gICAgICB2YWx1ZTogYWRkU3VwcG9ydFxuICAgIH0pXG4gICAgamF2YURvY0xpa2UuYWRkU3VwcG9ydChbJ2phdmEnLCAnamF2YXNjcmlwdCcsICdwaHAnXSwgamF2YURvY0xpa2UpXG4gIH0pKFByaXNtKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsdoc.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsdoc.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorJavadoclike = __webpack_require__(/*! ./javadoclike.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js\")\nvar refractorTypescript = __webpack_require__(/*! ./typescript.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/typescript.js\")\nmodule.exports = jsdoc\njsdoc.displayName = 'jsdoc'\njsdoc.aliases = []\nfunction jsdoc(Prism) {\n  Prism.register(refractorJavadoclike)\n  Prism.register(refractorTypescript)\n  ;(function (Prism) {\n    var javascript = Prism.languages.javascript\n    var type = /\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source\n    var parameterPrefix =\n      '(@(?:arg|argument|param|property)\\\\s+(?:' + type + '\\\\s+)?)'\n    Prism.languages.jsdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        // @param {string} foo - foo bar\n        pattern: RegExp(\n          parameterPrefix + /(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n    Prism.languages.insertBefore('jsdoc', 'keyword', {\n      'optional-parameter': {\n        // @param {string} [baz.foo=\"bar\"] foo bar\n        pattern: RegExp(\n          parameterPrefix +\n            /\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          parameter: {\n            pattern: /(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/,\n            lookbehind: true,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          code: {\n            pattern: /(=)[\\s\\S]*(?=\\]$)/,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          },\n          punctuation: /[=[\\]]/\n        }\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(\n              /<TYPE>/g,\n              function () {\n                return type\n              }\n            )\n          ),\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        {\n          pattern: RegExp('(@[a-z]+\\\\s+)' + type),\n          lookbehind: true,\n          inside: {\n            string: javascript.string,\n            number: javascript.number,\n            boolean: javascript.boolean,\n            keyword: Prism.languages.typescript.keyword,\n            operator: /=>|\\.\\.\\.|[&|?:*]/,\n            punctuation: /[.,;=<>{}()[\\]]/\n          }\n        }\n      ],\n      example: {\n        pattern:\n          /(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/,\n        lookbehind: true,\n        inside: {\n          code: {\n            pattern: /^([\\t ]*(?:\\*\\s*)?)\\S.*$/m,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          }\n        }\n      }\n    })\n    Prism.languages.javadoclike.addSupport('javascript', Prism.languages.jsdoc)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsdoc.js\n"));

/***/ })

}]);