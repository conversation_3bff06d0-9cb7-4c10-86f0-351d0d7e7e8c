"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_renpy"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/renpy.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/renpy.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = renpy\nrenpy.displayName = 'renpy'\nrenpy.aliases = ['rpy']\nfunction renpy(Prism) {\n  Prism.languages.renpy = {\n    comment: {\n      pattern: /(^|[^\\\\])#.+/,\n      lookbehind: true\n    },\n    string: {\n      pattern:\n        /(\"\"\"|''')[\\s\\S]+?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\])*\\2|(?:^#?(?:(?:[0-9a-fA-F]){3}|[0-9a-fA-F]{6})$)/m,\n      greedy: true\n    },\n    function: /\\b[a-z_]\\w*(?=\\()/i,\n    property:\n      /\\b(?:Update|UpdateVersion|action|activate_sound|adv_nvl_transition|after_load_transition|align|alpha|alt|anchor|antialias|area|auto|background|bar_invert|bar_resizing|bar_vertical|black_color|bold|bottom_bar|bottom_gutter|bottom_margin|bottom_padding|box_reverse|box_wrap|can_update|caret|child|color|crop|default_afm_enable|default_afm_time|default_fullscreen|default_text_cps|developer|directory_name|drag_handle|drag_joined|drag_name|drag_raise|draggable|dragged|drop_shadow|drop_shadow_color|droppable|dropped|easein|easeout|edgescroll|end_game_transition|end_splash_transition|enter_replay_transition|enter_sound|enter_transition|enter_yesno_transition|executable_name|exit_replay_transition|exit_sound|exit_transition|exit_yesno_transition|fadein|fadeout|first_indent|first_spacing|fit_first|focus|focus_mask|font|foreground|game_main_transition|get_installed_packages|google_play_key|google_play_salt|ground|has_music|has_sound|has_voice|height|help|hinting|hover|hover_background|hover_color|hover_sound|hovered|hyperlink_functions|idle|idle_color|image_style|include_update|insensitive|insensitive_background|insensitive_color|inside|intra_transition|italic|justify|kerning|keyboard_focus|language|layer_clipping|layers|layout|left_bar|left_gutter|left_margin|left_padding|length|line_leading|line_overlap_split|line_spacing|linear|main_game_transition|main_menu_music|maximum|min_width|minimum|minwidth|modal|mouse|mousewheel|name|narrator_menu|newline_indent|nvl_adv_transition|offset|order_reverse|outlines|overlay_functions|pos|position|prefix|radius|range|rest_indent|right_bar|right_gutter|right_margin|right_padding|rotate|rotate_pad|ruby_style|sample_sound|save_directory|say_attribute_transition|screen_height|screen_width|scrollbars|selected_hover|selected_hover_color|selected_idle|selected_idle_color|selected_insensitive|show_side_image|show_two_window|side_spacing|side_xpos|side_ypos|size|size_group|slow_cps|slow_cps_multiplier|spacing|strikethrough|subpixel|text_align|text_style|text_xpos|text_y_fudge|text_ypos|thumb|thumb_offset|thumb_shadow|thumbnail_height|thumbnail_width|time|top_bar|top_gutter|top_margin|top_padding|translations|underline|unscrollable|update|value|version|version_name|version_tuple|vertical|width|window_hide_transition|window_icon|window_left_padding|window_show_transition|window_title|windows_icon|xadjustment|xalign|xanchor|xanchoraround|xaround|xcenter|xfill|xinitial|xmargin|xmaximum|xminimum|xoffset|xofsset|xpadding|xpos|xsize|xzoom|yadjustment|yalign|yanchor|yanchoraround|yaround|ycenter|yfill|yinitial|ymargin|ymaximum|yminimum|yoffset|ypadding|ypos|ysize|ysizexysize|yzoom|zoom|zorder)\\b/,\n    tag: /\\b(?:bar|block|button|buttoscreenn|drag|draggroup|fixed|frame|grid|[hv]box|hotbar|hotspot|image|imagebutton|imagemap|input|key|label|menu|mm_menu_frame|mousearea|nvl|parallel|screen|self|side|tag|text|textbutton|timer|vbar|viewport|window)\\b|\\$/,\n    keyword:\n      /\\b(?:None|add|adjustment|alignaround|allow|angle|animation|around|as|assert|behind|box_layout|break|build|cache|call|center|changed|child_size|choice|circles|class|clear|clicked|clipping|clockwise|config|contains|continue|corner1|corner2|counterclockwise|def|default|define|del|delay|disabled|disabled_text|dissolve|elif|else|event|except|exclude|exec|expression|fade|finally|for|from|function|global|gm_root|has|hide|id|if|import|in|init|is|jump|knot|lambda|left|less_rounded|mm_root|movie|music|null|on|onlayer|pass|pause|persistent|play|print|python|queue|raise|random|renpy|repeat|return|right|rounded_window|scene|scope|set|show|slow|slow_abortable|slow_done|sound|stop|store|style|style_group|substitute|suffix|theme|transform|transform_anchor|transpose|try|ui|unhovered|updater|use|voice|while|widget|widget_hover|widget_selected|widget_text|yield)\\b/,\n    boolean: /\\b(?:[Ff]alse|[Tt]rue)\\b/,\n    number:\n      /(?:\\b(?:0[bo])?(?:(?:\\d|0x[\\da-f])[\\da-f]*(?:\\.\\d*)?)|\\B\\.\\d+)(?:e[+-]?\\d+)?j?/i,\n    operator:\n      /[-+%=]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]|\\b(?:and|at|not|or|with)\\b/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.rpy = Prism.languages.renpy\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/renpy.js\n"));

/***/ })

}]);