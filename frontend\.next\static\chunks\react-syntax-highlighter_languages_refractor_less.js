"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_less"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/less.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/less.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = less\nless.displayName = 'less'\nless.aliases = []\nfunction less(Prism) {\n  /* FIXME :\n:extend() is not handled specifically : its highlighting is buggy.\nMixin usage must be inside a ruleset to be highlighted.\nAt-rules (e.g. import) containing interpolations are buggy.\nDetached rulesets are highlighted as at-rules.\nA comment before a mixin usage prevents the latter to be properly highlighted.\n*/\n  Prism.languages.less = Prism.languages.extend('css', {\n    comment: [\n      /\\/\\*[\\s\\S]*?\\*\\//,\n      {\n        pattern: /(^|[^\\\\])\\/\\/.*/,\n        lookbehind: true\n      }\n    ],\n    atrule: {\n      pattern:\n        /@[\\w-](?:\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        punctuation: /[:()]/\n      }\n    },\n    // selectors and mixins are considered the same\n    selector: {\n      pattern:\n        /(?:@\\{[\\w-]+\\}|[^{};\\s@])(?:@\\{[\\w-]+\\}|\\((?:[^(){}]|\\([^(){}]*\\))*\\)|[^(){};@\\s]|\\s+(?!\\s))*?(?=\\s*\\{)/,\n      inside: {\n        // mixin parameters\n        variable: /@+[\\w-]+/\n      }\n    },\n    property: /(?:@\\{[\\w-]+\\}|[\\w-])+(?:\\+_?)?(?=\\s*:)/,\n    operator: /[+\\-*\\/]/\n  })\n  Prism.languages.insertBefore('less', 'property', {\n    variable: [\n      // Variable declaration (the colon must be consumed!)\n      {\n        pattern: /@[\\w-]+\\s*:/,\n        inside: {\n          punctuation: /:/\n        }\n      }, // Variable usage\n      /@@?[\\w-]+/\n    ],\n    'mixin-usage': {\n      pattern: /([{;]\\s*)[.#](?!\\d)[\\w-].*?(?=[(;])/,\n      lookbehind: true,\n      alias: 'function'\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/less.js\n"));

/***/ })

}]);