"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_scss"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scss.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scss.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = scss\nscss.displayName = 'scss'\nscss.aliases = []\nfunction scss(Prism) {\n  Prism.languages.scss = Prism.languages.extend('css', {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true\n    },\n    atrule: {\n      pattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n      inside: {\n        rule: /@[\\w-]+/ // See rest below\n      }\n    },\n    // url, compassified\n    url: /(?:[-a-z]+-)?url(?=\\()/i,\n    // CSS selector regex is not appropriate for Sass\n    // since there can be lot more things (var, @ directive, nesting..)\n    // a selector must start at the end of a property or after a brace (end of other rules or nesting)\n    // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n    // the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n    // can \"pass\" as a selector- e.g: proper#{$erty})\n    // this one was hard to do, so please be careful if you edit this one :)\n    selector: {\n      // Initial look-ahead is used to prevent matching of blank selectors\n      pattern:\n        /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n      inside: {\n        parent: {\n          pattern: /&/,\n          alias: 'important'\n        },\n        placeholder: /%[-\\w]+/,\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    },\n    property: {\n      pattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n      inside: {\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    }\n  })\n  Prism.languages.insertBefore('scss', 'atrule', {\n    keyword: [\n      /@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i,\n      {\n        pattern: /( )(?:from|through)(?= )/,\n        lookbehind: true\n      }\n    ]\n  })\n  Prism.languages.insertBefore('scss', 'important', {\n    // var and interpolated vars\n    variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n  })\n  Prism.languages.insertBefore('scss', 'function', {\n    'module-modifier': {\n      pattern: /\\b(?:as|hide|show|with)\\b/i,\n      alias: 'keyword'\n    },\n    placeholder: {\n      pattern: /%[-\\w]+/,\n      alias: 'selector'\n    },\n    statement: {\n      pattern: /\\B!(?:default|optional)\\b/i,\n      alias: 'keyword'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    operator: {\n      pattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n      lookbehind: true\n    }\n  })\n  Prism.languages.scss['atrule'].inside.rest = Prism.languages.scss\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL3Njc3MuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGlFQUFpRTtBQUNqRTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0VBQXdFLElBQUksR0FBRztBQUMvRSw4Q0FBOEMsTUFBTTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixhQUFhLG1CQUFtQixVQUFVLFVBQVUsS0FBSyxPQUFPLE1BQU0sS0FBSyxJQUFJO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsK0JBQStCLFVBQVU7QUFDekM7QUFDQSxLQUFLO0FBQ0w7QUFDQSxvQ0FBb0MsVUFBVTtBQUM5QztBQUNBLCtCQUErQixVQUFVO0FBQ3pDO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsMkJBQTJCLFVBQVU7QUFDckMsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFxzY3NzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNjc3NcbnNjc3MuZGlzcGxheU5hbWUgPSAnc2NzcydcbnNjc3MuYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBzY3NzKFByaXNtKSB7XG4gIFByaXNtLmxhbmd1YWdlcy5zY3NzID0gUHJpc20ubGFuZ3VhZ2VzLmV4dGVuZCgnY3NzJywge1xuICAgIGNvbW1lbnQ6IHtcbiAgICAgIHBhdHRlcm46IC8oXnxbXlxcXFxdKSg/OlxcL1xcKltcXHNcXFNdKj9cXCpcXC98XFwvXFwvLiopLyxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICB9LFxuICAgIGF0cnVsZToge1xuICAgICAgcGF0dGVybjogL0BbXFx3LV0oPzpcXChbXigpXStcXCl8W14oKVxcc118XFxzKyg/IVxccykpKj8oPz1cXHMrW3s7XSkvLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgIHJ1bGU6IC9AW1xcdy1dKy8gLy8gU2VlIHJlc3QgYmVsb3dcbiAgICAgIH1cbiAgICB9LFxuICAgIC8vIHVybCwgY29tcGFzc2lmaWVkXG4gICAgdXJsOiAvKD86Wy1hLXpdKy0pP3VybCg/PVxcKCkvaSxcbiAgICAvLyBDU1Mgc2VsZWN0b3IgcmVnZXggaXMgbm90IGFwcHJvcHJpYXRlIGZvciBTYXNzXG4gICAgLy8gc2luY2UgdGhlcmUgY2FuIGJlIGxvdCBtb3JlIHRoaW5ncyAodmFyLCBAIGRpcmVjdGl2ZSwgbmVzdGluZy4uKVxuICAgIC8vIGEgc2VsZWN0b3IgbXVzdCBzdGFydCBhdCB0aGUgZW5kIG9mIGEgcHJvcGVydHkgb3IgYWZ0ZXIgYSBicmFjZSAoZW5kIG9mIG90aGVyIHJ1bGVzIG9yIG5lc3RpbmcpXG4gICAgLy8gaXQgY2FuIGNvbnRhaW4gc29tZSBjaGFyYWN0ZXJzIHRoYXQgYXJlbid0IHVzZWQgZm9yIGRlZmluaW5nIHJ1bGVzIG9yIGVuZCBvZiBzZWxlY3RvciwgJiAocGFyZW50IHNlbGVjdG9yKSwgb3IgaW50ZXJwb2xhdGVkIHZhcmlhYmxlXG4gICAgLy8gdGhlIGVuZCBvZiBhIHNlbGVjdG9yIGlzIGZvdW5kIHdoZW4gdGhlcmUgaXMgbm8gcnVsZXMgaW4gaXQgKCB7fSBvciB7XFxzfSkgb3IgaWYgdGhlcmUgaXMgYSBwcm9wZXJ0eSAoYmVjYXVzZSBhbiBpbnRlcnBvbGF0ZWQgdmFyXG4gICAgLy8gY2FuIFwicGFzc1wiIGFzIGEgc2VsZWN0b3ItIGUuZzogcHJvcGVyI3skZXJ0eX0pXG4gICAgLy8gdGhpcyBvbmUgd2FzIGhhcmQgdG8gZG8sIHNvIHBsZWFzZSBiZSBjYXJlZnVsIGlmIHlvdSBlZGl0IHRoaXMgb25lIDopXG4gICAgc2VsZWN0b3I6IHtcbiAgICAgIC8vIEluaXRpYWwgbG9vay1haGVhZCBpcyB1c2VkIHRvIHByZXZlbnQgbWF0Y2hpbmcgb2YgYmxhbmsgc2VsZWN0b3JzXG4gICAgICBwYXR0ZXJuOlxuICAgICAgICAvKD89XFxTKVteQDt7fSgpXT8oPzpbXkA7e30oKVxcc118XFxzKyg/IVxccyl8I1xce1xcJFstXFx3XStcXH0pKyg/PVxccypcXHsoPzpcXH18XFxzfFtefV1bXjp7fV0qWzp7XVtefV0pKS8sXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgcGFyZW50OiB7XG4gICAgICAgICAgcGF0dGVybjogLyYvLFxuICAgICAgICAgIGFsaWFzOiAnaW1wb3J0YW50J1xuICAgICAgICB9LFxuICAgICAgICBwbGFjZWhvbGRlcjogLyVbLVxcd10rLyxcbiAgICAgICAgdmFyaWFibGU6IC9cXCRbLVxcd10rfCNcXHtcXCRbLVxcd10rXFx9L1xuICAgICAgfVxuICAgIH0sXG4gICAgcHJvcGVydHk6IHtcbiAgICAgIHBhdHRlcm46IC8oPzpbLVxcd118XFwkWy1cXHddfCNcXHtcXCRbLVxcd10rXFx9KSsoPz1cXHMqOikvLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgIHZhcmlhYmxlOiAvXFwkWy1cXHddK3wjXFx7XFwkWy1cXHddK1xcfS9cbiAgICAgIH1cbiAgICB9XG4gIH0pXG4gIFByaXNtLmxhbmd1YWdlcy5pbnNlcnRCZWZvcmUoJ3Njc3MnLCAnYXRydWxlJywge1xuICAgIGtleXdvcmQ6IFtcbiAgICAgIC9AKD86Y29udGVudHxkZWJ1Z3xlYWNofGVsc2UoPzogaWYpP3xleHRlbmR8Zm9yfGZvcndhcmR8ZnVuY3Rpb258aWZ8aW1wb3J0fGluY2x1ZGV8bWl4aW58cmV0dXJufHVzZXx3YXJufHdoaWxlKVxcYi9pLFxuICAgICAge1xuICAgICAgICBwYXR0ZXJuOiAvKCApKD86ZnJvbXx0aHJvdWdoKSg/PSApLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgICAgfVxuICAgIF1cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnc2NzcycsICdpbXBvcnRhbnQnLCB7XG4gICAgLy8gdmFyIGFuZCBpbnRlcnBvbGF0ZWQgdmFyc1xuICAgIHZhcmlhYmxlOiAvXFwkWy1cXHddK3wjXFx7XFwkWy1cXHddK1xcfS9cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnc2NzcycsICdmdW5jdGlvbicsIHtcbiAgICAnbW9kdWxlLW1vZGlmaWVyJzoge1xuICAgICAgcGF0dGVybjogL1xcYig/OmFzfGhpZGV8c2hvd3x3aXRoKVxcYi9pLFxuICAgICAgYWxpYXM6ICdrZXl3b3JkJ1xuICAgIH0sXG4gICAgcGxhY2Vob2xkZXI6IHtcbiAgICAgIHBhdHRlcm46IC8lWy1cXHddKy8sXG4gICAgICBhbGlhczogJ3NlbGVjdG9yJ1xuICAgIH0sXG4gICAgc3RhdGVtZW50OiB7XG4gICAgICBwYXR0ZXJuOiAvXFxCISg/OmRlZmF1bHR8b3B0aW9uYWwpXFxiL2ksXG4gICAgICBhbGlhczogJ2tleXdvcmQnXG4gICAgfSxcbiAgICBib29sZWFuOiAvXFxiKD86ZmFsc2V8dHJ1ZSlcXGIvLFxuICAgIG51bGw6IHtcbiAgICAgIHBhdHRlcm46IC9cXGJudWxsXFxiLyxcbiAgICAgIGFsaWFzOiAna2V5d29yZCdcbiAgICB9LFxuICAgIG9wZXJhdG9yOiB7XG4gICAgICBwYXR0ZXJuOiAvKFxccykoPzpbLSsqXFwvJV18Wz0hXT18PD0/fD49P3xhbmR8bm90fG9yKSg/PVxccykvLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgIH1cbiAgfSlcbiAgUHJpc20ubGFuZ3VhZ2VzLnNjc3NbJ2F0cnVsZSddLmluc2lkZS5yZXN0ID0gUHJpc20ubGFuZ3VhZ2VzLnNjc3Ncbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/scss.js\n"));

/***/ })

}]);