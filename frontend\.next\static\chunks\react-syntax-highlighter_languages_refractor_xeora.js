"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_xeora"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xeora.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xeora.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = xeora\nxeora.displayName = 'xeora'\nxeora.aliases = ['xeoracube']\nfunction xeora(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xeora = Prism.languages.extend('markup', {\n      constant: {\n        pattern: /\\$(?:DomainContents|PageRenderDuration)\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$/\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$@?(?:#+|[-+*~=^])?[\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$.]/\n          },\n          operator: {\n            pattern: /#+|[-+*~=^@]/\n          }\n        }\n      },\n      'function-inline': {\n        pattern:\n          /\\$F:[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\$/,\n        inside: {\n          variable: {\n            pattern: /(?:[,|])@?(?:#+|[-+*~=^])?[\\w.]+/,\n            inside: {\n              punctuation: {\n                pattern: /[,.|]/\n              },\n              operator: {\n                pattern: /#+|[-+*~=^@]/\n              }\n            }\n          },\n          punctuation: {\n            pattern: /\\$\\w:|[$:?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'function-block': {\n        pattern:\n          /\\$XF:\\{[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\}:XF\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$:{}?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-inline': {\n        pattern: /\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\/\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-open': {\n        pattern:\n          /\\$\\w+:\\{|\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\w.]+:\\{(?:![A-Z]+)?/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          },\n          attribute: {\n            pattern: /![A-Z]+$/,\n            inside: {\n              punctuation: {\n                pattern: /!/\n              }\n            },\n            alias: 'keyword'\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-separator': {\n        pattern: /\\}:[-\\w.]+:\\{/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-close': {\n        pattern: /\\}:[-\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}$]/\n          }\n        },\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        variable: Prism.languages.xeora['function-inline'].inside['variable']\n      },\n      Prism.languages.xeora['function-block']\n    )\n    Prism.languages.xeoracube = Prism.languages.xeora\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/xeora.js\n"));

/***/ })

}]);