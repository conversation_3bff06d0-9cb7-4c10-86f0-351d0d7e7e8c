"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_sml"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sml.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sml.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = sml\nsml.displayName = 'sml'\nsml.aliases = ['smlnj']\nfunction sml(Prism) {\n  // https://smlfamily.github.io/sml97-defn.pdf\n  // https://people.mpi-sws.org/~rossberg/sml.html\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstype|and|andalso|as|case|datatype|do|else|end|eqtype|exception|fn|fun|functor|handle|if|in|include|infix|infixr|let|local|nonfix|of|op|open|orelse|raise|rec|sharing|sig|signature|struct|structure|then|type|val|where|while|with|withtype)\\b/i\n    Prism.languages.sml = {\n      // allow one level of nesting\n      comment:\n        /\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*)|\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*))*\\*\\))*\\*\\)/,\n      string: {\n        pattern: /#?\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': [\n        {\n          // This is only an approximation since the real grammar is context-free\n          //\n          // Why the main loop so complex?\n          // The main loop is approximately the same as /(?:\\s*(?:[*,]|->)\\s*<TERMINAL>)*/ which is, obviously, a lot\n          // simpler. The difference is that if a comma is the last iteration of the loop, then the terminal must be\n          // followed by a long identifier.\n          pattern: RegExp(\n            /((?:^|[^:]):\\s*)<TERMINAL>(?:\\s*(?:(?:\\*|->)\\s*<TERMINAL>|,\\s*<TERMINAL>(?:(?=<NOT-LAST>)|(?!<NOT-LAST>)\\s+<LONG-ID>)))*/.source\n              .replace(/<NOT-LAST>/g, function () {\n                return /\\s*(?:[*,]|->)/.source\n              })\n              .replace(/<TERMINAL>/g, function () {\n                return /(?:'[\\w']*|<LONG-ID>|\\((?:[^()]|\\([^()]*\\))*\\)|\\{(?:[^{}]|\\{[^{}]*\\})*\\})(?:\\s+<LONG-ID>)*/\n                  .source\n              })\n              .replace(/<LONG-ID>/g, function () {\n                return /(?!<KEYWORD>)[a-z\\d_][\\w'.]*/.source\n              })\n              .replace(/<KEYWORD>/g, function () {\n                return keywords.source\n              }),\n            'i'\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        {\n          pattern:\n            /((?:^|[^\\w'])(?:datatype|exception|functor|signature|structure|type)\\s+)[a-z_][\\w'.]*/i,\n          lookbehind: true\n        }\n      ],\n      function: {\n        pattern: /((?:^|[^\\w'])fun\\s+)[a-z_][\\w'.]*/i,\n        lookbehind: true\n      },\n      keyword: keywords,\n      variable: {\n        pattern: /(^|[^\\w'])'[\\w']*/,\n        lookbehind: true\n      },\n      number: /~?\\b(?:\\d+(?:\\.\\d+)?(?:e~?\\d+)?|0x[\\da-f]+)\\b/i,\n      word: {\n        pattern: /\\b0w(?:\\d+|x[\\da-f]+)\\b/i,\n        alias: 'constant'\n      },\n      boolean: /\\b(?:false|true)\\b/i,\n      operator: /\\.\\.\\.|:[>=:]|=>?|->|[<>]=?|[!+\\-*/^#|@~]/,\n      punctuation: /[(){}\\[\\].:,;]/\n    }\n    Prism.languages.sml['class-name'][0].inside = Prism.languages.sml\n    Prism.languages.smlnj = Prism.languages.sml\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sml.js\n"));

/***/ })

}]);