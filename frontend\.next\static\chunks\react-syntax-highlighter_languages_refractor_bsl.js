"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_bsl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bsl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bsl.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = bsl\nbsl.displayName = 'bsl'\nbsl.aliases = []\nfunction bsl(Prism) {\n  /* eslint-disable no-misleading-character-class */\n  // 1C:Enterprise\n  // https://github.com/Diversus23/\n  //\n  Prism.languages.bsl = {\n    comment: /\\/\\/.*/,\n    string: [\n      // Строки\n      // Strings\n      {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      }, // Дата и время\n      // Date & time\n      {\n        pattern: /'(?:[^'\\r\\n\\\\]|\\\\.)*'/\n      }\n    ],\n    keyword: [\n      {\n        // RU\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:пока|для|новый|прервать|попытка|исключение|вызватьисключение|иначе|конецпопытки|неопределено|функция|перем|возврат|конецфункции|если|иначеесли|процедура|конецпроцедуры|тогда|знач|экспорт|конецесли|из|каждого|истина|ложь|по|цикл|конеццикла|выполнить)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      },\n      {\n        // EN\n        pattern:\n          /\\b(?:break|do|each|else|elseif|enddo|endfunction|endif|endprocedure|endtry|except|execute|export|false|for|function|if|in|new|null|procedure|raise|return|then|to|true|try|undefined|val|var|while)\\b/i\n      }\n    ],\n    number: {\n      pattern:\n        /(^(?=\\d)|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:E[+-]?\\d+)?/i,\n      lookbehind: true\n    },\n    operator: [\n      /[<>+\\-*/]=?|[%=]/, // RU\n      {\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:и|или|не)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      }, // EN\n      {\n        pattern: /\\b(?:and|not|or)\\b/i\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/,\n    directive: [\n      // Теги препроцессора вида &Клиент, &Сервер, ...\n      // Preprocessor tags of the type &Client, &Server, ...\n      {\n        pattern: /^([ \\t]*)&.*/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }, // Инструкции препроцессора вида:\n      // #Если Сервер Тогда\n      // ...\n      // #КонецЕсли\n      // Preprocessor instructions of the form:\n      // #If Server Then\n      // ...\n      // #EndIf\n      {\n        pattern: /^([ \\t]*)#.*/gm,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }\n    ]\n  }\n  Prism.languages.oscript = Prism.languages['bsl']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bsl.js\n"));

/***/ })

}]);