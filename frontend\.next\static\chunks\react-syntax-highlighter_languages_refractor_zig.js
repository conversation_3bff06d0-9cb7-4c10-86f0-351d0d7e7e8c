"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_zig"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/zig.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/zig.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = zig\nzig.displayName = 'zig'\nzig.aliases = []\nfunction zig(Prism) {\n  ;(function (Prism) {\n    function literal(str) {\n      return function () {\n        return str\n      }\n    }\n    var keyword =\n      /\\b(?:align|allowzero|and|anyframe|anytype|asm|async|await|break|cancel|catch|comptime|const|continue|defer|else|enum|errdefer|error|export|extern|fn|for|if|inline|linksection|nakedcc|noalias|nosuspend|null|or|orelse|packed|promise|pub|resume|return|stdcallcc|struct|suspend|switch|test|threadlocal|try|undefined|union|unreachable|usingnamespace|var|volatile|while)\\b/\n    var IDENTIFIER = '\\\\b(?!' + keyword.source + ')(?!\\\\d)\\\\w+\\\\b'\n    var ALIGN = /align\\s*\\((?:[^()]|\\([^()]*\\))*\\)/.source\n    var PREFIX_TYPE_OP =\n      /(?:\\?|\\bpromise->|(?:\\[[^[\\]]*\\]|\\*(?!\\*)|\\*\\*)(?:\\s*<ALIGN>|\\s*const\\b|\\s*volatile\\b|\\s*allowzero\\b)*)/.source.replace(\n        /<ALIGN>/g,\n        literal(ALIGN)\n      )\n    var SUFFIX_EXPR =\n      /(?:\\bpromise\\b|(?:\\berror\\.)?<ID>(?:\\.<ID>)*(?!\\s+<ID>))/.source.replace(\n        /<ID>/g,\n        literal(IDENTIFIER)\n      )\n    var TYPE =\n      '(?!\\\\s)(?:!?\\\\s*(?:' + PREFIX_TYPE_OP + '\\\\s*)*' + SUFFIX_EXPR + ')+'\n    /*\n     * A simplified grammar for Zig compile time type literals:\n     *\n     * TypeExpr = ( \"!\"? PREFIX_TYPE_OP* SUFFIX_EXPR )+\n     *\n     * SUFFIX_EXPR = ( \\b \"promise\" \\b | ( \\b \"error\" \".\" )? IDENTIFIER ( \".\" IDENTIFIER )* (?! \\s+ IDENTIFIER ) )\n     *\n     * PREFIX_TYPE_OP = \"?\"\n     *                | \\b \"promise\" \"->\"\n     *                | ( \"[\" [^\\[\\]]* \"]\" | \"*\" | \"**\" ) ( ALIGN | \"const\" \\b | \"volatile\" \\b | \"allowzero\" \\b )*\n     *\n     * ALIGN = \"align\" \"(\" ( [^()] | \"(\" [^()]* \")\" )* \")\"\n     *\n     * IDENTIFIER = \\b (?! KEYWORD ) [a-zA-Z_] \\w* \\b\n     *\n     */\n    Prism.languages.zig = {\n      comment: [\n        {\n          pattern: /\\/\\/[/!].*/,\n          alias: 'doc-comment'\n        },\n        /\\/{2}.*/\n      ],\n      string: [\n        {\n          // \"string\" and c\"string\"\n          pattern: /(^|[^\\\\@])c?\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // multiline strings and c-strings\n          pattern: /([\\r\\n])([ \\t]+c?\\\\{2}).*(?:(?:\\r\\n?|\\n)\\2.*)*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      char: {\n        // characters 'a', '\\n', '\\xFF', '\\u{10FFFF}'\n        pattern:\n          /(^|[^\\\\])'(?:[^'\\\\\\r\\n]|[\\uD800-\\uDFFF]{2}|\\\\(?:.|x[a-fA-F\\d]{2}|u\\{[a-fA-F\\d]{1,6}\\}))'/,\n        lookbehind: true,\n        greedy: true\n      },\n      builtin: /\\B@(?!\\d)\\w+(?=\\s*\\()/,\n      label: {\n        pattern:\n          /(\\b(?:break|continue)\\s*:\\s*)\\w+\\b|\\b(?!\\d)\\w+\\b(?=\\s*:\\s*(?:\\{|while\\b))/,\n        lookbehind: true\n      },\n      'class-name': [\n        // const Foo = struct {};\n        /\\b(?!\\d)\\w+(?=\\s*=\\s*(?:(?:extern|packed)\\s+)?(?:enum|struct|union)\\s*[({])/,\n        {\n          // const x: i32 = 9;\n          // var x: Bar;\n          // fn foo(x: bool, y: f32) void {}\n          pattern: RegExp(\n            /(:\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?[=;,)])|<TYPE>(?=\\s*(?:<ALIGN>\\s*)?\\{)/.source\n              .replace(/<TYPE>/g, literal(TYPE))\n              .replace(/<ALIGN>/g, literal(ALIGN))\n          ),\n          lookbehind: true,\n          inside: null // see below\n        },\n        {\n          // extern fn foo(x: f64) f64; (optional alignment)\n          pattern: RegExp(\n            /(\\)\\s*)<TYPE>(?=\\s*(?:<ALIGN>\\s*)?;)/.source\n              .replace(/<TYPE>/g, literal(TYPE))\n              .replace(/<ALIGN>/g, literal(ALIGN))\n          ),\n          lookbehind: true,\n          inside: null // see below\n        }\n      ],\n      'builtin-type': {\n        pattern:\n          /\\b(?:anyerror|bool|c_u?(?:int|long|longlong|short)|c_longdouble|c_void|comptime_(?:float|int)|f(?:16|32|64|128)|[iu](?:8|16|32|64|128|size)|noreturn|type|void)\\b/,\n        alias: 'keyword'\n      },\n      keyword: keyword,\n      function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n      number:\n        /\\b(?:0b[01]+|0o[0-7]+|0x[a-fA-F\\d]+(?:\\.[a-fA-F\\d]*)?(?:[pP][+-]?[a-fA-F\\d]+)?|\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator:\n        /\\.[*?]|\\.{2,3}|[-=]>|\\*\\*|\\+\\+|\\|\\||(?:<<|>>|[-+*]%|[-+*/%^&|<>!=])=?|[?~]/,\n      punctuation: /[.:,;(){}[\\]]/\n    }\n    Prism.languages.zig['class-name'].forEach(function (obj) {\n      if (obj.inside === null) {\n        obj.inside = Prism.languages.zig\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/zig.js\n"));

/***/ })

}]);