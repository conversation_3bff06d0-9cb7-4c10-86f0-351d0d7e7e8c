"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_turtle"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/turtle.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/turtle.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = turtle\nturtle.displayName = 'turtle'\nturtle.aliases = []\nfunction turtle(Prism) {\n  Prism.languages.turtle = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'multiline-string': {\n      pattern:\n        /\"\"\"(?:(?:\"\"?)?(?:[^\"\\\\]|\\\\.))*\"\"\"|'''(?:(?:''?)?(?:[^'\\\\]|\\\\.))*'''/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        comment: /#.*/\n      }\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"\\r\\n]|\\\\.)*\"|'(?:[^\\\\'\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    url: {\n      pattern:\n        /<(?:[^\\x00-\\x20<>\"{}|^`\\\\]|\\\\(?:u[\\da-fA-F]{4}|U[\\da-fA-F]{8}))*>/,\n      greedy: true,\n      inside: {\n        punctuation: /[<>]/\n      }\n    },\n    function: {\n      pattern:\n        /(?:(?![-.\\d\\xB7])[-.\\w\\xB7\\xC0-\\uFFFD]+)?:(?:(?![-.])(?:[-.:\\w\\xC0-\\uFFFD]|%[\\da-f]{2}|\\\\.)+)?/i,\n      inside: {\n        'local-name': {\n          pattern: /([^:]*:)[\\s\\S]+/,\n          lookbehind: true\n        },\n        prefix: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    },\n    number: /[+-]?\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}.,;()[\\]]|\\^\\^/,\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: [/(?:\\ba|@prefix|@base)\\b|=/, /\\b(?:base|graph|prefix)\\b/i],\n    tag: {\n      pattern: /@[a-z]+(?:-[a-z\\d]+)*/i,\n      inside: {\n        punctuation: /@/\n      }\n    }\n  }\n  Prism.languages.trig = Prism.languages['turtle']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/turtle.js\n"));

/***/ })

}]);