"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_tcl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tcl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tcl.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = tcl\ntcl.displayName = 'tcl'\ntcl.aliases = []\nfunction tcl(Prism) {\n  Prism.languages.tcl = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(\\$)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\$)\\{[^}]+\\}/,\n        lookbehind: true\n      },\n      {\n        pattern: /(^[\\t ]*set[ \\t]+)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/m,\n        lookbehind: true\n      }\n    ],\n    function: {\n      pattern: /(^[\\t ]*proc[ \\t]+)\\S+/m,\n      lookbehind: true\n    },\n    builtin: [\n      {\n        pattern:\n          /(^[\\t ]*)(?:break|class|continue|error|eval|exit|for|foreach|if|proc|return|switch|while)\\b/m,\n        lookbehind: true\n      },\n      /\\b(?:else|elseif)\\b/\n    ],\n    scope: {\n      pattern: /(^[\\t ]*)(?:global|upvar|variable)\\b/m,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    keyword: {\n      pattern:\n        /(^[\\t ]*|\\[)(?:Safe_Base|Tcl|after|append|apply|array|auto_(?:execok|import|load|mkindex|qualify|reset)|automkindex_old|bgerror|binary|catch|cd|chan|clock|close|concat|dde|dict|encoding|eof|exec|expr|fblocked|fconfigure|fcopy|file(?:event|name)?|flush|gets|glob|history|http|incr|info|interp|join|lappend|lassign|lindex|linsert|list|llength|load|lrange|lrepeat|lreplace|lreverse|lsearch|lset|lsort|math(?:func|op)|memory|msgcat|namespace|open|package|parray|pid|pkg_mkIndex|platform|puts|pwd|re_syntax|read|refchan|regexp|registry|regsub|rename|scan|seek|set|socket|source|split|string|subst|tcl(?:_endOfWord|_findLibrary|startOf(?:Next|Previous)Word|test|vars|wordBreak(?:After|Before))|tell|time|tm|trace|unknown|unload|unset|update|uplevel|vwait)\\b/m,\n      lookbehind: true\n    },\n    operator:\n      /!=?|\\*\\*?|==|&&?|\\|\\|?|<[=<]?|>[=>]?|[-+~\\/%?^]|\\b(?:eq|in|ne|ni)\\b/,\n    punctuation: /[{}()\\[\\]]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tcl.js\n"));

/***/ })

}]);