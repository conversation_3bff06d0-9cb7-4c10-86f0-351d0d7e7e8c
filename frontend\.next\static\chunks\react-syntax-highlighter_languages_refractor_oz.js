"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_oz"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/oz.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/oz.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = oz\noz.displayName = 'oz'\noz.aliases = []\nfunction oz(Prism) {\n  Prism.languages.oz = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|%.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    atom: {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      alias: 'builtin'\n    },\n    keyword:\n      /\\$|\\[\\]|\\b(?:_|at|attr|case|catch|choice|class|cond|declare|define|dis|else(?:case|if)?|end|export|fail|false|feat|finally|from|fun|functor|if|import|in|local|lock|meth|nil|not|of|or|prepare|proc|prop|raise|require|self|skip|then|thread|true|try|unit)\\b/,\n    function: [\n      /\\b[a-z][A-Za-z\\d]*(?=\\()/,\n      {\n        pattern: /(\\{)[A-Z][A-Za-z\\d]*\\b/,\n        lookbehind: true\n      }\n    ],\n    number:\n      /\\b(?:0[bx][\\da-f]+|\\d+(?:\\.\\d*)?(?:e~?\\d+)?)\\b|&(?:[^\\\\]|\\\\(?:\\d{3}|.))/i,\n    variable: /`(?:[^`\\\\]|\\\\.)+`/,\n    'attr-name': /\\b\\w+(?=[ \\t]*:(?![:=]))/,\n    operator:\n      /:(?:=|::?)|<[-:=]?|=(?:=|<?:?)|>=?:?|\\\\=:?|!!?|[|#+\\-*\\/,~^@]|\\b(?:andthen|div|mod|orelse)\\b/,\n    punctuation: /[\\[\\](){}.:;?]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/oz.js\n"));

/***/ })

}]);