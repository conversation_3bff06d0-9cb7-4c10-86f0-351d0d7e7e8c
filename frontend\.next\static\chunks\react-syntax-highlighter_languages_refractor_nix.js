"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nix"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nix.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nix.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nix\nnix.displayName = 'nix'\nnix.aliases = []\nfunction nix(Prism) {\n  Prism.languages.nix = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|''(?:(?!'')[\\s\\S]|''(?:'|\\\\|\\$\\{))*''/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // The lookbehind ensures the ${} is not preceded by \\ or ''\n          pattern: /(^|(?:^|(?!'').)[^\\\\])\\$\\{(?:[^{}]|\\{[^}]*\\})*\\}/,\n          lookbehind: true,\n          inside: null // see below\n        }\n      }\n    },\n    url: [\n      /\\b(?:[a-z]{3,7}:\\/\\/)[\\w\\-+%~\\/.:#=?&]+/,\n      {\n        pattern:\n          /([^\\/])(?:[\\w\\-+%~.:#=?&]*(?!\\/\\/)[\\w\\-+%~\\/.:#=?&])?(?!\\/\\/)\\/[\\w\\-+%~\\/.:#=?&]*/,\n        lookbehind: true\n      }\n    ],\n    antiquotation: {\n      pattern: /\\$(?=\\{)/,\n      alias: 'important'\n    },\n    number: /\\b\\d+\\b/,\n    keyword: /\\b(?:assert|builtins|else|if|in|inherit|let|null|or|then|with)\\b/,\n    function:\n      /\\b(?:abort|add|all|any|attrNames|attrValues|baseNameOf|compareVersions|concatLists|currentSystem|deepSeq|derivation|dirOf|div|elem(?:At)?|fetch(?:Tarball|url)|filter(?:Source)?|fromJSON|genList|getAttr|getEnv|hasAttr|hashString|head|import|intersectAttrs|is(?:Attrs|Bool|Function|Int|List|Null|String)|length|lessThan|listToAttrs|map|mul|parseDrvName|pathExists|read(?:Dir|File)|removeAttrs|replaceStrings|seq|sort|stringLength|sub(?:string)?|tail|throw|to(?:File|JSON|Path|String|XML)|trace|typeOf)\\b|\\bfoldl'\\B/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /[=!<>]=?|\\+\\+?|\\|\\||&&|\\/\\/|->?|[?@]/,\n    punctuation: /[{}()[\\].,:;]/\n  }\n  Prism.languages.nix.string.inside.interpolation.inside = Prism.languages.nix\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL25peC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsdUVBQXVFO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1Qyw4Q0FBOEMsT0FBTyxJQUFJLEdBQUcsSUFBSSxJQUFJO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0Esa0JBQWtCLElBQUk7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLFNBQVM7QUFDOUI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXG5peC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBuaXhcbm5peC5kaXNwbGF5TmFtZSA9ICduaXgnXG5uaXguYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBuaXgoUHJpc20pIHtcbiAgUHJpc20ubGFuZ3VhZ2VzLm5peCA9IHtcbiAgICBjb21tZW50OiB7XG4gICAgICBwYXR0ZXJuOiAvXFwvXFwqW1xcc1xcU10qP1xcKlxcL3wjLiovLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBzdHJpbmc6IHtcbiAgICAgIHBhdHRlcm46IC9cIig/OlteXCJcXFxcXXxcXFxcW1xcc1xcU10pKlwifCcnKD86KD8hJycpW1xcc1xcU118JycoPzonfFxcXFx8XFwkXFx7KSkqJycvLFxuICAgICAgZ3JlZWR5OiB0cnVlLFxuICAgICAgaW5zaWRlOiB7XG4gICAgICAgIGludGVycG9sYXRpb246IHtcbiAgICAgICAgICAvLyBUaGUgbG9va2JlaGluZCBlbnN1cmVzIHRoZSAke30gaXMgbm90IHByZWNlZGVkIGJ5IFxcIG9yICcnXG4gICAgICAgICAgcGF0dGVybjogLyhefCg/Ol58KD8hJycpLilbXlxcXFxdKVxcJFxceyg/Oltee31dfFxce1tefV0qXFx9KSpcXH0vLFxuICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgICAgaW5zaWRlOiBudWxsIC8vIHNlZSBiZWxvd1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICB1cmw6IFtcbiAgICAgIC9cXGIoPzpbYS16XXszLDd9OlxcL1xcLylbXFx3XFwtKyV+XFwvLjojPT8mXSsvLFxuICAgICAge1xuICAgICAgICBwYXR0ZXJuOlxuICAgICAgICAgIC8oW15cXC9dKSg/OltcXHdcXC0rJX4uOiM9PyZdKig/IVxcL1xcLylbXFx3XFwtKyV+XFwvLjojPT8mXSk/KD8hXFwvXFwvKVxcL1tcXHdcXC0rJX5cXC8uOiM9PyZdKi8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgIH1cbiAgICBdLFxuICAgIGFudGlxdW90YXRpb246IHtcbiAgICAgIHBhdHRlcm46IC9cXCQoPz1cXHspLyxcbiAgICAgIGFsaWFzOiAnaW1wb3J0YW50J1xuICAgIH0sXG4gICAgbnVtYmVyOiAvXFxiXFxkK1xcYi8sXG4gICAga2V5d29yZDogL1xcYig/OmFzc2VydHxidWlsdGluc3xlbHNlfGlmfGlufGluaGVyaXR8bGV0fG51bGx8b3J8dGhlbnx3aXRoKVxcYi8sXG4gICAgZnVuY3Rpb246XG4gICAgICAvXFxiKD86YWJvcnR8YWRkfGFsbHxhbnl8YXR0ck5hbWVzfGF0dHJWYWx1ZXN8YmFzZU5hbWVPZnxjb21wYXJlVmVyc2lvbnN8Y29uY2F0TGlzdHN8Y3VycmVudFN5c3RlbXxkZWVwU2VxfGRlcml2YXRpb258ZGlyT2Z8ZGl2fGVsZW0oPzpBdCk/fGZldGNoKD86VGFyYmFsbHx1cmwpfGZpbHRlcig/OlNvdXJjZSk/fGZyb21KU09OfGdlbkxpc3R8Z2V0QXR0cnxnZXRFbnZ8aGFzQXR0cnxoYXNoU3RyaW5nfGhlYWR8aW1wb3J0fGludGVyc2VjdEF0dHJzfGlzKD86QXR0cnN8Qm9vbHxGdW5jdGlvbnxJbnR8TGlzdHxOdWxsfFN0cmluZyl8bGVuZ3RofGxlc3NUaGFufGxpc3RUb0F0dHJzfG1hcHxtdWx8cGFyc2VEcnZOYW1lfHBhdGhFeGlzdHN8cmVhZCg/OkRpcnxGaWxlKXxyZW1vdmVBdHRyc3xyZXBsYWNlU3RyaW5nc3xzZXF8c29ydHxzdHJpbmdMZW5ndGh8c3ViKD86c3RyaW5nKT98dGFpbHx0aHJvd3x0byg/OkZpbGV8SlNPTnxQYXRofFN0cmluZ3xYTUwpfHRyYWNlfHR5cGVPZilcXGJ8XFxiZm9sZGwnXFxCLyxcbiAgICBib29sZWFuOiAvXFxiKD86ZmFsc2V8dHJ1ZSlcXGIvLFxuICAgIG9wZXJhdG9yOiAvWz0hPD5dPT98XFwrXFwrP3xcXHxcXHx8JiZ8XFwvXFwvfC0+P3xbP0BdLyxcbiAgICBwdW5jdHVhdGlvbjogL1t7fSgpW1xcXS4sOjtdL1xuICB9XG4gIFByaXNtLmxhbmd1YWdlcy5uaXguc3RyaW5nLmluc2lkZS5pbnRlcnBvbGF0aW9uLmluc2lkZSA9IFByaXNtLmxhbmd1YWdlcy5uaXhcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nix.js\n"));

/***/ })

}]);