"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_mizar"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mizar.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mizar.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = mizar\nmizar.displayName = 'mizar'\nmizar.aliases = []\nfunction mizar(Prism) {\n  Prism.languages.mizar = {\n    comment: /::.+/,\n    keyword:\n      /@proof\\b|\\b(?:according|aggregate|all|and|antonym|are|as|associativity|assume|asymmetry|attr|be|begin|being|by|canceled|case|cases|clusters?|coherence|commutativity|compatibility|connectedness|consider|consistency|constructors|contradiction|correctness|def|deffunc|define|definitions?|defpred|do|does|end|environ|equals|ex|exactly|existence|for|from|func|given|hence|hereby|holds|idempotence|identity|iff?|implies|involutiveness|irreflexivity|is|it|let|means|mode|non|not|notations?|now|of|or|otherwise|over|per|pred|prefix|projectivity|proof|provided|qua|reconsider|redefine|reduce|reducibility|reflexivity|registrations?|requirements|reserve|sch|schemes?|section|selector|set|sethood|st|struct|such|suppose|symmetry|synonym|take|that|the|then|theorems?|thesis|thus|to|transitivity|uniqueness|vocabular(?:ies|y)|when|where|with|wrt)\\b/,\n    parameter: {\n      pattern: /\\$(?:10|\\d)/,\n      alias: 'variable'\n    },\n    variable: /\\b\\w+(?=:)/,\n    number: /(?:\\b|-)\\d+\\b/,\n    operator: /\\.\\.\\.|->|&|\\.?=/,\n    punctuation: /\\(#|#\\)|[,:;\\[\\](){}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mizar.js\n"));

/***/ })

}]);