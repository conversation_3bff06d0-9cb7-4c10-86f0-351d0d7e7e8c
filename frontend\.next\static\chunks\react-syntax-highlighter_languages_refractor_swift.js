"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_swift"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/swift.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/swift.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = swift\nswift.displayName = 'swift'\nswift.aliases = []\nfunction swift(Prism) {\n  Prism.languages.swift = {\n    comment: {\n      // Nested comments are supported up to 2 levels\n      pattern:\n        /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-literal': [\n      // https://docs.swift.org/swift-book/LanguageGuide/StringsAndCharacters.html\n      {\n        pattern: RegExp(\n          /(^|[^\"#])/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/\n              .source +\n            ')' +\n            /(?![\"#])/.source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\\\($/,\n            alias: 'punctuation'\n          },\n          punctuation: /\\\\(?=[\\r\\n])/,\n          string: /[\\s\\S]+/\n        }\n      },\n      {\n        pattern: RegExp(\n          /(^|[^\"#])(#+)/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source +\n            ')' +\n            '\\\\2'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\#+\\($/,\n            alias: 'punctuation'\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    ],\n    directive: {\n      // directives with conditions\n      pattern: RegExp(\n        /#/.source +\n          '(?:' +\n          (/(?:elseif|if)\\b/.source +\n            '(?:[ \\t]*' + // This regex is a little complex. It's equivalent to this:\n            //   (?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*<round>)?|<round>)(?:[ \\t]*(?:&&|\\|\\|))?\n            // where <round> is a general parentheses expression.\n            /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/\n              .source +\n            ')+') +\n          '|' +\n          /(?:else|endif)\\b/.source +\n          ')'\n      ),\n      alias: 'property',\n      inside: {\n        'directive-name': /^#\\w+/,\n        boolean: /\\b(?:false|true)\\b/,\n        number: /\\b\\d+(?:\\.\\d+)*\\b/,\n        operator: /!|&&|\\|\\||[<>]=?/,\n        punctuation: /[(),]/\n      }\n    },\n    literal: {\n      pattern:\n        /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,\n      alias: 'constant'\n    },\n    'other-directive': {\n      pattern: /#\\w+\\b/,\n      alias: 'property'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'atrule'\n    },\n    'function-definition': {\n      pattern: /(\\bfunc\\s+)\\w+/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    label: {\n      // https://docs.swift.org/swift-book/LanguageGuide/ControlFlow.html#ID141\n      pattern:\n        /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,\n      lookbehind: true,\n      alias: 'important'\n    },\n    keyword:\n      /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    nil: {\n      pattern: /\\bnil\\b/,\n      alias: 'constant'\n    },\n    'short-argument': /\\$\\d+\\b/,\n    omit: {\n      pattern: /\\b_\\b/,\n      alias: 'keyword'\n    },\n    number:\n      /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\n    // A class name must start with an upper-case letter and be either 1 letter long or contain a lower-case letter.\n    'class-name': /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant: /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,\n    // Operators are generic in Swift. Developers can even create new operators (e.g. +++).\n    // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html#ID481\n    // This regex only supports ASCII operators.\n    operator: /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,\n    punctuation: /[{}[\\]();,.:\\\\]/\n  }\n  Prism.languages.swift['string-literal'].forEach(function (rule) {\n    rule.inside['interpolation'].inside = Prism.languages.swift\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/swift.js\n"));

/***/ })

}]);