"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_smalltalk"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/smalltalk.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/smalltalk.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = smalltalk\nsmalltalk.displayName = 'smalltalk'\nsmalltalk.aliases = []\nfunction smalltalk(Prism) {\n  Prism.languages.smalltalk = {\n    comment: {\n      pattern: /\"(?:\"\"|[^\"])*\"/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\$./,\n      greedy: true\n    },\n    string: {\n      pattern: /'(?:''|[^'])*'/,\n      greedy: true\n    },\n    symbol: /#[\\da-z]+|#(?:-|([+\\/\\\\*~<>=@%|&?!])\\1?)|#(?=\\()/i,\n    'block-arguments': {\n      pattern: /(\\[\\s*):[^\\[|]*\\|/,\n      lookbehind: true,\n      inside: {\n        variable: /:[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    'temporary-variables': {\n      pattern: /\\|[^|]+\\|/,\n      inside: {\n        variable: /[\\da-z]+/i,\n        punctuation: /\\|/\n      }\n    },\n    keyword: /\\b(?:new|nil|self|super)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: [\n      /\\d+r-?[\\dA-Z]+(?:\\.[\\dA-Z]+)?(?:e-?\\d+)?/,\n      /\\b\\d+(?:\\.\\d+)?(?:e-?\\d+)?/\n    ],\n    operator: /[<=]=?|:=|~[~=]|\\/\\/?|\\\\\\\\|>[>=]?|[!^+\\-*&|,@]/,\n    punctuation: /[.;:?\\[\\](){}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/smalltalk.js\n"));

/***/ })

}]);