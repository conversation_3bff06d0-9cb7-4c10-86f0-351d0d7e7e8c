"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_reason"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/reason.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/reason.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = reason\nreason.displayName = 'reason'\nreason.aliases = []\nfunction reason(Prism) {\n  Prism.languages.reason = Prism.languages.extend('clike', {\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    // 'class-name' must be matched *after* 'constructor' defined below\n    'class-name': /\\b[A-Z]\\w*/,\n    keyword:\n      /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,\n    operator:\n      /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/\n  })\n  Prism.languages.insertBefore('reason', 'class-name', {\n    char: {\n      pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,\n      greedy: true\n    },\n    // Negative look-ahead prevents from matching things like String.capitalize\n    constructor: /\\b[A-Z]\\w*\\b(?!\\s*\\.)/,\n    label: {\n      pattern: /\\b[a-z]\\w*(?=::)/,\n      alias: 'symbol'\n    }\n  }) // We can't match functions property, so let's not even try.\n  delete Prism.languages.reason.function\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/reason.js\n"));

/***/ })

}]);