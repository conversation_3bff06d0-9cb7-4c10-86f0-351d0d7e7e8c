"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_elm"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elm.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elm.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = elm\nelm.displayName = 'elm'\nelm.aliases = []\nfunction elm(Prism) {\n  Prism.languages.elm = {\n    comment: /--.*|\\{-[\\s\\S]*?-\\}/,\n    char: {\n      pattern:\n        /'(?:[^\\\\'\\r\\n]|\\\\(?:[abfnrtv\\\\']|\\d+|x[0-9a-fA-F]+|u\\{[0-9a-fA-F]+\\}))'/,\n      greedy: true\n    },\n    string: [\n      {\n        // Multiline strings are wrapped in triple \". Quotes may appear unescaped.\n        pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n        greedy: true\n      },\n      {\n        pattern: /\"(?:[^\\\\\"\\r\\n]|\\\\.)*\"/,\n        greedy: true\n      }\n    ],\n    'import-statement': {\n      // The imported or hidden names are not included in this import\n      // statement. This is because we want to highlight those exactly like\n      // we do for the names in the program.\n      pattern:\n        /(^[\\t ]*)import\\s+[A-Z]\\w*(?:\\.[A-Z]\\w*)*(?:\\s+as\\s+(?:[A-Z]\\w*)(?:\\.[A-Z]\\w*)*)?(?:\\s+exposing\\s+)?/m,\n      lookbehind: true,\n      inside: {\n        keyword: /\\b(?:as|exposing|import)\\b/\n      }\n    },\n    keyword:\n      /\\b(?:alias|as|case|else|exposing|if|in|infixl|infixr|let|module|of|then|type)\\b/,\n    // These are builtin variables only. Constructors are highlighted later as a constant.\n    builtin:\n      /\\b(?:abs|acos|always|asin|atan|atan2|ceiling|clamp|compare|cos|curry|degrees|e|flip|floor|fromPolar|identity|isInfinite|isNaN|logBase|max|min|negate|never|not|pi|radians|rem|round|sin|sqrt|tan|toFloat|toPolar|toString|truncate|turns|uncurry|xor)\\b/,\n    // decimal integers and floating point numbers | hexadecimal integers\n    number: /\\b(?:\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?|0x[0-9a-f]+)\\b/i,\n    // Most of this is needed because of the meaning of a single '.'.\n    // If it stands alone freely, it is the function composition.\n    // It may also be a separator between a module name and an identifier => no\n    // operator. If it comes together with other special characters it is an\n    // operator too.\n    // Valid operator characters in 0.18: +-/*=.$<>:&|^?%#@~!\n    // Ref: https://groups.google.com/forum/#!msg/elm-dev/0AHSnDdkSkQ/E0SVU70JEQAJ\n    operator: /\\s\\.\\s|[+\\-/*=.$<>:&|^?%#@~!]{2,}|[+\\-/*=$<>:&|^?%#@~!]/,\n    // In Elm, nearly everything is a variable, do not highlight these.\n    hvariable: /\\b(?:[A-Z]\\w*\\.)*[a-z]\\w*\\b/,\n    constant: /\\b(?:[A-Z]\\w*\\.)*[A-Z]\\w*\\b/,\n    punctuation: /[{}[\\]|(),.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2VsbS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLFlBQVk7QUFDakM7QUFDQTtBQUNBLCtEQUErRCxjQUFjO0FBQzdFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsR0FBRztBQUNoRDtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFxlbG0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gZWxtXG5lbG0uZGlzcGxheU5hbWUgPSAnZWxtJ1xuZWxtLmFsaWFzZXMgPSBbXVxuZnVuY3Rpb24gZWxtKFByaXNtKSB7XG4gIFByaXNtLmxhbmd1YWdlcy5lbG0gPSB7XG4gICAgY29tbWVudDogLy0tLip8XFx7LVtcXHNcXFNdKj8tXFx9LyxcbiAgICBjaGFyOiB7XG4gICAgICBwYXR0ZXJuOlxuICAgICAgICAvJyg/OlteXFxcXCdcXHJcXG5dfFxcXFwoPzpbYWJmbnJ0dlxcXFwnXXxcXGQrfHhbMC05YS1mQS1GXSt8dVxce1swLTlhLWZBLUZdK1xcfSkpJy8sXG4gICAgICBncmVlZHk6IHRydWVcbiAgICB9LFxuICAgIHN0cmluZzogW1xuICAgICAge1xuICAgICAgICAvLyBNdWx0aWxpbmUgc3RyaW5ncyBhcmUgd3JhcHBlZCBpbiB0cmlwbGUgXCIuIFF1b3RlcyBtYXkgYXBwZWFyIHVuZXNjYXBlZC5cbiAgICAgICAgcGF0dGVybjogL1wiXCJcIltcXHNcXFNdKj9cIlwiXCIvLFxuICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46IC9cIig/OlteXFxcXFwiXFxyXFxuXXxcXFxcLikqXCIvLFxuICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgIH1cbiAgICBdLFxuICAgICdpbXBvcnQtc3RhdGVtZW50Jzoge1xuICAgICAgLy8gVGhlIGltcG9ydGVkIG9yIGhpZGRlbiBuYW1lcyBhcmUgbm90IGluY2x1ZGVkIGluIHRoaXMgaW1wb3J0XG4gICAgICAvLyBzdGF0ZW1lbnQuIFRoaXMgaXMgYmVjYXVzZSB3ZSB3YW50IHRvIGhpZ2hsaWdodCB0aG9zZSBleGFjdGx5IGxpa2VcbiAgICAgIC8vIHdlIGRvIGZvciB0aGUgbmFtZXMgaW4gdGhlIHByb2dyYW0uXG4gICAgICBwYXR0ZXJuOlxuICAgICAgICAvKF5bXFx0IF0qKWltcG9ydFxccytbQS1aXVxcdyooPzpcXC5bQS1aXVxcdyopKig/Olxccythc1xccysoPzpbQS1aXVxcdyopKD86XFwuW0EtWl1cXHcqKSopPyg/OlxccytleHBvc2luZ1xccyspPy9tLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgIGluc2lkZToge1xuICAgICAgICBrZXl3b3JkOiAvXFxiKD86YXN8ZXhwb3Npbmd8aW1wb3J0KVxcYi9cbiAgICAgIH1cbiAgICB9LFxuICAgIGtleXdvcmQ6XG4gICAgICAvXFxiKD86YWxpYXN8YXN8Y2FzZXxlbHNlfGV4cG9zaW5nfGlmfGlufGluZml4bHxpbmZpeHJ8bGV0fG1vZHVsZXxvZnx0aGVufHR5cGUpXFxiLyxcbiAgICAvLyBUaGVzZSBhcmUgYnVpbHRpbiB2YXJpYWJsZXMgb25seS4gQ29uc3RydWN0b3JzIGFyZSBoaWdobGlnaHRlZCBsYXRlciBhcyBhIGNvbnN0YW50LlxuICAgIGJ1aWx0aW46XG4gICAgICAvXFxiKD86YWJzfGFjb3N8YWx3YXlzfGFzaW58YXRhbnxhdGFuMnxjZWlsaW5nfGNsYW1wfGNvbXBhcmV8Y29zfGN1cnJ5fGRlZ3JlZXN8ZXxmbGlwfGZsb29yfGZyb21Qb2xhcnxpZGVudGl0eXxpc0luZmluaXRlfGlzTmFOfGxvZ0Jhc2V8bWF4fG1pbnxuZWdhdGV8bmV2ZXJ8bm90fHBpfHJhZGlhbnN8cmVtfHJvdW5kfHNpbnxzcXJ0fHRhbnx0b0Zsb2F0fHRvUG9sYXJ8dG9TdHJpbmd8dHJ1bmNhdGV8dHVybnN8dW5jdXJyeXx4b3IpXFxiLyxcbiAgICAvLyBkZWNpbWFsIGludGVnZXJzIGFuZCBmbG9hdGluZyBwb2ludCBudW1iZXJzIHwgaGV4YWRlY2ltYWwgaW50ZWdlcnNcbiAgICBudW1iZXI6IC9cXGIoPzpcXGQrKD86XFwuXFxkKyk/KD86ZVsrLV0/XFxkKyk/fDB4WzAtOWEtZl0rKVxcYi9pLFxuICAgIC8vIE1vc3Qgb2YgdGhpcyBpcyBuZWVkZWQgYmVjYXVzZSBvZiB0aGUgbWVhbmluZyBvZiBhIHNpbmdsZSAnLicuXG4gICAgLy8gSWYgaXQgc3RhbmRzIGFsb25lIGZyZWVseSwgaXQgaXMgdGhlIGZ1bmN0aW9uIGNvbXBvc2l0aW9uLlxuICAgIC8vIEl0IG1heSBhbHNvIGJlIGEgc2VwYXJhdG9yIGJldHdlZW4gYSBtb2R1bGUgbmFtZSBhbmQgYW4gaWRlbnRpZmllciA9PiBub1xuICAgIC8vIG9wZXJhdG9yLiBJZiBpdCBjb21lcyB0b2dldGhlciB3aXRoIG90aGVyIHNwZWNpYWwgY2hhcmFjdGVycyBpdCBpcyBhblxuICAgIC8vIG9wZXJhdG9yIHRvby5cbiAgICAvLyBWYWxpZCBvcGVyYXRvciBjaGFyYWN0ZXJzIGluIDAuMTg6ICstLyo9LiQ8PjomfF4/JSNAfiFcbiAgICAvLyBSZWY6IGh0dHBzOi8vZ3JvdXBzLmdvb2dsZS5jb20vZm9ydW0vIyFtc2cvZWxtLWRldi8wQUhTbkRka1NrUS9FMFNWVTcwSkVRQUpcbiAgICBvcGVyYXRvcjogL1xcc1xcLlxcc3xbK1xcLS8qPS4kPD46JnxePyUjQH4hXXsyLH18WytcXC0vKj0kPD46JnxePyUjQH4hXS8sXG4gICAgLy8gSW4gRWxtLCBuZWFybHkgZXZlcnl0aGluZyBpcyBhIHZhcmlhYmxlLCBkbyBub3QgaGlnaGxpZ2h0IHRoZXNlLlxuICAgIGh2YXJpYWJsZTogL1xcYig/OltBLVpdXFx3KlxcLikqW2Etel1cXHcqXFxiLyxcbiAgICBjb25zdGFudDogL1xcYig/OltBLVpdXFx3KlxcLikqW0EtWl1cXHcqXFxiLyxcbiAgICBwdW5jdHVhdGlvbjogL1t7fVtcXF18KCksLjpdL1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elm.js\n"));

/***/ })

}]);