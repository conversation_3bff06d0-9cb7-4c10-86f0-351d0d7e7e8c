"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_bicep"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bicep.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bicep.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = bicep\nbicep.displayName = 'bicep'\nbicep.aliases = []\nfunction bicep(Prism) {\n  // based loosely upon: https://github.com/Azure/bicep/blob/main/src/textmate/bicep.tmlanguage\n  Prism.languages.bicep = {\n    comment: [\n      {\n        // multiline comments eg /* ASDF */\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        // singleline comments eg // ASDF\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    property: [\n      {\n        pattern: /([\\r\\n][ \\t]*)[a-z_]\\w*(?=[ \\t]*:)/i,\n        lookbehind: true\n      },\n      {\n        pattern: /([\\r\\n][ \\t]*)'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'(?=[ \\t]*:)/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: [\n      {\n        pattern: /'''[^'][\\s\\S]*?'''/,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?!\\{)|[^'\\\\\\r\\n$])*'/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    'interpolated-string': {\n      pattern: /(^|[^\\\\'])'(?:\\\\.|\\$(?:(?!\\{)|\\{[^{}\\r\\n]*\\})|[^'\\\\\\r\\n$])*'/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}\\r\\n]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true\n            },\n            punctuation: /^\\$\\{|\\}$/\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    datatype: {\n      pattern: /(\\b(?:output|param)\\b[ \\t]+\\w+[ \\t]+)\\w+\\b/,\n      lookbehind: true,\n      alias: 'class-name'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    // https://github.com/Azure/bicep/blob/114a3251b4e6e30082a58729f19a8cc4e374ffa6/src/textmate/bicep.tmlanguage#L184\n    keyword:\n      /\\b(?:existing|for|if|in|module|null|output|param|resource|targetScope|var)\\b/,\n    decorator: /@\\w+\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:E[+-]?\\d+)?/i,\n    operator:\n      /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.bicep['interpolated-string'].inside['interpolation'].inside[\n    'expression'\n  ].inside = Prism.languages.bicep\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2JpY2VwLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxJQUFJLElBQUksUUFBUTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixJQUFJLFFBQVE7QUFDcEM7QUFDQTtBQUNBLDhCQUE4QixhQUFhO0FBQzNDO0FBQ0EsYUFBYTtBQUNiLCtCQUErQixHQUFHO0FBQ2xDO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRFQUE0RSxFQUFFO0FBQzlFLHFCQUFxQixJQUFJO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVmcmFjdG9yQDMuNi4wXFxub2RlX21vZHVsZXNcXHJlZnJhY3RvclxcbGFuZ1xcYmljZXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gYmljZXBcbmJpY2VwLmRpc3BsYXlOYW1lID0gJ2JpY2VwJ1xuYmljZXAuYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBiaWNlcChQcmlzbSkge1xuICAvLyBiYXNlZCBsb29zZWx5IHVwb246IGh0dHBzOi8vZ2l0aHViLmNvbS9BenVyZS9iaWNlcC9ibG9iL21haW4vc3JjL3RleHRtYXRlL2JpY2VwLnRtbGFuZ3VhZ2VcbiAgUHJpc20ubGFuZ3VhZ2VzLmJpY2VwID0ge1xuICAgIGNvbW1lbnQ6IFtcbiAgICAgIHtcbiAgICAgICAgLy8gbXVsdGlsaW5lIGNvbW1lbnRzIGVnIC8qIEFTREYgKi9cbiAgICAgICAgcGF0dGVybjogLyhefFteXFxcXF0pXFwvXFwqW1xcc1xcU10qPyg/OlxcKlxcL3wkKS8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGdyZWVkeTogdHJ1ZVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgLy8gc2luZ2xlbGluZSBjb21tZW50cyBlZyAvLyBBU0RGXG4gICAgICAgIHBhdHRlcm46IC8oXnxbXlxcXFw6XSlcXC9cXC8uKi8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGdyZWVkeTogdHJ1ZVxuICAgICAgfVxuICAgIF0sXG4gICAgcHJvcGVydHk6IFtcbiAgICAgIHtcbiAgICAgICAgcGF0dGVybjogLyhbXFxyXFxuXVsgXFx0XSopW2Etel9dXFx3Kig/PVsgXFx0XSo6KS9pLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBwYXR0ZXJuOiAvKFtcXHJcXG5dWyBcXHRdKiknKD86XFxcXC58XFwkKD8hXFx7KXxbXidcXFxcXFxyXFxuJF0pKicoPz1bIFxcdF0qOikvLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgIH1cbiAgICBdLFxuICAgIHN0cmluZzogW1xuICAgICAge1xuICAgICAgICBwYXR0ZXJuOiAvJycnW14nXVtcXHNcXFNdKj8nJycvLFxuICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHBhdHRlcm46IC8oXnxbXlxcXFwnXSknKD86XFxcXC58XFwkKD8hXFx7KXxbXidcXFxcXFxyXFxuJF0pKicvLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgIH1cbiAgICBdLFxuICAgICdpbnRlcnBvbGF0ZWQtc3RyaW5nJzoge1xuICAgICAgcGF0dGVybjogLyhefFteXFxcXCddKScoPzpcXFxcLnxcXCQoPzooPyFcXHspfFxce1tee31cXHJcXG5dKlxcfSl8W14nXFxcXFxcclxcbiRdKSonLyxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBpbnNpZGU6IHtcbiAgICAgICAgaW50ZXJwb2xhdGlvbjoge1xuICAgICAgICAgIHBhdHRlcm46IC9cXCRcXHtbXnt9XFxyXFxuXSpcXH0vLFxuICAgICAgICAgIGluc2lkZToge1xuICAgICAgICAgICAgZXhwcmVzc2lvbjoge1xuICAgICAgICAgICAgICBwYXR0ZXJuOiAvKF5cXCRcXHspW1xcc1xcU10rKD89XFx9JCkvLFxuICAgICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcHVuY3R1YXRpb246IC9eXFwkXFx7fFxcfSQvXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBzdHJpbmc6IC9bXFxzXFxTXSsvXG4gICAgICB9XG4gICAgfSxcbiAgICBkYXRhdHlwZToge1xuICAgICAgcGF0dGVybjogLyhcXGIoPzpvdXRwdXR8cGFyYW0pXFxiWyBcXHRdK1xcdytbIFxcdF0rKVxcdytcXGIvLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgIGFsaWFzOiAnY2xhc3MtbmFtZSdcbiAgICB9LFxuICAgIGJvb2xlYW46IC9cXGIoPzpmYWxzZXx0cnVlKVxcYi8sXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL0F6dXJlL2JpY2VwL2Jsb2IvMTE0YTMyNTFiNGU2ZTMwMDgyYTU4NzI5ZjE5YThjYzRlMzc0ZmZhNi9zcmMvdGV4dG1hdGUvYmljZXAudG1sYW5ndWFnZSNMMTg0XG4gICAga2V5d29yZDpcbiAgICAgIC9cXGIoPzpleGlzdGluZ3xmb3J8aWZ8aW58bW9kdWxlfG51bGx8b3V0cHV0fHBhcmFtfHJlc291cmNlfHRhcmdldFNjb3BlfHZhcilcXGIvLFxuICAgIGRlY29yYXRvcjogL0BcXHcrXFxiLyxcbiAgICBmdW5jdGlvbjogL1xcYlthLXpfXVxcdyooPz1bIFxcdF0qXFwoKS9pLFxuICAgIG51bWJlcjogLyg/OlxcYlxcZCsoPzpcXC5cXGQqKT98XFxCXFwuXFxkKykoPzpFWystXT9cXGQrKT8vaSxcbiAgICBvcGVyYXRvcjpcbiAgICAgIC8tLXxcXCtcXCt8XFwqXFwqPT98PT58JiY9P3xcXHxcXHw9P3xbIT1dPT18PDw9P3w+Pj4/PT98Wy0rKi8lJnxeIT08Pl09P3xcXC57M318XFw/XFw/PT98XFw/XFwuP3xbfjpdLyxcbiAgICBwdW5jdHVhdGlvbjogL1t7fVtcXF07KCksLjpdL1xuICB9XG4gIFByaXNtLmxhbmd1YWdlcy5iaWNlcFsnaW50ZXJwb2xhdGVkLXN0cmluZyddLmluc2lkZVsnaW50ZXJwb2xhdGlvbiddLmluc2lkZVtcbiAgICAnZXhwcmVzc2lvbidcbiAgXS5pbnNpZGUgPSBQcmlzbS5sYW5ndWFnZXMuYmljZXBcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bicep.js\n"));

/***/ })

}]);