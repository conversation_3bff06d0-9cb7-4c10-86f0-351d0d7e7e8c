"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_wren"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wren.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wren.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = wren\nwren.displayName = 'wren'\nwren.aliases = []\nfunction wren(Prism) {\n  // https://wren.io/\n  Prism.languages.wren = {\n    // Multiline comments in Wren can have nested multiline comments\n    // Comments: // and /* */\n    comment: [\n      {\n        // support 3 levels of nesting\n        // regex: \\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\/\n        pattern:\n          /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*))*\\*\\/)*\\*\\/)*\\*\\//,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    // Triple quoted strings are multiline but cannot have interpolation (raw strings)\n    // Based on prism-python.js\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    // see below\n    'string-literal': null,\n    // #!/usr/bin/env wren on the first line\n    hashbang: {\n      pattern: /^#!\\/.+/,\n      greedy: true,\n      alias: 'comment'\n    },\n    // Attributes are special keywords to add meta data to classes\n    attribute: {\n      // #! attributes are stored in class properties\n      // #!myvar = true\n      // #attributes are not stored and dismissed at compilation\n      pattern: /#!?[ \\t\\u3000]*\\w+/,\n      alias: 'keyword'\n    },\n    'class-name': [\n      {\n        // class definition\n        // class Meta {}\n        pattern: /(\\bclass\\s+)\\w+/,\n        lookbehind: true\n      }, // A class must always start with an uppercase.\n      // File.read\n      /\\b[A-Z][a-z\\d_]*\\b/\n    ],\n    // A constant can be a variable, class, property or method. Just named in all uppercase letters\n    constant: /\\b[A-Z][A-Z\\d_]*\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    keyword:\n      /\\b(?:as|break|class|construct|continue|else|for|foreign|if|import|in|is|return|static|super|this|var|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    // Functions can be Class.method()\n    function: /\\b[a-z_]\\w*(?=\\s*[({])/i,\n    operator: /<<|>>|[=!<>]=?|&&|\\|\\||[-+*/%~^&|?:]|\\.{2,3}/,\n    punctuation: /[\\[\\](){}.,;]/\n  }\n  Prism.languages.wren['string-literal'] = {\n    // A single quote string is multiline and can have interpolation (similar to JS backticks ``)\n    pattern:\n      /(^|[^\\\\\"])\"(?:[^\\\\\"%]|\\\\[\\s\\S]|%(?!\\()|%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\))*\"/,\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      interpolation: {\n        // \"%(interpolation)\"\n        pattern:\n          /((?:^|[^\\\\])(?:\\\\{2})*)%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\)/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /^(%\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: Prism.languages.wren\n          },\n          'interpolation-punctuation': {\n            pattern: /^%\\(|\\)$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: /[\\s\\S]+/\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/wren.js\n"));

/***/ })

}]);