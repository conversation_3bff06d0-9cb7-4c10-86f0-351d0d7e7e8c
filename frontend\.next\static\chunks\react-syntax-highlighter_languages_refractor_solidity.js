"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_solidity"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solidity.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solidity.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = solidity\nsolidity.displayName = 'solidity'\nsolidity.aliases = ['sol']\nfunction solidity(Prism) {\n  Prism.languages.solidity = Prism.languages.extend('clike', {\n    'class-name': {\n      pattern:\n        /(\\b(?:contract|enum|interface|library|new|struct|using)\\s+)(?!\\d)[\\w$]+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_|anonymous|as|assembly|assert|break|calldata|case|constant|constructor|continue|contract|default|delete|do|else|emit|enum|event|external|for|from|function|if|import|indexed|inherited|interface|internal|is|let|library|mapping|memory|modifier|new|payable|pragma|private|public|pure|require|returns?|revert|selfdestruct|solidity|storage|struct|suicide|switch|this|throw|using|var|view|while)\\b/,\n    operator: /=>|->|:=|=:|\\*\\*|\\+\\+|--|\\|\\||&&|<<=?|>>=?|[-+*/%^&|<>!=]=?|[~?]/\n  })\n  Prism.languages.insertBefore('solidity', 'keyword', {\n    builtin:\n      /\\b(?:address|bool|byte|u?int(?:8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?|string|bytes(?:[1-9]|[12]\\d|3[0-2])?)\\b/\n  })\n  Prism.languages.insertBefore('solidity', 'number', {\n    version: {\n      pattern: /([<>]=?|\\^)\\d+\\.\\d+\\.\\d+\\b/,\n      lookbehind: true,\n      alias: 'number'\n    }\n  })\n  Prism.languages.sol = Prism.languages.solidity\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solidity.js\n"));

/***/ })

}]);