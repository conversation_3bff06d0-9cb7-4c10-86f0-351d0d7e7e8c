"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_webIdl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/web-idl.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/web-idl.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = webIdl\nwebIdl.displayName = 'webIdl'\nwebIdl.aliases = []\nfunction webIdl(Prism) {\n  ;(function (Prism) {\n    var id = /(?:\\B-|\\b_|\\b)[A-Za-z][\\w-]*(?![\\w-])/.source\n    var type =\n      '(?:' +\n      /\\b(?:unsigned\\s+)?long\\s+long(?![\\w-])/.source +\n      '|' +\n      /\\b(?:unrestricted|unsigned)\\s+[a-z]+(?![\\w-])/.source +\n      '|' +\n      /(?!(?:unrestricted|unsigned)\\b)/.source +\n      id +\n      /(?:\\s*<(?:[^<>]|<[^<>]*>)*>)?/.source +\n      ')' +\n      /(?:\\s*\\?)?/.source\n    var typeInside = {}\n    Prism.languages['web-idl'] = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      namespace: {\n        pattern: RegExp(/(\\bnamespace\\s+)/.source + id),\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(^|[^\\w-])(?:iterable|maplike|setlike)\\s*<(?:[^<>]|<[^<>]*>)*>/,\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:attribute|const|deleter|getter|optional|setter)\\s+)/.source +\n              type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // callback return type\n          pattern: RegExp(\n            '(' + /\\bcallback\\s+/.source + id + /\\s*=\\s*/.source + ')' + type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // typedef\n          pattern: RegExp(/(\\btypedef\\b\\s*)/.source + type),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:callback|dictionary|enum|interface(?:\\s+mixin)?)\\s+)(?!(?:interface|mixin)\\b)/\n              .source + id\n          ),\n          lookbehind: true\n        },\n        {\n          // inheritance\n          pattern: RegExp(/(:\\s*)/.source + id),\n          lookbehind: true\n        }, // includes and implements\n        RegExp(id + /(?=\\s+(?:implements|includes)\\b)/.source),\n        {\n          pattern: RegExp(/(\\b(?:implements|includes)\\s+)/.source + id),\n          lookbehind: true\n        },\n        {\n          // function return type, parameter types, and dictionary members\n          pattern: RegExp(\n            type +\n              '(?=' +\n              /\\s*(?:\\.{3}\\s*)?/.source +\n              id +\n              /\\s*[(),;=]/.source +\n              ')'\n          ),\n          inside: typeInside\n        }\n      ],\n      builtin:\n        /\\b(?:ArrayBuffer|BigInt64Array|BigUint64Array|ByteString|DOMString|DataView|Float32Array|Float64Array|FrozenArray|Int16Array|Int32Array|Int8Array|ObservableArray|Promise|USVString|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray)\\b/,\n      keyword: [\n        /\\b(?:async|attribute|callback|const|constructor|deleter|dictionary|enum|getter|implements|includes|inherit|interface|mixin|namespace|null|optional|or|partial|readonly|required|setter|static|stringifier|typedef|unrestricted)\\b/, // type keywords\n        /\\b(?:any|bigint|boolean|byte|double|float|iterable|long|maplike|object|octet|record|sequence|setlike|short|symbol|undefined|unsigned|void)\\b/\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: {\n        pattern:\n          /(^|[^\\w-])-?(?:0x[0-9a-f]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|NaN|Infinity)(?![\\w-])/i,\n        lookbehind: true\n      },\n      operator: /\\.{3}|[=:?<>-]/,\n      punctuation: /[(){}[\\].,;]/\n    }\n    for (var key in Prism.languages['web-idl']) {\n      if (key !== 'class-name') {\n        typeInside[key] = Prism.languages['web-idl'][key]\n      }\n    }\n    Prism.languages['webidl'] = Prism.languages['web-idl']\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/web-idl.js\n"));

/***/ })

}]);