from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, <PERSON>
from typing import Annotated, Optional
from pathlib import Path
import json

SERVER_NAME = "WordMCP"
mcp = FastMCP(SERVER_NAME)

UPLOADS_DIR = Path("uploads")
EDITS_DIR = UPLOADS_DIR / ".edits"
EDITS_DIR.mkdir(parents=True, exist_ok=True)


class SectionUpdate(BaseModel):
    file_path: Annotated[str, Field(description="原始DOCX文件的路径（后端可访问）")]
    section_id: Annotated[str, Field(description="章节ID，例如 h2-3")]
    new_html: Annotated[str, Field(description="要写入缓存的章节HTML内容（完整块）")]


class SectionQuery(BaseModel):
    file_path: Annotated[str, Field(description="原始DOCX文件的路径（后端可访问）")]
    section_id: Annotated[str, Field(description="章节ID，例如 h2-3")]


def _doc_key(file_path: Path) -> str:
    return f"doc_{abs(hash(str(file_path))) }"


@mcp.tool()
async def update_section(payload: SectionUpdate) -> str:
    """
    覆盖写入某个章节的 HTML 内容，数据将保存到 uploads/.edits/{doc_key}.json。
    """
    path = Path(payload.file_path)
    if not path.exists():
        return "❌ 文件不存在"
    key = _doc_key(path)
    edits_path = EDITS_DIR / f"{key}.json"
    try:
        if edits_path.exists():
            data = json.loads(edits_path.read_text(encoding="utf-8"))
            if not isinstance(data, dict):
                data = {}
        else:
            data = {}
        if payload.section_id not in data:
            data[payload.section_id] = {}
        data[payload.section_id]["html"] = payload.new_html
        edits_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        return "✅ 章节内容已更新"
    except Exception as e:
        return f"❌ 更新失败: {e}"


@mcp.tool()
async def get_section(payload: SectionQuery) -> str:
    """
    读取指定章节的缓存 HTML（若无缓存则返回空字符串）。
    """
    path = Path(payload.file_path)
    if not path.exists():
        return ""
    key = _doc_key(path)
    edits_path = EDITS_DIR / f"{key}.json"
    try:
        if not edits_path.exists():
            return ""
        data = json.loads(edits_path.read_text(encoding="utf-8"))
        if not isinstance(data, dict):
            return ""
        sec = data.get(payload.section_id)
        return (sec or {}).get("html", "") or ""
    except Exception:
        return ""


if __name__ == "__main__":
    print(f"{SERVER_NAME} started")
    mcp.run(transport="stdio")


