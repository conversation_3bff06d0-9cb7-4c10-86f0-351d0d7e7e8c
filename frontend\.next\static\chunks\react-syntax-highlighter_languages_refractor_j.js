"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_j"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/j.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/j.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = j\nj.displayName = 'j'\nj.aliases = []\nfunction j(Prism) {\n  Prism.languages.j = {\n    comment: {\n      pattern: /\\bNB\\..*/,\n      greedy: true\n    },\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:(?:CR|LF|adverb|conjunction|def|define|dyad|monad|noun|verb)\\b|(?:assert|break|case|catch[dt]?|continue|do|else|elseif|end|fcase|for|for_\\w+|goto_\\w+|if|label_\\w+|return|select|throw|try|while|whilst)\\.)/,\n    verb: {\n      // Negative look-ahead prevents bad highlighting\n      // of ^: ;. =. =: !. !:\n      pattern:\n        /(?!\\^:|;\\.|[=!][.:])(?:\\{(?:\\.|::?)?|p(?:\\.\\.?|:)|[=!\\]]|[<>+*\\-%$|,#][.:]?|[?^]\\.?|[;\\[]:?|[~}\"i][.:]|[ACeEIjLor]\\.|(?:[_\\/\\\\qsux]|_?\\d):)/,\n      alias: 'keyword'\n    },\n    number:\n      /\\b_?(?:(?!\\d:)\\d+(?:\\.\\d+)?(?:(?:ad|ar|[ejpx])_?\\d+(?:\\.\\d+)?)*(?:b_?[\\da-z]+(?:\\.[\\da-z]+)?)?|_\\b(?!\\.))/,\n    adverb: {\n      pattern: /[~}]|[\\/\\\\]\\.?|[bfM]\\.|t[.:]/,\n      alias: 'builtin'\n    },\n    operator: /[=a][.:]|_\\./,\n    conjunction: {\n      pattern: /&(?:\\.:?|:)?|[.:@][.:]?|[!D][.:]|[;dHT]\\.|`:?|[\\^LS]:|\"/,\n      alias: 'variable'\n    },\n    punctuation: /[()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2ouanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQSxpQkFBaUIsaUJBQWlCLDZEQUE2RCxTQUFTO0FBQ3hHO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFxqLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGpcbmouZGlzcGxheU5hbWUgPSAnaidcbmouYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBqKFByaXNtKSB7XG4gIFByaXNtLmxhbmd1YWdlcy5qID0ge1xuICAgIGNvbW1lbnQ6IHtcbiAgICAgIHBhdHRlcm46IC9cXGJOQlxcLi4qLyxcbiAgICAgIGdyZWVkeTogdHJ1ZVxuICAgIH0sXG4gICAgc3RyaW5nOiB7XG4gICAgICBwYXR0ZXJuOiAvJyg/OicnfFteJ1xcclxcbl0pKicvLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBrZXl3b3JkOlxuICAgICAgL1xcYig/Oig/OkNSfExGfGFkdmVyYnxjb25qdW5jdGlvbnxkZWZ8ZGVmaW5lfGR5YWR8bW9uYWR8bm91bnx2ZXJiKVxcYnwoPzphc3NlcnR8YnJlYWt8Y2FzZXxjYXRjaFtkdF0/fGNvbnRpbnVlfGRvfGVsc2V8ZWxzZWlmfGVuZHxmY2FzZXxmb3J8Zm9yX1xcdyt8Z290b19cXHcrfGlmfGxhYmVsX1xcdyt8cmV0dXJufHNlbGVjdHx0aHJvd3x0cnl8d2hpbGV8d2hpbHN0KVxcLikvLFxuICAgIHZlcmI6IHtcbiAgICAgIC8vIE5lZ2F0aXZlIGxvb2stYWhlYWQgcHJldmVudHMgYmFkIGhpZ2hsaWdodGluZ1xuICAgICAgLy8gb2YgXjogOy4gPS4gPTogIS4gITpcbiAgICAgIHBhdHRlcm46XG4gICAgICAgIC8oPyFcXF46fDtcXC58Wz0hXVsuOl0pKD86XFx7KD86XFwufDo6Pyk/fHAoPzpcXC5cXC4/fDopfFs9IVxcXV18Wzw+KypcXC0lJHwsI11bLjpdP3xbP15dXFwuP3xbO1xcW106P3xbfn1cImldWy46XXxbQUNlRUlqTG9yXVxcLnwoPzpbX1xcL1xcXFxxc3V4XXxfP1xcZCk6KS8sXG4gICAgICBhbGlhczogJ2tleXdvcmQnXG4gICAgfSxcbiAgICBudW1iZXI6XG4gICAgICAvXFxiXz8oPzooPyFcXGQ6KVxcZCsoPzpcXC5cXGQrKT8oPzooPzphZHxhcnxbZWpweF0pXz9cXGQrKD86XFwuXFxkKyk/KSooPzpiXz9bXFxkYS16XSsoPzpcXC5bXFxkYS16XSspPyk/fF9cXGIoPyFcXC4pKS8sXG4gICAgYWR2ZXJiOiB7XG4gICAgICBwYXR0ZXJuOiAvW359XXxbXFwvXFxcXF1cXC4/fFtiZk1dXFwufHRbLjpdLyxcbiAgICAgIGFsaWFzOiAnYnVpbHRpbidcbiAgICB9LFxuICAgIG9wZXJhdG9yOiAvWz1hXVsuOl18X1xcLi8sXG4gICAgY29uanVuY3Rpb246IHtcbiAgICAgIHBhdHRlcm46IC8mKD86XFwuOj98Oik/fFsuOkBdWy46XT98WyFEXVsuOl18WztkSFRdXFwufGA6P3xbXFxeTFNdOnxcIi8sXG4gICAgICBhbGlhczogJ3ZhcmlhYmxlJ1xuICAgIH0sXG4gICAgcHVuY3R1YXRpb246IC9bKCldL1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/j.js\n"));

/***/ })

}]);