"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_haskell"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = haskell\nhaskell.displayName = 'haskell'\nhaskell.aliases = ['hs']\nfunction haskell(Prism) {\n  Prism.languages.haskell = {\n    comment: {\n      pattern:\n        /(^|[^-!#$%*+=?&@|~.:<>^\\\\\\/])(?:--(?:(?=.)[^-!#$%*+=?&@|~.:<>^\\\\\\/].*|$)|\\{-[\\s\\S]*?-\\})/m,\n      lookbehind: true\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:[abfnrtv\\\\\"'&]|\\^[A-Z@[\\]^_]|ACK|BEL|BS|CAN|CR|DC1|DC2|DC3|DC4|DEL|DLE|EM|ENQ|EOT|ESC|ETB|ETX|FF|FS|GS|HT|LF|NAK|NUL|RS|SI|SO|SOH|SP|STX|SUB|SYN|US|VT|\\d+|o[0-7]+|x[0-9a-fA-F]+))'/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"]|\\\\(?:\\S|\\s+\\\\))*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:case|class|data|deriving|do|else|if|in|infixl|infixr|instance|let|module|newtype|of|primitive|then|type|where)\\b/,\n    'import-statement': {\n      // The imported or hidden names are not included in this import\n      // statement. This is because we want to highlight those exactly like\n      // we do for the names in the program.\n      pattern:\n        /(^[\\t ]*)import\\s+(?:qualified\\s+)?(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*(?:\\s+as\\s+(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*)?(?:\\s+hiding\\b)?/m,\n      lookbehind: true,\n      inside: {\n        keyword: /\\b(?:as|hiding|import|qualified)\\b/,\n        punctuation: /\\./\n      }\n    },\n    // These are builtin variables only. Constructors are highlighted later as a constant.\n    builtin:\n      /\\b(?:abs|acos|acosh|all|and|any|appendFile|approxRational|asTypeOf|asin|asinh|atan|atan2|atanh|basicIORun|break|catch|ceiling|chr|compare|concat|concatMap|const|cos|cosh|curry|cycle|decodeFloat|denominator|digitToInt|div|divMod|drop|dropWhile|either|elem|encodeFloat|enumFrom|enumFromThen|enumFromThenTo|enumFromTo|error|even|exp|exponent|fail|filter|flip|floatDigits|floatRadix|floatRange|floor|fmap|foldl|foldl1|foldr|foldr1|fromDouble|fromEnum|fromInt|fromInteger|fromIntegral|fromRational|fst|gcd|getChar|getContents|getLine|group|head|id|inRange|index|init|intToDigit|interact|ioError|isAlpha|isAlphaNum|isAscii|isControl|isDenormalized|isDigit|isHexDigit|isIEEE|isInfinite|isLower|isNaN|isNegativeZero|isOctDigit|isPrint|isSpace|isUpper|iterate|last|lcm|length|lex|lexDigits|lexLitChar|lines|log|logBase|lookup|map|mapM|mapM_|max|maxBound|maximum|maybe|min|minBound|minimum|mod|negate|not|notElem|null|numerator|odd|or|ord|otherwise|pack|pi|pred|primExitWith|print|product|properFraction|putChar|putStr|putStrLn|quot|quotRem|range|rangeSize|read|readDec|readFile|readFloat|readHex|readIO|readInt|readList|readLitChar|readLn|readOct|readParen|readSigned|reads|readsPrec|realToFrac|recip|rem|repeat|replicate|return|reverse|round|scaleFloat|scanl|scanl1|scanr|scanr1|seq|sequence|sequence_|show|showChar|showInt|showList|showLitChar|showParen|showSigned|showString|shows|showsPrec|significand|signum|sin|sinh|snd|sort|span|splitAt|sqrt|subtract|succ|sum|tail|take|takeWhile|tan|tanh|threadToIOResult|toEnum|toInt|toInteger|toLower|toRational|toUpper|truncate|uncurry|undefined|unlines|until|unwords|unzip|unzip3|userError|words|writeFile|zip|zip3|zipWith|zipWith3)\\b/,\n    // decimal integers and floating point numbers | octal integers | hexadecimal integers\n    number: /\\b(?:\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?|0o[0-7]+|0x[0-9a-f]+)\\b/i,\n    operator: [\n      {\n        // infix operator\n        pattern: /`(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*`/,\n        greedy: true\n      },\n      {\n        // function composition\n        pattern: /(\\s)\\.(?=\\s)/,\n        lookbehind: true\n      }, // Most of this is needed because of the meaning of a single '.'.\n      // If it stands alone freely, it is the function composition.\n      // It may also be a separator between a module name and an identifier => no\n      // operator. If it comes together with other special characters it is an\n      // operator too.\n      //\n      // This regex means: /[-!#$%*+=?&@|~.:<>^\\\\\\/]+/ without /\\./.\n      /[-!#$%*+=?&@|~:<>^\\\\\\/][-!#$%*+=?&@|~.:<>^\\\\\\/]*|\\.[-!#$%*+=?&@|~.:<>^\\\\\\/]+/\n    ],\n    // In Haskell, nearly everything is a variable, do not highlight these.\n    hvariable: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    constant: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[A-Z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.hs = Prism.languages.haskell\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haskell.js\n"));

/***/ })

}]);