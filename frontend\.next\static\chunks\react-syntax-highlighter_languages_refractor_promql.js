"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_promql"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/promql.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/promql.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = promql\npromql.displayName = 'promql'\npromql.aliases = []\nfunction promql(Prism) {\n  // Thanks to: https://github.com/prometheus-community/monaco-promql/blob/master/src/promql/promql.ts\n  // As well as: https://kausal.co/blog/slate-prism-add-new-syntax-promql/\n  ;(function (Prism) {\n    // PromQL Aggregation Operators\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#aggregation-operators)\n    var aggregations = [\n      'sum',\n      'min',\n      'max',\n      'avg',\n      'group',\n      'stddev',\n      'stdvar',\n      'count',\n      'count_values',\n      'bottomk',\n      'topk',\n      'quantile'\n    ] // PromQL vector matching + the by and without clauses\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#vector-matching)\n    var vectorMatching = [\n      'on',\n      'ignoring',\n      'group_right',\n      'group_left',\n      'by',\n      'without'\n    ] // PromQL offset modifier\n    // (https://prometheus.io/docs/prometheus/latest/querying/basics/#offset-modifier)\n    var offsetModifier = ['offset']\n    var keywords = aggregations.concat(vectorMatching, offsetModifier)\n    Prism.languages.promql = {\n      comment: {\n        pattern: /(^[ \\t]*)#.*/m,\n        lookbehind: true\n      },\n      'vector-match': {\n        // Match the comma-separated label lists inside vector matching:\n        pattern: new RegExp(\n          '((?:' + vectorMatching.join('|') + ')\\\\s*)\\\\([^)]*\\\\)'\n        ),\n        lookbehind: true,\n        inside: {\n          'label-key': {\n            pattern: /\\b[^,]+\\b/,\n            alias: 'attr-name'\n          },\n          punctuation: /[(),]/\n        }\n      },\n      'context-labels': {\n        pattern: /\\{[^{}]*\\}/,\n        inside: {\n          'label-key': {\n            pattern: /\\b[a-z_]\\w*(?=\\s*(?:=|![=~]))/,\n            alias: 'attr-name'\n          },\n          'label-value': {\n            pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n            greedy: true,\n            alias: 'attr-value'\n          },\n          punctuation: /\\{|\\}|=~?|![=~]|,/\n        }\n      },\n      'context-range': [\n        {\n          pattern: /\\[[\\w\\s:]+\\]/,\n          // [1m]\n          inside: {\n            punctuation: /\\[|\\]|:/,\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        },\n        {\n          pattern: /(\\boffset\\s+)\\w+/,\n          // offset 1m\n          lookbehind: true,\n          inside: {\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        }\n      ],\n      keyword: new RegExp('\\\\b(?:' + keywords.join('|') + ')\\\\b', 'i'),\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      number:\n        /[-+]?(?:(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[-+]?\\d+)?\\b|\\b(?:0x[0-9a-f]+|nan|inf)\\b)/i,\n      operator: /[\\^*/%+-]|==|!=|<=|<|>=|>|\\b(?:and|or|unless)\\b/i,\n      punctuation: /[{};()`,.[\\]]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/promql.js\n"));

/***/ })

}]);