"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_graphql"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/graphql.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/graphql.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = graphql\ngraphql.displayName = 'graphql'\ngraphql.aliases = []\nfunction graphql(Prism) {\n  Prism.languages.graphql = {\n    comment: /#.*/,\n    description: {\n      pattern:\n        /(?:\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")(?=\\s*[a-z_])/i,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        'language-markdown': {\n          pattern: /(^\"(?:\"\")?)(?!\\1)[\\s\\S]+(?=\\1$)/,\n          lookbehind: true,\n          inside: Prism.languages.markdown\n        }\n      }\n    },\n    string: {\n      pattern: /\"\"\"(?:[^\"]|(?!\"\"\")\")*\"\"\"|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    number: /(?:\\B-|\\b)\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n    boolean: /\\b(?:false|true)\\b/,\n    variable: /\\$[a-z_]\\w*/i,\n    directive: {\n      pattern: /@[a-z_]\\w*/i,\n      alias: 'function'\n    },\n    'attr-name': {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:\\((?:[^()\"]|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")*\\))?:)/i,\n      greedy: true\n    },\n    'atom-input': {\n      pattern: /\\b[A-Z]\\w*Input\\b/,\n      alias: 'class-name'\n    },\n    scalar: /\\b(?:Boolean|Float|ID|Int|String)\\b/,\n    constant: /\\b[A-Z][A-Z_\\d]*\\b/,\n    'class-name': {\n      pattern:\n        /(\\b(?:enum|implements|interface|on|scalar|type|union)\\s+|&\\s*|:\\s*|\\[)[A-Z_]\\w*/,\n      lookbehind: true\n    },\n    fragment: {\n      pattern: /(\\bfragment\\s+|\\.{3}\\s*(?!on\\b))[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-mutation': {\n      pattern: /(\\bmutation\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'definition-query': {\n      pattern: /(\\bquery\\s+)[a-zA-Z_]\\w*/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword:\n      /\\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\\b/,\n    operator: /[!=|&]|\\.{3}/,\n    'property-query': /\\w+(?=\\s*\\()/,\n    object: /\\w+(?=\\s*\\{)/,\n    punctuation: /[!(){}\\[\\]:=,]/,\n    property: /\\w+/\n  }\n  Prism.hooks.add('after-tokenize', function afterTokenizeGraphql(env) {\n    if (env.language !== 'graphql') {\n      return\n    }\n    /**\n     * get the graphql token stream that we want to customize\n     *\n     * @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n     * @type {Token[]}\n     */\n    var validTokens = env.tokens.filter(function (token) {\n      return (\n        typeof token !== 'string' &&\n        token.type !== 'comment' &&\n        token.type !== 'scalar'\n      )\n    })\n    var currentIndex = 0\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {number} offset\n     * @returns {Token | undefined}\n     */\n    function getToken(offset) {\n      return validTokens[currentIndex + offset]\n    }\n    /**\n     * Returns whether the token relative to the current index has the given type.\n     *\n     * @param {readonly string[]} types\n     * @param {number} [offset=0]\n     * @returns {boolean}\n     */\n    function isTokenType(types, offset) {\n      offset = offset || 0\n      for (var i = 0; i < types.length; i++) {\n        var token = getToken(i + offset)\n        if (!token || token.type !== types[i]) {\n          return false\n        }\n      }\n      return true\n    }\n    /**\n     * Returns the index of the closing bracket to an opening bracket.\n     *\n     * It is assumed that `token[currentIndex - 1]` is an opening bracket.\n     *\n     * If no closing bracket could be found, `-1` will be returned.\n     *\n     * @param {RegExp} open\n     * @param {RegExp} close\n     * @returns {number}\n     */\n    function findClosingBracket(open, close) {\n      var stackHeight = 1\n      for (var i = currentIndex; i < validTokens.length; i++) {\n        var token = validTokens[i]\n        var content = token.content\n        if (token.type === 'punctuation' && typeof content === 'string') {\n          if (open.test(content)) {\n            stackHeight++\n          } else if (close.test(content)) {\n            stackHeight--\n            if (stackHeight === 0) {\n              return i\n            }\n          }\n        }\n      }\n      return -1\n    }\n    /**\n     * Adds an alias to the given token.\n     *\n     * @param {Token} token\n     * @param {string} alias\n     * @returns {void}\n     */\n    function addAlias(token, alias) {\n      var aliases = token.alias\n      if (!aliases) {\n        token.alias = aliases = []\n      } else if (!Array.isArray(aliases)) {\n        token.alias = aliases = [aliases]\n      }\n      aliases.push(alias)\n    }\n    for (; currentIndex < validTokens.length; ) {\n      var startToken = validTokens[currentIndex++] // add special aliases for mutation tokens\n      if (startToken.type === 'keyword' && startToken.content === 'mutation') {\n        // any array of the names of all input variables (if any)\n        var inputVariables = []\n        if (\n          isTokenType(['definition-mutation', 'punctuation']) &&\n          getToken(1).content === '('\n        ) {\n          // definition\n          currentIndex += 2 // skip 'definition-mutation' and 'punctuation'\n          var definitionEnd = findClosingBracket(/^\\($/, /^\\)$/)\n          if (definitionEnd === -1) {\n            continue\n          } // find all input variables\n          for (; currentIndex < definitionEnd; currentIndex++) {\n            var t = getToken(0)\n            if (t.type === 'variable') {\n              addAlias(t, 'variable-input')\n              inputVariables.push(t.content)\n            }\n          }\n          currentIndex = definitionEnd + 1\n        }\n        if (\n          isTokenType(['punctuation', 'property-query']) &&\n          getToken(0).content === '{'\n        ) {\n          currentIndex++ // skip opening bracket\n          addAlias(getToken(0), 'property-mutation')\n          if (inputVariables.length > 0) {\n            var mutationEnd = findClosingBracket(/^\\{$/, /^\\}$/)\n            if (mutationEnd === -1) {\n              continue\n            } // give references to input variables a special alias\n            for (var i = currentIndex; i < mutationEnd; i++) {\n              var varToken = validTokens[i]\n              if (\n                varToken.type === 'variable' &&\n                inputVariables.indexOf(varToken.content) >= 0\n              ) {\n                addAlias(varToken, 'variable-input')\n              }\n            }\n          }\n        }\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/graphql.js\n"));

/***/ })

}]);