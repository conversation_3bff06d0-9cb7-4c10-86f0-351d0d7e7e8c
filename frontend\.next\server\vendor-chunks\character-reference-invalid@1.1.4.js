"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-reference-invalid@1.1.4";
exports.ids = ["vendor-chunks/character-reference-invalid@1.1.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/character-reference-invalid@1.1.4/node_modules/character-reference-invalid/index.json":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/character-reference-invalid@1.1.4/node_modules/character-reference-invalid/index.json ***!
  \******************************************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}');

/***/ })

};
;