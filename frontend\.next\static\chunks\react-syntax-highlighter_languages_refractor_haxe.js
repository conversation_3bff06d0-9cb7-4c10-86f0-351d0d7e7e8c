"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_haxe"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haxe.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haxe.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = haxe\nhaxe.displayName = 'haxe'\nhaxe.aliases = []\nfunction haxe(Prism) {\n  Prism.languages.haxe = Prism.languages.extend('clike', {\n    string: {\n      // Strings can be multi-line\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    'class-name': [\n      {\n        pattern:\n          /(\\b(?:abstract|class|enum|extends|implements|interface|new|typedef)\\s+)[A-Z_]\\w*/,\n        lookbehind: true\n      }, // based on naming convention\n      /\\b[A-Z]\\w*/\n    ],\n    // The final look-ahead prevents highlighting of keywords if expressions such as \"haxe.macro.Expr\"\n    keyword:\n      /\\bthis\\b|\\b(?:abstract|as|break|case|cast|catch|class|continue|default|do|dynamic|else|enum|extends|extern|final|for|from|function|if|implements|import|in|inline|interface|macro|new|null|operator|overload|override|package|private|public|return|static|super|switch|throw|to|try|typedef|untyped|using|var|while)(?!\\.)\\b/,\n    function: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:<[^<>]*>\\s*)?\\()/i,\n      greedy: true\n    },\n    operator: /\\.{3}|\\+\\+|--|&&|\\|\\||->|=>|(?:<<?|>{1,3}|[-+*/%!=&|^])=?|[?:~]/\n  })\n  Prism.languages.insertBefore('haxe', 'string', {\n    'string-interpolation': {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^\\\\])\\$(?:\\w+|\\{[^{}]+\\})/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{?|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.haxe\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'class-name', {\n    regex: {\n      pattern: /~\\/(?:[^\\/\\\\\\r\\n]|\\\\.)+\\/[a-z]*/,\n      greedy: true,\n      inside: {\n        'regex-flags': /\\b[a-z]+$/,\n        'regex-source': {\n          pattern: /^(~\\/)[\\s\\S]+(?=\\/$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^~\\/|\\/$/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'keyword', {\n    preprocessor: {\n      pattern: /#(?:else|elseif|end|if)\\b.*/,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /@:?[\\w.]+/,\n      alias: 'symbol'\n    },\n    reification: {\n      pattern: /\\$(?:\\w+|(?=\\{))/,\n      alias: 'important'\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/haxe.js\n"));

/***/ })

}]);