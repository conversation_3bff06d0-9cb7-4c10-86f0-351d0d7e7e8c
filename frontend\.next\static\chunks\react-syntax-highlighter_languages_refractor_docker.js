"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_docker"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/docker.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/docker.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = docker\ndocker.displayName = 'docker'\ndocker.aliases = ['dockerfile']\nfunction docker(Prism) {\n  ;(function (Prism) {\n    // Many of the following regexes will contain negated lookaheads like `[ \\t]+(?![ \\t])`. This is a trick to ensure\n    // that quantifiers behave *atomically*. Atomic quantifiers are necessary to prevent exponential backtracking.\n    var spaceAfterBackSlash =\n      /\\\\[\\r\\n](?:\\s|\\\\[\\r\\n]|#.*(?!.))*(?![\\s#]|\\\\[\\r\\n])/.source // At least one space, comment, or line break\n    var space = /(?:[ \\t]+(?![ \\t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(\n      /<SP_BS>/g,\n      function () {\n        return spaceAfterBackSlash\n      }\n    )\n    var string =\n      /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"|'(?:[^'\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*'/\n        .source\n    var option = /--[\\w-]+=(?:<STR>|(?![\"'])(?:[^\\s\\\\]|\\\\.)+)/.source.replace(\n      /<STR>/g,\n      function () {\n        return string\n      }\n    )\n    var stringRule = {\n      pattern: RegExp(string),\n      greedy: true\n    }\n    var commentRule = {\n      pattern: /(^[ \\t]*)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function re(source, flags) {\n      source = source\n        .replace(/<OPT>/g, function () {\n          return option\n        })\n        .replace(/<SP>/g, function () {\n          return space\n        })\n      return RegExp(source, flags)\n    }\n    Prism.languages.docker = {\n      instruction: {\n        pattern:\n          /(^[ \\t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\\s)(?:\\\\.|[^\\r\\n\\\\])*(?:\\\\$(?:\\s|#.*$)*(?![\\s#])(?:\\\\.|[^\\r\\n\\\\])*)*/im,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          options: {\n            pattern: re(\n              /(^(?:ONBUILD<SP>)?\\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,\n              'i'\n            ),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              property: {\n                pattern: /(^|\\s)--[\\w-]+/,\n                lookbehind: true\n              },\n              string: [\n                stringRule,\n                {\n                  pattern: /(=)(?![\"'])(?:[^\\s\\\\]|\\\\.)+/,\n                  lookbehind: true\n                }\n              ],\n              operator: /\\\\$/m,\n              punctuation: /=/\n            }\n          },\n          keyword: [\n            {\n              // https://docs.docker.com/engine/reference/builder/#healthcheck\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\\b/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#from\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \\t\\\\]+<SP>)AS/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#onbuild\n              pattern: re(/(^ONBUILD<SP>)\\w+/.source, 'i'),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: /^\\w+/,\n              greedy: true\n            }\n          ],\n          comment: commentRule,\n          string: stringRule,\n          variable: /\\$(?:\\w+|\\{[^{}\"'\\\\]*\\})/,\n          operator: /\\\\$/m\n        }\n      },\n      comment: commentRule\n    }\n    Prism.languages.dockerfile = Prism.languages.docker\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2RvY2tlci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsSUFBSSxRQUFRO0FBQzVDO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGRvY2tlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBkb2NrZXJcbmRvY2tlci5kaXNwbGF5TmFtZSA9ICdkb2NrZXInXG5kb2NrZXIuYWxpYXNlcyA9IFsnZG9ja2VyZmlsZSddXG5mdW5jdGlvbiBkb2NrZXIoUHJpc20pIHtcbiAgOyhmdW5jdGlvbiAoUHJpc20pIHtcbiAgICAvLyBNYW55IG9mIHRoZSBmb2xsb3dpbmcgcmVnZXhlcyB3aWxsIGNvbnRhaW4gbmVnYXRlZCBsb29rYWhlYWRzIGxpa2UgYFsgXFx0XSsoPyFbIFxcdF0pYC4gVGhpcyBpcyBhIHRyaWNrIHRvIGVuc3VyZVxuICAgIC8vIHRoYXQgcXVhbnRpZmllcnMgYmVoYXZlICphdG9taWNhbGx5Ki4gQXRvbWljIHF1YW50aWZpZXJzIGFyZSBuZWNlc3NhcnkgdG8gcHJldmVudCBleHBvbmVudGlhbCBiYWNrdHJhY2tpbmcuXG4gICAgdmFyIHNwYWNlQWZ0ZXJCYWNrU2xhc2ggPVxuICAgICAgL1xcXFxbXFxyXFxuXSg/Olxcc3xcXFxcW1xcclxcbl18Iy4qKD8hLikpKig/IVtcXHMjXXxcXFxcW1xcclxcbl0pLy5zb3VyY2UgLy8gQXQgbGVhc3Qgb25lIHNwYWNlLCBjb21tZW50LCBvciBsaW5lIGJyZWFrXG4gICAgdmFyIHNwYWNlID0gLyg/OlsgXFx0XSsoPyFbIFxcdF0pKD86PFNQX0JTPik/fDxTUF9CUz4pLy5zb3VyY2UucmVwbGFjZShcbiAgICAgIC88U1BfQlM+L2csXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBzcGFjZUFmdGVyQmFja1NsYXNoXG4gICAgICB9XG4gICAgKVxuICAgIHZhciBzdHJpbmcgPVxuICAgICAgL1wiKD86W15cIlxcXFxcXHJcXG5dfFxcXFwoPzpcXHJcXG58W1xcc1xcU10pKSpcInwnKD86W14nXFxcXFxcclxcbl18XFxcXCg/OlxcclxcbnxbXFxzXFxTXSkpKicvXG4gICAgICAgIC5zb3VyY2VcbiAgICB2YXIgb3B0aW9uID0gLy0tW1xcdy1dKz0oPzo8U1RSPnwoPyFbXCInXSkoPzpbXlxcc1xcXFxdfFxcXFwuKSspLy5zb3VyY2UucmVwbGFjZShcbiAgICAgIC88U1RSPi9nLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gc3RyaW5nXG4gICAgICB9XG4gICAgKVxuICAgIHZhciBzdHJpbmdSdWxlID0ge1xuICAgICAgcGF0dGVybjogUmVnRXhwKHN0cmluZyksXG4gICAgICBncmVlZHk6IHRydWVcbiAgICB9XG4gICAgdmFyIGNvbW1lbnRSdWxlID0ge1xuICAgICAgcGF0dGVybjogLyheWyBcXHRdKikjLiovbSxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBncmVlZHk6IHRydWVcbiAgICB9XG4gICAgLyoqXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNvdXJjZVxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmbGFnc1xuICAgICAqIEByZXR1cm5zIHtSZWdFeHB9XG4gICAgICovXG4gICAgZnVuY3Rpb24gcmUoc291cmNlLCBmbGFncykge1xuICAgICAgc291cmNlID0gc291cmNlXG4gICAgICAgIC5yZXBsYWNlKC88T1BUPi9nLCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgcmV0dXJuIG9wdGlvblxuICAgICAgICB9KVxuICAgICAgICAucmVwbGFjZSgvPFNQPi9nLCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgcmV0dXJuIHNwYWNlXG4gICAgICAgIH0pXG4gICAgICByZXR1cm4gUmVnRXhwKHNvdXJjZSwgZmxhZ3MpXG4gICAgfVxuICAgIFByaXNtLmxhbmd1YWdlcy5kb2NrZXIgPSB7XG4gICAgICBpbnN0cnVjdGlvbjoge1xuICAgICAgICBwYXR0ZXJuOlxuICAgICAgICAgIC8oXlsgXFx0XSopKD86QUREfEFSR3xDTUR8Q09QWXxFTlRSWVBPSU5UfEVOVnxFWFBPU0V8RlJPTXxIRUFMVEhDSEVDS3xMQUJFTHxNQUlOVEFJTkVSfE9OQlVJTER8UlVOfFNIRUxMfFNUT1BTSUdOQUx8VVNFUnxWT0xVTUV8V09SS0RJUikoPz1cXHMpKD86XFxcXC58W15cXHJcXG5cXFxcXSkqKD86XFxcXCQoPzpcXHN8Iy4qJCkqKD8hW1xccyNdKSg/OlxcXFwufFteXFxyXFxuXFxcXF0pKikqL2ltLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICBncmVlZHk6IHRydWUsXG4gICAgICAgIGluc2lkZToge1xuICAgICAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgICAgIHBhdHRlcm46IHJlKFxuICAgICAgICAgICAgICAvKF4oPzpPTkJVSUxEPFNQPik/XFx3KzxTUD4pPE9QVD4oPzo8U1A+PE9QVD4pKi8uc291cmNlLFxuICAgICAgICAgICAgICAnaSdcbiAgICAgICAgICAgICksXG4gICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICAgICAgZ3JlZWR5OiB0cnVlLFxuICAgICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICAgIHByb3BlcnR5OiB7XG4gICAgICAgICAgICAgICAgcGF0dGVybjogLyhefFxccyktLVtcXHctXSsvLFxuICAgICAgICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgc3RyaW5nOiBbXG4gICAgICAgICAgICAgICAgc3RyaW5nUnVsZSxcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICBwYXR0ZXJuOiAvKD0pKD8hW1wiJ10pKD86W15cXHNcXFxcXXxcXFxcLikrLyxcbiAgICAgICAgICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgIG9wZXJhdG9yOiAvXFxcXCQvbSxcbiAgICAgICAgICAgICAgcHVuY3R1YXRpb246IC89L1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAga2V5d29yZDogW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAvLyBodHRwczovL2RvY3MuZG9ja2VyLmNvbS9lbmdpbmUvcmVmZXJlbmNlL2J1aWxkZXIvI2hlYWx0aGNoZWNrXG4gICAgICAgICAgICAgIHBhdHRlcm46IHJlKFxuICAgICAgICAgICAgICAgIC8oXig/Ok9OQlVJTEQ8U1A+KT9IRUFMVEhDSEVDSzxTUD4oPzo8T1BUPjxTUD4pKikoPzpDTUR8Tk9ORSlcXGIvXG4gICAgICAgICAgICAgICAgICAuc291cmNlLFxuICAgICAgICAgICAgICAgICdpJ1xuICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIC8vIGh0dHBzOi8vZG9jcy5kb2NrZXIuY29tL2VuZ2luZS9yZWZlcmVuY2UvYnVpbGRlci8jZnJvbVxuICAgICAgICAgICAgICBwYXR0ZXJuOiByZShcbiAgICAgICAgICAgICAgICAvKF4oPzpPTkJVSUxEPFNQPik/RlJPTTxTUD4oPzo8T1BUPjxTUD4pKig/IS0tKVteIFxcdFxcXFxdKzxTUD4pQVMvXG4gICAgICAgICAgICAgICAgICAuc291cmNlLFxuICAgICAgICAgICAgICAgICdpJ1xuICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIC8vIGh0dHBzOi8vZG9jcy5kb2NrZXIuY29tL2VuZ2luZS9yZWZlcmVuY2UvYnVpbGRlci8jb25idWlsZFxuICAgICAgICAgICAgICBwYXR0ZXJuOiByZSgvKF5PTkJVSUxEPFNQPilcXHcrLy5zb3VyY2UsICdpJyksXG4gICAgICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgICAgICAgIGdyZWVkeTogdHJ1ZVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgcGF0dGVybjogL15cXHcrLyxcbiAgICAgICAgICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgXSxcbiAgICAgICAgICBjb21tZW50OiBjb21tZW50UnVsZSxcbiAgICAgICAgICBzdHJpbmc6IHN0cmluZ1J1bGUsXG4gICAgICAgICAgdmFyaWFibGU6IC9cXCQoPzpcXHcrfFxce1tee31cIidcXFxcXSpcXH0pLyxcbiAgICAgICAgICBvcGVyYXRvcjogL1xcXFwkL21cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIGNvbW1lbnQ6IGNvbW1lbnRSdWxlXG4gICAgfVxuICAgIFByaXNtLmxhbmd1YWdlcy5kb2NrZXJmaWxlID0gUHJpc20ubGFuZ3VhZ2VzLmRvY2tlclxuICB9KShQcmlzbSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/docker.js\n"));

/***/ })

}]);