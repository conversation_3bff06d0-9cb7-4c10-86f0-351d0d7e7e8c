"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_vhdl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vhdl.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vhdl.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = vhdl\nvhdl.displayName = 'vhdl'\nvhdl.aliases = []\nfunction vhdl(Prism) {\n  Prism.languages.vhdl = {\n    comment: /--.+/,\n    // support for all logic vectors\n    'vhdl-vectors': {\n      pattern: /\\b[oxb]\"[\\da-f_]+\"|\"[01uxzwlh-]+\"/i,\n      alias: 'number'\n    },\n    // support for operator overloading included\n    'quoted-function': {\n      pattern: /\"\\S+?\"(?=\\()/,\n      alias: 'function'\n    },\n    string: /\"(?:[^\\\\\"\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"/,\n    constant: /\\b(?:library|use)\\b/i,\n    // support for predefined attributes included\n    keyword:\n      /\\b(?:'active|'ascending|'base|'delayed|'driving|'driving_value|'event|'high|'image|'instance_name|'last_active|'last_event|'last_value|'left|'leftof|'length|'low|'path_name|'pos|'pred|'quiet|'range|'reverse_range|'right|'rightof|'simple_name|'stable|'succ|'transaction|'val|'value|access|after|alias|all|architecture|array|assert|attribute|begin|block|body|buffer|bus|case|component|configuration|constant|disconnect|downto|else|elsif|end|entity|exit|file|for|function|generate|generic|group|guarded|if|impure|in|inertial|inout|is|label|library|linkage|literal|loop|map|new|next|null|of|on|open|others|out|package|port|postponed|procedure|process|pure|range|record|register|reject|report|return|select|severity|shared|signal|subtype|then|to|transport|type|unaffected|units|until|use|variable|wait|when|while|with)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\w+(?=\\()/,\n    // decimal, based, physical, and exponential numbers supported\n    number: /'[01uxzwlh-]'|\\b(?:\\d+#[\\da-f_.]+#|\\d[\\d_.]*)(?:e[-+]?\\d+)?/i,\n    operator:\n      /[<>]=?|:=|[-+*/&=]|\\b(?:abs|and|mod|nand|nor|not|or|rem|rol|ror|sla|sll|sra|srl|xnor|xor)\\b/i,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/vhdl.js\n"));

/***/ })

}]);