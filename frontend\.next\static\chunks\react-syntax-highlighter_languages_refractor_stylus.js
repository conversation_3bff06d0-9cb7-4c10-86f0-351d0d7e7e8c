"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_stylus"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stylus.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stylus.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = stylus\nstylus.displayName = 'stylus'\nstylus.aliases = []\nfunction stylus(Prism) {\n  ;(function (Prism) {\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+)/,\n      lookbehind: true\n    } // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    }\n    var inside = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /\\burl\\(([\"']?).*?\\1\\)/i,\n        greedy: true\n      },\n      string: {\n        pattern: /(\"|')(?:(?!\\1)[^\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\\1/,\n        greedy: true\n      },\n      interpolation: null,\n      // See below\n      func: null,\n      // See below\n      important: /\\B!(?:important|optional)\\b/i,\n      keyword: {\n        pattern: /(^|\\s+)(?:(?:else|for|if|return|unless)(?=\\s|$)|@[\\w-]+)/,\n        lookbehind: true\n      },\n      hexcode: /#[\\da-f]{3,6}/i,\n      color: [\n        /\\b(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)\\b/i,\n        {\n          pattern:\n            /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n          inside: {\n            unit: unit,\n            number: number,\n            function: /[\\w-]+(?=\\()/,\n            punctuation: /[(),]/\n          }\n        }\n      ],\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      boolean: /\\b(?:false|true)\\b/,\n      operator: [\n        // We want non-word chars around \"-\" because it is\n        // accepted in property names.\n        /~|[+!\\/%<>?=]=?|[-:]=|\\*[*=]?|\\.{2,3}|&&|\\|\\||\\B-\\B|\\b(?:and|in|is(?: a| defined| not|nt)?|not|or)\\b/\n      ],\n      number: number,\n      punctuation: /[{}()\\[\\];:,]/\n    }\n    inside['interpolation'] = {\n      pattern: /\\{[^\\r\\n}:]+\\}/,\n      alias: 'variable',\n      inside: {\n        delimiter: {\n          pattern: /^\\{|\\}$/,\n          alias: 'punctuation'\n        },\n        rest: inside\n      }\n    }\n    inside['func'] = {\n      pattern: /[\\w-]+\\([^)]*\\).*/,\n      inside: {\n        function: /^[^(]+/,\n        rest: inside\n      }\n    }\n    Prism.languages.stylus = {\n      'atrule-declaration': {\n        pattern: /(^[ \\t]*)@.+/m,\n        lookbehind: true,\n        inside: {\n          atrule: /^@[\\w-]+/,\n          rest: inside\n        }\n      },\n      'variable-declaration': {\n        pattern: /(^[ \\t]*)[\\w$-]+\\s*.?=[ \\t]*(?:\\{[^{}]*\\}|\\S.*|$)/m,\n        lookbehind: true,\n        inside: {\n          variable: /^\\S+/,\n          rest: inside\n        }\n      },\n      statement: {\n        pattern: /(^[ \\t]*)(?:else|for|if|return|unless)[ \\t].+/m,\n        lookbehind: true,\n        inside: {\n          keyword: /^\\S+/,\n          rest: inside\n        }\n      },\n      // A property/value pair cannot end with a comma or a brace\n      // It cannot have indented content unless it ended with a semicolon\n      'property-declaration': {\n        pattern:\n          /((?:^|\\{)([ \\t]*))(?:[\\w-]|\\{[^}\\r\\n]+\\})+(?:\\s*:\\s*|[ \\t]+)(?!\\s)[^{\\r\\n]*(?:;|[^{\\r\\n,]$(?!(?:\\r?\\n|\\r)(?:\\{|\\2[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          property: {\n            pattern: /^[^\\s:]+/,\n            inside: {\n              interpolation: inside.interpolation\n            }\n          },\n          rest: inside\n        }\n      },\n      // A selector can contain parentheses only as part of a pseudo-element\n      // It can span multiple lines.\n      // It must end with a comma or an accolade or have indented content.\n      selector: {\n        pattern:\n          /(^[ \\t]*)(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)(?:(?:\\r?\\n|\\r)(?:\\1(?:(?=\\S)(?:[^{}\\r\\n:()]|::?[\\w-]+(?:\\([^)\\r\\n]*\\)|(?![\\w-]))|\\{[^}\\r\\n]+\\})+)))*(?:,$|\\{|(?=(?:\\r?\\n|\\r)(?:\\{|\\1[ \\t])))/m,\n        lookbehind: true,\n        inside: {\n          interpolation: inside.interpolation,\n          comment: inside.comment,\n          punctuation: /[{},]/\n        }\n      },\n      func: inside.func,\n      string: inside.string,\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n        lookbehind: true,\n        greedy: true\n      },\n      interpolation: inside.interpolation,\n      punctuation: /[{}()\\[\\];:.]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/stylus.js\n"));

/***/ })

}]);