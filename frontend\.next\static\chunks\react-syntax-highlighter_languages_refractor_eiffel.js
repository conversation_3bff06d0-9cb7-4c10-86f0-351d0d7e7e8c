"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_eiffel"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/eiffel.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/eiffel.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = eiffel\neiffel.displayName = 'eiffel'\neiffel.aliases = []\nfunction eiffel(Prism) {\n  Prism.languages.eiffel = {\n    comment: /--.*/,\n    string: [\n      // Aligned-verbatim-strings\n      {\n        pattern: /\"([^[]*)\\[[\\s\\S]*?\\]\\1\"/,\n        greedy: true\n      }, // Non-aligned-verbatim-strings\n      {\n        pattern: /\"([^{]*)\\{[\\s\\S]*?\\}\\1\"/,\n        greedy: true\n      }, // Single-line string\n      {\n        pattern: /\"(?:%(?:(?!\\n)\\s)*\\n\\s*%|%\\S|[^%\"\\r\\n])*\"/,\n        greedy: true\n      }\n    ],\n    // normal char | special char | char code\n    char: /'(?:%.|[^%'\\r\\n])+'/,\n    keyword:\n      /\\b(?:across|agent|alias|all|and|as|assign|attached|attribute|check|class|convert|create|Current|debug|deferred|detachable|do|else|elseif|end|ensure|expanded|export|external|feature|from|frozen|if|implies|inherit|inspect|invariant|like|local|loop|not|note|obsolete|old|once|or|Precursor|redefine|rename|require|rescue|Result|retry|select|separate|some|then|undefine|until|variant|Void|when|xor)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    // Convention: class-names are always all upper-case characters\n    'class-name': /\\b[A-Z][\\dA-Z_]*\\b/,\n    number: [\n      // hexa | octal | bin\n      /\\b0[xcb][\\da-f](?:_*[\\da-f])*\\b/i, // Decimal\n      /(?:\\b\\d(?:_*\\d)*)?\\.(?:(?:\\d(?:_*\\d)*)?e[+-]?)?\\d(?:_*\\d)*\\b|\\b\\d(?:_*\\d)*\\b\\.?/i\n    ],\n    punctuation: /:=|<<|>>|\\(\\||\\|\\)|->|\\.(?=\\w)|[{}[\\];(),:?]/,\n    operator: /\\\\\\\\|\\|\\.\\.\\||\\.\\.|\\/[~\\/=]?|[><]=?|[-+*^=~]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/eiffel.js\n"));

/***/ })

}]);