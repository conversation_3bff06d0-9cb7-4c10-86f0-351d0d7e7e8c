"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_tremor"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tremor.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tremor.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = tremor\ntremor.displayName = 'tremor'\ntremor.aliases = []\nfunction tremor(Prism) {\n  ;(function (Prism) {\n    Prism.languages.tremor = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n        lookbehind: true\n      },\n      'interpolated-string': null,\n      // see below\n      extractor: {\n        pattern: /\\b[a-z_]\\w*\\|(?:[^\\r\\n\\\\|]|\\\\(?:\\r\\n|[\\s\\S]))*\\|/i,\n        greedy: true,\n        inside: {\n          regex: {\n            pattern: /(^re)\\|[\\s\\S]+/,\n            lookbehind: true\n          },\n          function: /^\\w+/,\n          value: /\\|[\\s\\S]+/\n        }\n      },\n      identifier: {\n        pattern: /`[^`]*`/,\n        greedy: true\n      },\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())\\b/,\n      keyword:\n        /\\b(?:args|as|by|case|config|connect|connector|const|copy|create|default|define|deploy|drop|each|emit|end|erase|event|flow|fn|for|from|group|having|insert|into|intrinsic|let|links|match|merge|mod|move|of|operator|patch|pipeline|recur|script|select|set|sliding|state|stream|to|tumbling|update|use|when|where|window|with)\\b/,\n      boolean: /\\b(?:false|null|true)\\b/i,\n      number:\n        /\\b(?:0b[01_]*|0x[0-9a-fA-F_]*|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee][+-]?[\\d_]+)?)\\b/,\n      'pattern-punctuation': {\n        pattern: /%(?=[({[])/,\n        alias: 'punctuation'\n      },\n      operator:\n        /[-+*\\/%~!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?>?=?|(?:absent|and|not|or|present|xor)\\b/,\n      punctuation: /::|[;\\[\\]()\\{\\},.:]/\n    }\n    var interpolationPattern =\n      /#\\{(?:[^\"{}]|\\{[^{}]*\\}|\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\")*\\}/.source\n    Prism.languages.tremor['interpolated-string'] = {\n      pattern: RegExp(\n        /(^|[^\\\\])/.source +\n          '(?:' +\n          '\"\"\"(?:' +\n          /[^\"\\\\#]|\\\\[\\s\\S]|\"(?!\"\")|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"\"\"' +\n          '|' +\n          '\"(?:' +\n          /[^\"\\\\\\r\\n#]|\\\\(?:\\r\\n|[\\s\\S])|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"' +\n          ')'\n      ),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          inside: {\n            punctuation: /^#\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.tremor\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.troy = Prism.languages['tremor']\n    Prism.languages.trickle = Prism.languages['tremor']\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/tremor.js\n"));

/***/ })

}]);