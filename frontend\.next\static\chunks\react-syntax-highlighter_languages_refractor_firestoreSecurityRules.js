"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_firestoreSecurityRules"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/firestore-security-rules.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/firestore-security-rules.js ***!
  \****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = firestoreSecurityRules\nfirestoreSecurityRules.displayName = 'firestoreSecurityRules'\nfirestoreSecurityRules.aliases = []\nfunction firestoreSecurityRules(Prism) {\n  Prism.languages['firestore-security-rules'] = Prism.languages.extend(\n    'clike',\n    {\n      comment: /\\/\\/.*/,\n      keyword:\n        /\\b(?:allow|function|if|match|null|return|rules_version|service)\\b/,\n      operator: /&&|\\|\\||[<>!=]=?|[-+*/%]|\\b(?:in|is)\\b/\n    }\n  )\n  delete Prism.languages['firestore-security-rules']['class-name']\n  Prism.languages.insertBefore('firestore-security-rules', 'keyword', {\n    path: {\n      pattern:\n        /(^|[\\s(),])(?:\\/(?:[\\w\\xA0-\\uFFFF]+|\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)))+/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)/,\n          inside: {\n            operator: /=/,\n            keyword: /\\*\\*/,\n            punctuation: /[.$(){}]/\n          }\n        },\n        punctuation: /\\//\n      }\n    },\n    method: {\n      // to make the pattern shorter, the actual method names are omitted\n      pattern: /(\\ballow\\s+)[a-z]+(?:\\s*,\\s*[a-z]+)*(?=\\s*[:;])/,\n      lookbehind: true,\n      alias: 'builtin',\n      inside: {\n        punctuation: /,/\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/firestore-security-rules.js\n"));

/***/ })

}]);