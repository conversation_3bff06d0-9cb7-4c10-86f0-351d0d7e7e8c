"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_monkey"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/monkey.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/monkey.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = monkey\nmonkey.displayName = 'monkey'\nmonkey.aliases = []\nfunction monkey(Prism) {\n  Prism.languages.monkey = {\n    comment: {\n      pattern: /^#Rem\\s[\\s\\S]*?^#End|'.+/im,\n      greedy: true\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"/,\n      greedy: true\n    },\n    preprocessor: {\n      pattern: /(^[ \\t]*)#.+/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    function: /\\b\\w+(?=\\()/,\n    'type-char': {\n      pattern: /\\b[?%#$]/,\n      alias: 'class-name'\n    },\n    number: {\n      pattern:\n        /((?:\\.\\.)?)(?:(?:\\b|\\B-\\.?|\\B\\.)\\d+(?:(?!\\.\\.)\\.\\d*)?|\\$[\\da-f]+)/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:Abstract|Array|Bool|Case|Catch|Class|Const|Continue|Default|Eachin|Else|ElseIf|End|EndIf|Exit|Extends|Extern|False|Field|Final|Float|For|Forever|Function|Global|If|Implements|Import|Inline|Int|Interface|Local|Method|Module|New|Next|Null|Object|Private|Property|Public|Repeat|Return|Select|Self|Step|Strict|String|Super|Then|Throw|To|True|Try|Until|Void|Wend|While)\\b/i,\n    operator:\n      /\\.\\.|<[=>]?|>=?|:?=|(?:[+\\-*\\/&~|]|\\b(?:Mod|Shl|Shr)\\b)=?|\\b(?:And|Not|Or)\\b/i,\n    punctuation: /[.,:;()\\[\\]]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL21vbmtleS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0EiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVmcmFjdG9yQDMuNi4wXFxub2RlX21vZHVsZXNcXHJlZnJhY3RvclxcbGFuZ1xcbW9ua2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IG1vbmtleVxubW9ua2V5LmRpc3BsYXlOYW1lID0gJ21vbmtleSdcbm1vbmtleS5hbGlhc2VzID0gW11cbmZ1bmN0aW9uIG1vbmtleShQcmlzbSkge1xuICBQcmlzbS5sYW5ndWFnZXMubW9ua2V5ID0ge1xuICAgIGNvbW1lbnQ6IHtcbiAgICAgIHBhdHRlcm46IC9eI1JlbVxcc1tcXHNcXFNdKj9eI0VuZHwnLisvaW0sXG4gICAgICBncmVlZHk6IHRydWVcbiAgICB9LFxuICAgIHN0cmluZzoge1xuICAgICAgcGF0dGVybjogL1wiW15cIlxcclxcbl0qXCIvLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBwcmVwcm9jZXNzb3I6IHtcbiAgICAgIHBhdHRlcm46IC8oXlsgXFx0XSopIy4rL20sXG4gICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgZ3JlZWR5OiB0cnVlLFxuICAgICAgYWxpYXM6ICdwcm9wZXJ0eSdcbiAgICB9LFxuICAgIGZ1bmN0aW9uOiAvXFxiXFx3Kyg/PVxcKCkvLFxuICAgICd0eXBlLWNoYXInOiB7XG4gICAgICBwYXR0ZXJuOiAvXFxiWz8lIyRdLyxcbiAgICAgIGFsaWFzOiAnY2xhc3MtbmFtZSdcbiAgICB9LFxuICAgIG51bWJlcjoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgLygoPzpcXC5cXC4pPykoPzooPzpcXGJ8XFxCLVxcLj98XFxCXFwuKVxcZCsoPzooPyFcXC5cXC4pXFwuXFxkKik/fFxcJFtcXGRhLWZdKykvaSxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICB9LFxuICAgIGtleXdvcmQ6XG4gICAgICAvXFxiKD86QWJzdHJhY3R8QXJyYXl8Qm9vbHxDYXNlfENhdGNofENsYXNzfENvbnN0fENvbnRpbnVlfERlZmF1bHR8RWFjaGlufEVsc2V8RWxzZUlmfEVuZHxFbmRJZnxFeGl0fEV4dGVuZHN8RXh0ZXJufEZhbHNlfEZpZWxkfEZpbmFsfEZsb2F0fEZvcnxGb3JldmVyfEZ1bmN0aW9ufEdsb2JhbHxJZnxJbXBsZW1lbnRzfEltcG9ydHxJbmxpbmV8SW50fEludGVyZmFjZXxMb2NhbHxNZXRob2R8TW9kdWxlfE5ld3xOZXh0fE51bGx8T2JqZWN0fFByaXZhdGV8UHJvcGVydHl8UHVibGljfFJlcGVhdHxSZXR1cm58U2VsZWN0fFNlbGZ8U3RlcHxTdHJpY3R8U3RyaW5nfFN1cGVyfFRoZW58VGhyb3d8VG98VHJ1ZXxUcnl8VW50aWx8Vm9pZHxXZW5kfFdoaWxlKVxcYi9pLFxuICAgIG9wZXJhdG9yOlxuICAgICAgL1xcLlxcLnw8Wz0+XT98Pj0/fDo/PXwoPzpbK1xcLSpcXC8mfnxdfFxcYig/Ok1vZHxTaGx8U2hyKVxcYik9P3xcXGIoPzpBbmR8Tm90fE9yKVxcYi9pLFxuICAgIHB1bmN0dWF0aW9uOiAvWy4sOjsoKVxcW1xcXV0vXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/monkey.js\n"));

/***/ })

}]);