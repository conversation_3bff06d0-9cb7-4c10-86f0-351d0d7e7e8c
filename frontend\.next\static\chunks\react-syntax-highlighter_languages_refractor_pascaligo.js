"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_pascaligo"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascaligo.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascaligo.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = pascaligo\npascaligo.displayName = 'pascaligo'\npascaligo.aliases = []\nfunction pascaligo(Prism) {\n  ;(function (Prism) {\n    // Pascaligo is a layer 2 smart contract language for the tezos blockchain\n    var braces = /\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\)/.source\n    var type = /(?:\\b\\w+(?:<braces>)?|<braces>)/.source.replace(\n      /<braces>/g,\n      function () {\n        return braces\n      }\n    )\n    var pascaligo = (Prism.languages.pascaligo = {\n      comment: /\\(\\*[\\s\\S]+?\\*\\)|\\/\\/.*/,\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\^[a-z]/i,\n        greedy: true\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(\\btype\\s+\\w+\\s+is\\s+)<type>/.source.replace(\n              /<type>/g,\n              function () {\n                return type\n              }\n            ),\n            'i'\n          ),\n          lookbehind: true,\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /<type>(?=\\s+is\\b)/.source.replace(/<type>/g, function () {\n              return type\n            }),\n            'i'\n          ),\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /(:\\s*)<type>/.source.replace(/<type>/g, function () {\n              return type\n            })\n          ),\n          lookbehind: true,\n          inside: null // see below\n        }\n      ],\n      keyword: {\n        pattern:\n          /(^|[^&])\\b(?:begin|block|case|const|else|end|fail|for|from|function|if|is|nil|of|remove|return|skip|then|type|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      boolean: {\n        pattern: /(^|[^&])\\b(?:False|True)\\b/i,\n        lookbehind: true\n      },\n      builtin: {\n        pattern: /(^|[^&])\\b(?:bool|int|list|map|nat|record|string|unit)\\b/i,\n        lookbehind: true\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: [\n        // Hexadecimal, octal and binary\n        /%[01]+|&[0-7]+|\\$[a-f\\d]+/i, // Decimal\n        /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?(?:mtz|n)?/i\n      ],\n      operator:\n        /->|=\\/=|\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=|]|\\b(?:and|mod|or)\\b/,\n      punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.{}]/\n    })\n    var classNameInside = [\n      'comment',\n      'keyword',\n      'builtin',\n      'operator',\n      'punctuation'\n    ].reduce(function (accum, key) {\n      accum[key] = pascaligo[key]\n      return accum\n    }, {})\n    pascaligo['class-name'].forEach(function (p) {\n      p.inside = classNameInside\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pascaligo.js\n"));

/***/ })

}]);