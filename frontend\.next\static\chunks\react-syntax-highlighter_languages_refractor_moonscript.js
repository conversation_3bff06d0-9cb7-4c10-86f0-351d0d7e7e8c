"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_moonscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/moonscript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/moonscript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = moonscript\nmoonscript.displayName = 'moonscript'\nmoonscript.aliases = ['moon']\nfunction moonscript(Prism) {\n  Prism.languages.moonscript = {\n    comment: /--.*/,\n    string: [\n      {\n        pattern: /'[^']*'|\\[(=*)\\[[\\s\\S]*?\\]\\1\\]/,\n        greedy: true\n      },\n      {\n        pattern: /\"[^\"]*\"/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /#\\{[^{}]*\\}/,\n            inside: {\n              moonscript: {\n                pattern: /(^#\\{)[\\s\\S]+(?=\\})/,\n                lookbehind: true,\n                inside: null // see beow\n              },\n              'interpolation-punctuation': {\n                pattern: /#\\{|\\}/,\n                alias: 'punctuation'\n              }\n            }\n          }\n        }\n      }\n    ],\n    'class-name': [\n      {\n        pattern: /(\\b(?:class|extends)[ \\t]+)\\w+/,\n        lookbehind: true\n      }, // class-like names start with a capital letter\n      /\\b[A-Z]\\w*/\n    ],\n    keyword:\n      /\\b(?:class|continue|do|else|elseif|export|extends|for|from|if|import|in|local|nil|return|self|super|switch|then|unless|using|when|while|with)\\b/,\n    variable: /@@?\\w*/,\n    property: {\n      pattern: /\\b(?!\\d)\\w+(?=:)|(:)(?!\\d)\\w+/,\n      lookbehind: true\n    },\n    function: {\n      pattern:\n        /\\b(?:_G|_VERSION|assert|collectgarbage|coroutine\\.(?:create|resume|running|status|wrap|yield)|debug\\.(?:debug|getfenv|gethook|getinfo|getlocal|getmetatable|getregistry|getupvalue|setfenv|sethook|setlocal|setmetatable|setupvalue|traceback)|dofile|error|getfenv|getmetatable|io\\.(?:close|flush|input|lines|open|output|popen|read|stderr|stdin|stdout|tmpfile|type|write)|ipairs|load|loadfile|loadstring|math\\.(?:abs|acos|asin|atan|atan2|ceil|cos|cosh|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pi|pow|rad|random|randomseed|sin|sinh|sqrt|tan|tanh)|module|next|os\\.(?:clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\.(?:cpath|loaded|loadlib|path|preload|seeall)|pairs|pcall|print|rawequal|rawget|rawset|require|select|setfenv|setmetatable|string\\.(?:byte|char|dump|find|format|gmatch|gsub|len|lower|match|rep|reverse|sub|upper)|table\\.(?:concat|insert|maxn|remove|sort)|tonumber|tostring|type|unpack|xpcall)\\b/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /(?:\\B\\.\\d+|\\b\\d+\\.\\d+|\\b\\d+(?=[eE]))(?:[eE][-+]?\\d+)?\\b|\\b(?:0x[a-fA-F\\d]+|\\d+)(?:U?LL)?\\b/,\n    operator:\n      /\\.{3}|[-=]>|~=|(?:[-+*/%<>!=]|\\.\\.)=?|[:#^]|\\b(?:and|or)\\b=?|\\b(?:not)\\b/,\n    punctuation: /[.,()[\\]{}\\\\]/\n  }\n  Prism.languages.moonscript.string[1].inside.interpolation.inside.moonscript.inside =\n    Prism.languages.moonscript\n  Prism.languages.moon = Prism.languages.moonscript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/moonscript.js\n"));

/***/ })

}]);