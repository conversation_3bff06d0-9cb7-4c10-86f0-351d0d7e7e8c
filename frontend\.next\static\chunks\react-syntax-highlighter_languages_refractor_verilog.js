"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_verilog"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/verilog.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/verilog.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = verilog\nverilog.displayName = 'verilog'\nverilog.aliases = []\nfunction verilog(Prism) {\n  Prism.languages.verilog = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'kernel-function': {\n      // support for any kernel function (ex: $display())\n      pattern: /\\B\\$\\w+\\b/,\n      alias: 'property'\n    },\n    // support for user defined constants (ex: `define)\n    constant: /\\B`\\w+\\b/,\n    function: /\\b\\w+(?=\\()/,\n    // support for verilog and system verilog keywords\n    keyword:\n      /\\b(?:alias|and|assert|assign|assume|automatic|before|begin|bind|bins|binsof|bit|break|buf|bufif0|bufif1|byte|case|casex|casez|cell|chandle|class|clocking|cmos|config|const|constraint|context|continue|cover|covergroup|coverpoint|cross|deassign|default|defparam|design|disable|dist|do|edge|else|end|endcase|endclass|endclocking|endconfig|endfunction|endgenerate|endgroup|endinterface|endmodule|endpackage|endprimitive|endprogram|endproperty|endsequence|endspecify|endtable|endtask|enum|event|expect|export|extends|extern|final|first_match|for|force|foreach|forever|fork|forkjoin|function|generate|genvar|highz0|highz1|if|iff|ifnone|ignore_bins|illegal_bins|import|incdir|include|initial|inout|input|inside|instance|int|integer|interface|intersect|join|join_any|join_none|large|liblist|library|local|localparam|logic|longint|macromodule|matches|medium|modport|module|nand|negedge|new|nmos|nor|noshowcancelled|not|notif0|notif1|null|or|output|package|packed|parameter|pmos|posedge|primitive|priority|program|property|protected|pull0|pull1|pulldown|pullup|pulsestyle_ondetect|pulsestyle_onevent|pure|rand|randc|randcase|randsequence|rcmos|real|realtime|ref|reg|release|repeat|return|rnmos|rpmos|rtran|rtranif0|rtranif1|scalared|sequence|shortint|shortreal|showcancelled|signed|small|solve|specify|specparam|static|string|strong0|strong1|struct|super|supply0|supply1|table|tagged|task|this|throughout|time|timeprecision|timeunit|tran|tranif0|tranif1|tri|tri0|tri1|triand|trior|trireg|type|typedef|union|unique|unsigned|use|uwire|var|vectored|virtual|void|wait|wait_order|wand|weak0|weak1|while|wildcard|wire|with|within|wor|xnor|xor)\\b/,\n    // bold highlighting for all verilog and system verilog logic blocks\n    important: /\\b(?:always|always_comb|always_ff|always_latch)\\b(?: *@)?/,\n    // support for time ticks, vectors, and real numbers\n    number:\n      /\\B##?\\d+|(?:\\b\\d+)?'[odbh] ?[\\da-fzx_?]+|\\b(?:\\d*[._])?\\d+(?:e[-+]?\\d+)?/i,\n    operator: /[-+{}^~%*\\/?=!<>&|]+/,\n    punctuation: /[[\\];(),.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/verilog.js\n"));

/***/ })

}]);