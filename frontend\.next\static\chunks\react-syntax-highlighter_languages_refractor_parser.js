"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_parser"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parser.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parser.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = parser\nparser.displayName = 'parser'\nparser.aliases = []\nfunction parser(Prism) {\n  ;(function (Prism) {\n    var parser = (Prism.languages.parser = Prism.languages.extend('markup', {\n      keyword: {\n        pattern:\n          /(^|[^^])(?:\\^(?:case|eval|for|if|switch|throw)\\b|@(?:BASE|CLASS|GET(?:_DEFAULT)?|OPTIONS|SET_DEFAULT|USE)\\b)/,\n        lookbehind: true\n      },\n      variable: {\n        pattern: /(^|[^^])\\B\\$(?:\\w+|(?=[.{]))(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\.|:+/\n        }\n      },\n      function: {\n        pattern: /(^|[^^])\\B[@^]\\w+(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /(^@)(?:GET_|SET_)/,\n            lookbehind: true\n          },\n          punctuation: /\\.|:+/\n        }\n      },\n      escape: {\n        pattern: /\\^(?:[$^;@()\\[\\]{}\"':]|#[a-f\\d]*)/i,\n        alias: 'builtin'\n      },\n      punctuation: /[\\[\\](){};]/\n    }))\n    parser = Prism.languages.insertBefore('parser', 'keyword', {\n      'parser-comment': {\n        pattern: /(\\s)#.*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      expression: {\n        // Allow for 3 levels of depth\n        pattern: /(^|[^^])\\((?:[^()]|\\((?:[^()]|\\((?:[^()])*\\))*\\))*\\)/,\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          string: {\n            pattern: /(^|[^^])([\"'])(?:(?!\\2)[^^]|\\^[\\s\\S])*\\2/,\n            lookbehind: true\n          },\n          keyword: parser.keyword,\n          variable: parser.variable,\n          function: parser.function,\n          boolean: /\\b(?:false|true)\\b/,\n          number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?)\\b/i,\n          escape: parser.escape,\n          operator:\n            /[~+*\\/\\\\%]|!(?:\\|\\|?|=)?|&&?|\\|\\|?|==|<[<=]?|>[>=]?|-[fd]?|\\b(?:def|eq|ge|gt|in|is|le|lt|ne)\\b/,\n          punctuation: parser.punctuation\n        }\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        expression: parser.expression,\n        keyword: parser.keyword,\n        variable: parser.variable,\n        function: parser.function,\n        escape: parser.escape,\n        'parser-punctuation': {\n          pattern: parser.punctuation,\n          alias: 'punctuation'\n        }\n      },\n      parser['tag'].inside['attr-value']\n    )\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSwyQkFBMkIsU0FBUztBQUNwQztBQUNBLE9BQU87QUFDUCw4QkFBOEI7QUFDOUIsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFxwYXJzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcGFyc2VyXG5wYXJzZXIuZGlzcGxheU5hbWUgPSAncGFyc2VyJ1xucGFyc2VyLmFsaWFzZXMgPSBbXVxuZnVuY3Rpb24gcGFyc2VyKFByaXNtKSB7XG4gIDsoZnVuY3Rpb24gKFByaXNtKSB7XG4gICAgdmFyIHBhcnNlciA9IChQcmlzbS5sYW5ndWFnZXMucGFyc2VyID0gUHJpc20ubGFuZ3VhZ2VzLmV4dGVuZCgnbWFya3VwJywge1xuICAgICAga2V5d29yZDoge1xuICAgICAgICBwYXR0ZXJuOlxuICAgICAgICAgIC8oXnxbXl5dKSg/OlxcXig/OmNhc2V8ZXZhbHxmb3J8aWZ8c3dpdGNofHRocm93KVxcYnxAKD86QkFTRXxDTEFTU3xHRVQoPzpfREVGQVVMVCk/fE9QVElPTlN8U0VUX0RFRkFVTFR8VVNFKVxcYikvLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICB9LFxuICAgICAgdmFyaWFibGU6IHtcbiAgICAgICAgcGF0dGVybjogLyhefFteXl0pXFxCXFwkKD86XFx3K3woPz1bLntdKSkoPzooPzpcXC58Ojo/KVxcdyspKig/OlxcLnw6Oj8pPy8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZToge1xuICAgICAgICAgIHB1bmN0dWF0aW9uOiAvXFwufDorL1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgZnVuY3Rpb246IHtcbiAgICAgICAgcGF0dGVybjogLyhefFteXl0pXFxCW0BeXVxcdysoPzooPzpcXC58Ojo/KVxcdyspKig/OlxcLnw6Oj8pPy8sXG4gICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgIGluc2lkZToge1xuICAgICAgICAgIGtleXdvcmQ6IHtcbiAgICAgICAgICAgIHBhdHRlcm46IC8oXkApKD86R0VUX3xTRVRfKS8sXG4gICAgICAgICAgICBsb29rYmVoaW5kOiB0cnVlXG4gICAgICAgICAgfSxcbiAgICAgICAgICBwdW5jdHVhdGlvbjogL1xcLnw6Ky9cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIGVzY2FwZToge1xuICAgICAgICBwYXR0ZXJuOiAvXFxeKD86WyReO0AoKVxcW1xcXXt9XCInOl18I1thLWZcXGRdKikvaSxcbiAgICAgICAgYWxpYXM6ICdidWlsdGluJ1xuICAgICAgfSxcbiAgICAgIHB1bmN0dWF0aW9uOiAvW1xcW1xcXSgpe307XS9cbiAgICB9KSlcbiAgICBwYXJzZXIgPSBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCdwYXJzZXInLCAna2V5d29yZCcsIHtcbiAgICAgICdwYXJzZXItY29tbWVudCc6IHtcbiAgICAgICAgcGF0dGVybjogLyhcXHMpIy4qLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgYWxpYXM6ICdjb21tZW50J1xuICAgICAgfSxcbiAgICAgIGV4cHJlc3Npb246IHtcbiAgICAgICAgLy8gQWxsb3cgZm9yIDMgbGV2ZWxzIG9mIGRlcHRoXG4gICAgICAgIHBhdHRlcm46IC8oXnxbXl5dKVxcKCg/OlteKCldfFxcKCg/OlteKCldfFxcKCg/OlteKCldKSpcXCkpKlxcKSkqXFwpLyxcbiAgICAgICAgZ3JlZWR5OiB0cnVlLFxuICAgICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICBzdHJpbmc6IHtcbiAgICAgICAgICAgIHBhdHRlcm46IC8oXnxbXl5dKShbXCInXSkoPzooPyFcXDIpW15eXXxcXF5bXFxzXFxTXSkqXFwyLyxcbiAgICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWVcbiAgICAgICAgICB9LFxuICAgICAgICAgIGtleXdvcmQ6IHBhcnNlci5rZXl3b3JkLFxuICAgICAgICAgIHZhcmlhYmxlOiBwYXJzZXIudmFyaWFibGUsXG4gICAgICAgICAgZnVuY3Rpb246IHBhcnNlci5mdW5jdGlvbixcbiAgICAgICAgICBib29sZWFuOiAvXFxiKD86ZmFsc2V8dHJ1ZSlcXGIvLFxuICAgICAgICAgIG51bWJlcjogL1xcYig/OjB4W2EtZlxcZF0rfFxcZCsoPzpcXC5cXGQqKT8oPzplWystXT9cXGQrKT8pXFxiL2ksXG4gICAgICAgICAgZXNjYXBlOiBwYXJzZXIuZXNjYXBlLFxuICAgICAgICAgIG9wZXJhdG9yOlxuICAgICAgICAgICAgL1t+KypcXC9cXFxcJV18ISg/OlxcfFxcfD98PSk/fCYmP3xcXHxcXHw/fD09fDxbPD1dP3w+Wz49XT98LVtmZF0/fFxcYig/OmRlZnxlcXxnZXxndHxpbnxpc3xsZXxsdHxuZSlcXGIvLFxuICAgICAgICAgIHB1bmN0dWF0aW9uOiBwYXJzZXIucHVuY3R1YXRpb25cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG4gICAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZShcbiAgICAgICdpbnNpZGUnLFxuICAgICAgJ3B1bmN0dWF0aW9uJyxcbiAgICAgIHtcbiAgICAgICAgZXhwcmVzc2lvbjogcGFyc2VyLmV4cHJlc3Npb24sXG4gICAgICAgIGtleXdvcmQ6IHBhcnNlci5rZXl3b3JkLFxuICAgICAgICB2YXJpYWJsZTogcGFyc2VyLnZhcmlhYmxlLFxuICAgICAgICBmdW5jdGlvbjogcGFyc2VyLmZ1bmN0aW9uLFxuICAgICAgICBlc2NhcGU6IHBhcnNlci5lc2NhcGUsXG4gICAgICAgICdwYXJzZXItcHVuY3R1YXRpb24nOiB7XG4gICAgICAgICAgcGF0dGVybjogcGFyc2VyLnB1bmN0dWF0aW9uLFxuICAgICAgICAgIGFsaWFzOiAncHVuY3R1YXRpb24nXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBwYXJzZXJbJ3RhZyddLmluc2lkZVsnYXR0ci12YWx1ZSddXG4gICAgKVxuICB9KShQcmlzbSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/parser.js\n"));

/***/ })

}]);