"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_powershell"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powershell.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powershell.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = powershell\npowershell.displayName = 'powershell'\npowershell.aliases = []\nfunction powershell(Prism) {\n  ;(function (Prism) {\n    var powershell = (Prism.languages.powershell = {\n      comment: [\n        {\n          pattern: /(^|[^`])<#[\\s\\S]*?#>/,\n          lookbehind: true\n        },\n        {\n          pattern: /(^|[^`])#.*/,\n          lookbehind: true\n        }\n      ],\n      string: [\n        {\n          pattern: /\"(?:`[\\s\\S]|[^`\"])*\"/,\n          greedy: true,\n          inside: null // see below\n        },\n        {\n          pattern: /'(?:[^']|'')*'/,\n          greedy: true\n        }\n      ],\n      // Matches name spaces as well as casts, attribute decorators. Force starting with letter to avoid matching array indices\n      // Supports two levels of nested brackets (e.g. `[OutputType([System.Collections.Generic.List[int]])]`)\n      namespace: /\\[[a-z](?:\\[(?:\\[[^\\]]*\\]|[^\\[\\]])*\\]|[^\\[\\]])*\\]/i,\n      boolean: /\\$(?:false|true)\\b/i,\n      variable: /\\$\\w+\\b/,\n      // Cmdlets and aliases. Aliases should come last, otherwise \"write\" gets preferred over \"write-host\" for example\n      // Get-Command | ?{ $_.ModuleName -match \"Microsoft.PowerShell.(Util|Core|Management)\" }\n      // Get-Alias | ?{ $_.ReferencedCommand.Module.Name -match \"Microsoft.PowerShell.(Util|Core|Management)\" }\n      function: [\n        /\\b(?:Add|Approve|Assert|Backup|Block|Checkpoint|Clear|Close|Compare|Complete|Compress|Confirm|Connect|Convert|ConvertFrom|ConvertTo|Copy|Debug|Deny|Disable|Disconnect|Dismount|Edit|Enable|Enter|Exit|Expand|Export|Find|ForEach|Format|Get|Grant|Group|Hide|Import|Initialize|Install|Invoke|Join|Limit|Lock|Measure|Merge|Move|New|Open|Optimize|Out|Ping|Pop|Protect|Publish|Push|Read|Receive|Redo|Register|Remove|Rename|Repair|Request|Reset|Resize|Resolve|Restart|Restore|Resume|Revoke|Save|Search|Select|Send|Set|Show|Skip|Sort|Split|Start|Step|Stop|Submit|Suspend|Switch|Sync|Tee|Test|Trace|Unblock|Undo|Uninstall|Unlock|Unprotect|Unpublish|Unregister|Update|Use|Wait|Watch|Where|Write)-[a-z]+\\b/i,\n        /\\b(?:ac|cat|chdir|clc|cli|clp|clv|compare|copy|cp|cpi|cpp|cvpa|dbp|del|diff|dir|ebp|echo|epal|epcsv|epsn|erase|fc|fl|ft|fw|gal|gbp|gc|gci|gcs|gdr|gi|gl|gm|gp|gps|group|gsv|gu|gv|gwmi|iex|ii|ipal|ipcsv|ipsn|irm|iwmi|iwr|kill|lp|ls|measure|mi|mount|move|mp|mv|nal|ndr|ni|nv|ogv|popd|ps|pushd|pwd|rbp|rd|rdr|ren|ri|rm|rmdir|rni|rnp|rp|rv|rvpa|rwmi|sal|saps|sasv|sbp|sc|select|set|shcm|si|sl|sleep|sls|sort|sp|spps|spsv|start|sv|swmi|tee|trcm|type|write)\\b/i\n      ],\n      // per http://technet.microsoft.com/en-us/library/hh847744.aspx\n      keyword:\n        /\\b(?:Begin|Break|Catch|Class|Continue|Data|Define|Do|DynamicParam|Else|ElseIf|End|Exit|Filter|Finally|For|ForEach|From|Function|If|InlineScript|Parallel|Param|Process|Return|Sequence|Switch|Throw|Trap|Try|Until|Using|Var|While|Workflow)\\b/i,\n      operator: {\n        pattern:\n          /(^|\\W)(?:!|-(?:b?(?:and|x?or)|as|(?:Not)?(?:Contains|In|Like|Match)|eq|ge|gt|is(?:Not)?|Join|le|lt|ne|not|Replace|sh[lr])\\b|-[-=]?|\\+[+=]?|[*\\/%]=?)/i,\n        lookbehind: true\n      },\n      punctuation: /[|{}[\\];(),.]/\n    }) // Variable interpolation inside strings, and nested expressions\n    powershell.string[0].inside = {\n      function: {\n        // Allow for one level of nesting\n        pattern: /(^|[^`])\\$\\((?:\\$\\([^\\r\\n()]*\\)|(?!\\$\\()[^\\r\\n)])*\\)/,\n        lookbehind: true,\n        inside: powershell\n      },\n      boolean: powershell.boolean,\n      variable: powershell.variable\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powershell.js\n"));

/***/ })

}]);