"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_n1ql"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/n1ql.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/n1ql.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = n1ql\nn1ql.displayName = 'n1ql'\nn1ql.aliases = []\nfunction n1ql(Prism) {\n  // https://docs.couchbase.com/server/current/n1ql/n1ql-language-reference/index.html\n  Prism.languages.n1ql = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?(?:$|\\*\\/)|--.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\]|\\1\\1)*\\1/,\n      greedy: true\n    },\n    identifier: {\n      pattern: /`(?:\\\\[\\s\\S]|[^\\\\`]|``)*`/,\n      greedy: true\n    },\n    parameter: /\\$[\\w.]+/,\n    // https://docs.couchbase.com/server/current/n1ql/n1ql-language-reference/reservedwords.html#n1ql-reserved-words\n    keyword:\n      /\\b(?:ADVISE|ALL|ALTER|ANALYZE|AS|ASC|AT|BEGIN|BINARY|BOOLEAN|BREAK|BUCKET|BUILD|BY|CALL|CAST|CLUSTER|COLLATE|COLLECTION|COMMIT|COMMITTED|CONNECT|CONTINUE|CORRELATE|CORRELATED|COVER|CREATE|CURRENT|DATABASE|DATASET|DATASTORE|DECLARE|DECREMENT|DELETE|DERIVED|DESC|DESCRIBE|DISTINCT|DO|DROP|EACH|ELEMENT|EXCEPT|EXCLUDE|EXECUTE|EXPLAIN|FETCH|FILTER|FLATTEN|FLUSH|FOLLOWING|FOR|FORCE|FROM|FTS|FUNCTION|GOLANG|GRANT|GROUP|GROUPS|GSI|HASH|HAVING|IF|IGNORE|ILIKE|INCLUDE|INCREMENT|INDEX|INFER|INLINE|INNER|INSERT|INTERSECT|INTO|IS|ISOLATION|JAVASCRIPT|JOIN|KEY|KEYS|KEYSPACE|KNOWN|LANGUAGE|LAST|LEFT|LET|LETTING|LEVEL|LIMIT|LSM|MAP|MAPPING|MATCHED|MATERIALIZED|MERGE|MINUS|MISSING|NAMESPACE|NEST|NL|NO|NTH_VALUE|NULL|NULLS|NUMBER|OBJECT|OFFSET|ON|OPTION|OPTIONS|ORDER|OTHERS|OUTER|OVER|PARSE|PARTITION|PASSWORD|PATH|POOL|PRECEDING|PREPARE|PRIMARY|PRIVATE|PRIVILEGE|PROBE|PROCEDURE|PUBLIC|RANGE|RAW|REALM|REDUCE|RENAME|RESPECT|RETURN|RETURNING|REVOKE|RIGHT|ROLE|ROLLBACK|ROW|ROWS|SATISFIES|SAVEPOINT|SCHEMA|SCOPE|SELECT|SELF|SEMI|SET|SHOW|SOME|START|STATISTICS|STRING|SYSTEM|TIES|TO|TRAN|TRANSACTION|TRIGGER|TRUNCATE|UNBOUNDED|UNDER|UNION|UNIQUE|UNKNOWN|UNNEST|UNSET|UPDATE|UPSERT|USE|USER|USING|VALIDATE|VALUE|VALUES|VIA|VIEW|WHERE|WHILE|WINDOW|WITH|WORK|XOR)\\b/i,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:FALSE|TRUE)\\b/i,\n    number: /(?:\\b\\d+\\.|\\B\\.)\\d+e[+\\-]?\\d+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i,\n    operator:\n      /[-+*\\/%]|!=|==?|\\|\\||<[>=]?|>=?|\\b(?:AND|ANY|ARRAY|BETWEEN|CASE|ELSE|END|EVERY|EXISTS|FIRST|IN|LIKE|NOT|OR|THEN|VALUED|WHEN|WITHIN)\\b/i,\n    punctuation: /[;[\\](),.{}:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/n1ql.js\n"));

/***/ })

}]);