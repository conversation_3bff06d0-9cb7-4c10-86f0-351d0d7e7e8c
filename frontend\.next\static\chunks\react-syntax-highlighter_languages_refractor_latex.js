"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_latex"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/latex.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/latex.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = latex\nlatex.displayName = 'latex'\nlatex.aliases = ['tex', 'context']\nfunction latex(Prism) {\n  ;(function (Prism) {\n    var funcPattern = /\\\\(?:[^a-z()[\\]]|[a-z*]+)/i\n    var insideEqu = {\n      'equation-command': {\n        pattern: funcPattern,\n        alias: 'regex'\n      }\n    }\n    Prism.languages.latex = {\n      comment: /%.*/,\n      // the verbatim environment prints whitespace to the document\n      cdata: {\n        pattern:\n          /(\\\\begin\\{((?:lstlisting|verbatim)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n        lookbehind: true\n      },\n      /*\n       * equations can be between $$ $$ or $ $ or \\( \\) or \\[ \\]\n       * (all are multiline)\n       */\n      equation: [\n        {\n          pattern:\n            /\\$\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$\\$|\\$(?:\\\\[\\s\\S]|[^\\\\$])+\\$|\\\\\\([\\s\\S]*?\\\\\\)|\\\\\\[[\\s\\S]*?\\\\\\]/,\n          inside: insideEqu,\n          alias: 'string'\n        },\n        {\n          pattern:\n            /(\\\\begin\\{((?:align|eqnarray|equation|gather|math|multline)\\*?)\\})[\\s\\S]*?(?=\\\\end\\{\\2\\})/,\n          lookbehind: true,\n          inside: insideEqu,\n          alias: 'string'\n        }\n      ],\n      /*\n       * arguments which are keywords or references are highlighted\n       * as keywords\n       */\n      keyword: {\n        pattern:\n          /(\\\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      url: {\n        pattern: /(\\\\url\\{)[^}]+(?=\\})/,\n        lookbehind: true\n      },\n      /*\n       * section or chapter headlines are highlighted as bold so that\n       * they stand out more\n       */\n      headline: {\n        pattern:\n          /(\\\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\\*?(?:\\[[^\\]]+\\])?\\{)[^}]+(?=\\})/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      function: {\n        pattern: funcPattern,\n        alias: 'selector'\n      },\n      punctuation: /[[\\]{}&]/\n    }\n    Prism.languages.tex = Prism.languages.latex\n    Prism.languages.context = Prism.languages.latex\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/latex.js\n"));

/***/ })

}]);