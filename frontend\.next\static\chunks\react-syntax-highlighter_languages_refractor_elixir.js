"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_elixir"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elixir.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elixir.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = elixir\nelixir.displayName = 'elixir'\nelixir.aliases = []\nfunction elixir(Prism) {\n  Prism.languages.elixir = {\n    doc: {\n      pattern:\n        /@(?:doc|moduledoc)\\s+(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2)/,\n      inside: {\n        attribute: /^@\\w+/,\n        string: /['\"][\\s\\S]+/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    // ~r\"\"\"foo\"\"\" (multi-line), ~r'''foo''' (multi-line), ~r/foo/, ~r|foo|, ~r\"foo\", ~r'foo', ~r(foo), ~r[foo], ~r{foo}, ~r<foo>\n    regex: {\n      pattern:\n        /~[rR](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|[^\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[uismxfr]*/,\n      greedy: true\n    },\n    string: [\n      {\n        // ~s\"\"\"foo\"\"\" (multi-line), ~s'''foo''' (multi-line), ~s/foo/, ~s|foo|, ~s\"foo\", ~s'foo', ~s(foo), ~s[foo], ~s{foo} (with interpolation care), ~s<foo>\n        pattern:\n          /~[cCsSwW](?:(\"\"\"|''')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1|([\\/|\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])+\\2|\\((?:\\\\.|[^\\\\)\\r\\n])+\\)|\\[(?:\\\\.|[^\\\\\\]\\r\\n])+\\]|\\{(?:\\\\.|#\\{[^}]+\\}|#(?!\\{)|[^#\\\\}\\r\\n])+\\}|<(?:\\\\.|[^\\\\>\\r\\n])+>)[csa]?/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        pattern: /(\"\"\"|''')[\\s\\S]*?\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      },\n      {\n        // Multi-line strings are allowed\n        pattern: /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true,\n        inside: {\n          // See interpolation below\n        }\n      }\n    ],\n    atom: {\n      // Look-behind prevents bad highlighting of the :: operator\n      pattern: /(^|[^:]):\\w+/,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    module: {\n      pattern: /\\b[A-Z]\\w*\\b/,\n      alias: 'class-name'\n    },\n    // Look-ahead prevents bad highlighting of the :: operator\n    'attr-name': /\\b\\w+\\??:(?!:)/,\n    argument: {\n      // Look-behind prevents bad highlighting of the && operator\n      pattern: /(^|[^&])&\\d+/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'variable'\n    },\n    function: /\\b[_a-zA-Z]\\w*[?!]?(?:(?=\\s*(?:\\.\\s*)?\\()|(?=\\/\\d))/,\n    number: /\\b(?:0[box][a-f\\d_]+|\\d[\\d_]*)(?:\\.[\\d_]+)?(?:e[+-]?[\\d_]+)?\\b/i,\n    keyword:\n      /\\b(?:after|alias|and|case|catch|cond|def(?:callback|delegate|exception|impl|macro|module|n|np|p|protocol|struct)?|do|else|end|fn|for|if|import|not|or|quote|raise|require|rescue|try|unless|unquote|use|when)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    operator: [\n      /\\bin\\b|&&?|\\|[|>]?|\\\\\\\\|::|\\.\\.\\.?|\\+\\+?|-[->]?|<[-=>]|>=|!==?|\\B!|=(?:==?|[>~])?|[*\\/^]/,\n      {\n        // We don't want to match <<\n        pattern: /([^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /([^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /<<|>>|[.,%\\[\\]{}()]/\n  }\n  Prism.languages.elixir.string.forEach(function (o) {\n    o.inside = {\n      interpolation: {\n        pattern: /#\\{[^}]+\\}/,\n        inside: {\n          delimiter: {\n            pattern: /^#\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.elixir\n        }\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/elixir.js\n"));

/***/ })

}]);