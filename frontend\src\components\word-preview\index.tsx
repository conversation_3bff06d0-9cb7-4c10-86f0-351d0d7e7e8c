"use client";

import React, { useState, useEffect, useMemo, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  ChevronRight, 
  ChevronDown, 
  RefreshCw,
  Upload,
  Download,
  Eye,
  EyeOff
} from "lucide-react";
import { cn } from "@/lib/utils";
import FileUpload from "./file-upload";

interface WordSection {
  id: string;
  title: string;
  content: string;
  html?: string;
  level: number;
  wordCount: number;
  selected?: boolean;
}

interface WordDocument {
  id: string;
  name: string;
  path: string;
  sections: WordSection[];
  totalWords: number;
  lastModified: Date;
}

interface WordPreviewProps {
  className?: string;
  onSectionSelect?: (section: WordSection) => void;
  onDocumentLoad?: (document: WordDocument) => void;
  onStartEditing?: (sectionId: string) => void;
}

export function WordPreview({ className, onSectionSelect, onDocumentLoad, onStartEditing }: WordPreviewProps) {
  const [document, setDocument] = useState<WordDocument | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState<'split' | 'sections' | 'content'>('split');
  const [showUpload, setShowUpload] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedHtml, setEditedHtml] = useState<string>("");
  const editableRef = useRef<HTMLDivElement | null>(null);

  // 加载Word文档并进行章节分割
  const loadDocument = async (filePath: string) => {
    setIsLoading(true);
    try {
      // 调用后端API来加载和解析Word文档
      const response = await fetch('/api/word/load-and-split', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          file_path: filePath,
          max_tokens_per_section: 30000
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.document) {
        const loadedDocument: WordDocument = {
          id: result.document_id,
          name: result.document.name,
          path: filePath,
          sections: result.document.sections.map((section: any) => ({
            id: section.id,
            title: section.title,
            content: section.content,
            html: section.html,
            level: section.level,
            wordCount: section.word_count
          })),
          totalWords: result.document.total_words,
          lastModified: new Date()
        };

        setDocument(loadedDocument);
        // 默认选择第一节，便于立即预览内容
        if (loadedDocument.sections && loadedDocument.sections.length > 0) {
          const first = loadedDocument.sections[0];
          setSelectedSection(first.id);
          setEditedHtml(first.html || "");
          try {
            window.localStorage.setItem('word:currentSectionId', first.id);
            window.localStorage.setItem('word:currentSectionTitle', first.title);
            window.localStorage.setItem('word:currentSectionContent', first.content || '');
          } catch {}
        }
        // 将当前文档上下文写入本地存储，供聊天侧读取
        try {
          window.localStorage.setItem('word:docId', loadedDocument.id);
          window.localStorage.setItem('word:docName', loadedDocument.name);
          window.localStorage.setItem('word:currentFilePath', filePath);
        } catch {}
        onDocumentLoad?.(loadedDocument);
      } else {
        throw new Error(result.message || '文档加载失败');
      }
    } catch (error) {
      console.error("Failed to load document:", error);
      // 显示错误信息给用户
      alert(`文档加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSectionClick = (section: WordSection) => {
    setSelectedSection(section.id);
    setIsEditing(false);
    setEditedHtml(section.html || "");
    onSectionSelect?.(section);
    // 同步所选章节到本地存储，供聊天侧上下文使用
    try {
      window.localStorage.setItem('word:currentSectionId', section.id);
      window.localStorage.setItem('word:currentSectionTitle', section.title);
      window.localStorage.setItem('word:currentSectionContent', section.content || '');
    } catch {}
  };

  const toggleSectionExpansion = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleFileSelect = (file: File) => {
    console.log("Selected file:", file.name);
    // 这里可以进行文件预处理
  };

  const handleFileLoad = async (filePath: string) => {
    console.log("Loading file:", filePath);
    // 调用loadDocument来处理上传的文件
    await loadDocument(filePath);
    setShowUpload(false);
  };

  const handleStartEditing = async (sectionId: string) => {
    console.log("Starting to edit section:", sectionId);
    // 通知后端选择章节
    try {
      if (!document) return;
      const res = await fetch('/api/word/select-section', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ document_id: document.id, section_id: sectionId })
      });
      if (!res.ok) throw new Error(`选择章节失败: ${res.status}`);
      const data = await res.json();
      if (!data.success) throw new Error(data.message || '选择章节失败');
      onStartEditing?.(sectionId);
      alert(`开始编辑章节: ${document.sections.find(s => s.id === sectionId)?.title}`);
    } catch (e) {
      alert(e instanceof Error ? e.message : '选择章节失败');
    }
  };

  const renderSection = (section: WordSection) => {
    const isExpanded = expandedSections.has(section.id);
    const isSelected = selectedSection === section.id;
    const hasSubsections = document?.sections.some(s => 
      s.level > section.level && s.id.startsWith(section.id.split('-')[0])
    );

    return (
      <div key={section.id} className="mb-2">
        <div
          className={cn(
            "flex items-center gap-2 p-2 rounded-md cursor-pointer hover:bg-gray-50 transition-colors",
            isSelected && "bg-blue-50 border border-blue-200",
            `ml-${(section.level - 1) * 4}`
          )}
          onClick={() => handleSectionClick(section)}
        >
          {hasSubsections && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation();
                toggleSectionExpansion(section.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="flex-1 text-sm font-medium">{section.title}</span>
          <Badge variant="secondary" className="text-xs">
            {section.wordCount}字
          </Badge>
        </div>
        
        {previewMode === 'content' && isSelected && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md ml-6">
            <p className="text-sm text-gray-700 line-clamp-3">
              {section.content}
            </p>
          </div>
        )}
      </div>
    );
  };

  // 当左侧对话触发文档更新（如MCP编辑完成）时，监听刷新事件
  useEffect(() => {
    const onRefresh = () => {
      if (document?.path) {
        loadDocument(document.path);
      }
    };
    window.addEventListener('word-doc-refresh', onRefresh);
    return () => window.removeEventListener('word-doc-refresh', onRefresh);
  }, [document?.path]);

  // 选中章节变化时，同步编辑HTML
  useEffect(() => {
    if (!document || !selectedSection) return;
    const sec = document.sections.find(s => s.id === selectedSection);
    setEditedHtml(sec?.html || "");
  }, [selectedSection, document?.id]);

  const selectedSectionObj = useMemo(() => {
    return document?.sections.find(s => s.id === selectedSection) || null;
  }, [document?.sections, selectedSection]);

  const handleSave = async () => {
    if (!document || !selectedSectionObj) return;
    try {
      const res = await fetch('/api/word/update-section', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          file_path: document.path,
          section_id: selectedSectionObj.id,
          new_html: editedHtml,
        })
      });
      if (!res.ok) throw new Error(`保存失败: ${res.status}`);
      const data = await res.json();
      if (!data.success) throw new Error(data.message || '保存失败');
      // 前端本地更新，避免整页刷新导致闪烁
      setDocument(prev => {
        if (!prev) return prev;
        const nextSections = prev.sections.map(s => {
          if (s.id !== selectedSectionObj.id) return s;
          // 提取纯文本用于统计和聊天上下文
          const temp = window.document.createElement('div');
          temp.innerHTML = editedHtml;
          const text = temp.innerText || temp.textContent || '';
          const charCount = text.replace(/\s+/g, '').length;
          return { ...s, html: editedHtml, content: text, wordCount: charCount };
        });
        return { ...prev, sections: nextSections, lastModified: new Date() };
      });
      // 同步本地存储供聊天侧使用
      try {
        const temp = window.document.createElement('div');
        temp.innerHTML = editedHtml;
        const text = temp.innerText || temp.textContent || '';
        window.localStorage.setItem('word:currentSectionContent', text);
      } catch {}
      setIsEditing(false);
    } catch (e) {
      alert(e instanceof Error ? e.message : '保存失败');
    }
  };

  // 移除自动加载默认文档，等待用户上传

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-gray-900">Word文档预览</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowUpload(!showUpload)}
          >
            <Upload className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            title={previewMode === 'split' ? '切换为仅列表' : previewMode === 'sections' ? '切换为仅内容' : '切换为分屏'}
            onClick={() => {
              setPreviewMode(prev => prev === 'split' ? 'sections' : prev === 'sections' ? 'content' : 'split');
            }}
          >
            {previewMode === 'sections' ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => document?.path && loadDocument(document.path)}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
          {document?.path && (
            <Button
              variant="ghost"
              size="sm"
              onClick={async () => {
                try {
                  const url = `/api/word/download?file_path=${encodeURIComponent(document.path)}`;
                  const res = await fetch(url);
                  if (!res.ok) throw new Error(`下载失败: ${res.status}`);
                  const blob = await res.blob();
                  const link = window.document.createElement('a');
                  const objectUrl = URL.createObjectURL(blob);
                  link.href = objectUrl;
                  const fallbackName = document.name || 'document.docx';
                  link.download = fallbackName;
                  window.document.body.appendChild(link);
                  link.click();
                  link.remove();
                  URL.revokeObjectURL(objectUrl);
                } catch (e) {
                  alert(e instanceof Error ? e.message : '下载失败');
                }
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 文档信息 */}
      {document && (
        <div className="p-4 bg-gray-50 border-b">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-gray-900">{document.name}</h3>
            <Badge variant="outline">{document.totalWords}字</Badge>
          </div>
          <p className="text-sm text-gray-500">
            最后修改: {document.lastModified.toLocaleString()}
          </p>
        </div>
      )}

      {/* 文件上传区域 */}
      {showUpload && (
        <div className="p-4 border-b bg-gray-50">
          <FileUpload
            onFileSelect={handleFileSelect}
            onFileLoad={handleFileLoad}
            maxSize={50}
            accept=".doc,.docx"
          />
        </div>
      )}

      {/* 主体内容：支持 分屏/仅列表/仅内容 三种模式 */}
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">加载中...</span>
        </div>
      ) : document ? (
        <div className={cn("flex-1 grid", previewMode === 'split' ? "grid-cols-2" : "grid-cols-1")}> 
          {(previewMode === 'split' || previewMode === 'sections') && (
            <ScrollArea className="p-4 border-r h-full max-h-full overflow-y-auto scrollbar-pretty">
              <div className="space-y-1">
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">文档章节</h4>
                  <Separator />
                </div>
                {document.sections.map(renderSection)}
              </div>
            </ScrollArea>
          )}
          {(previewMode === 'split' || previewMode === 'content') && (
            <ScrollArea className="p-4 h-full max-h-full overflow-y-auto scrollbar-pretty">
              {selectedSectionObj ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-700">章节内容</h4>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant={isEditing ? 'secondary' : 'outline'} onClick={() => setIsEditing(v => !v)}>
                        {isEditing ? '退出编辑' : '编辑'}
                      </Button>
                      {isEditing && (
                        <Button size="sm" variant="default" onClick={handleSave}>保存</Button>
                      )}
                    </div>
                  </div>
                  <Separator />
                  <div className="flex w-full justify-center">
                    <div
                      className={cn(
                        "max-w-none w-[794px] bg-white shadow-sm border rounded-lg p-6",
                        "selection:bg-yellow-200 selection:text-black",
                      )}
                    >
                      <div
                        ref={editableRef}
                        contentEditable={isEditing}
                        suppressContentEditableWarning
                        className={cn(
                          "min-h-[600px] outline-none", // 类似A4宽度 794px
                          isEditing ? "cursor-text" : "cursor-default",
                        )}
                        onInput={(e) => {
                          const el = e.currentTarget as HTMLDivElement;
                          setEditedHtml(el.innerHTML);
                        }}
                        dangerouslySetInnerHTML={{ __html: editedHtml || selectedSectionObj.html || '' }}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-32 text-gray-500">
                  <span className="text-sm">请选择左侧章节查看内容</span>
                </div>
              )}
            </ScrollArea>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-32 text-gray-500">
          <FileText className="h-8 w-8 mb-2" />
          <p className="text-sm">请上传Word文档</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setShowUpload(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            选择文件
          </Button>
        </div>
      )}

      {/* 底部操作栏 */}
      {document && selectedSection && (
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <span className="text-sm text-gray-600">
                已选择: {document.sections.find(s => s.id === selectedSection)?.title}
              </span>
              <div className="text-xs text-gray-500 mt-1">
                字数: {document.sections.find(s => s.id === selectedSection)?.wordCount || 0}
              </div>
            </div>
            <Button
              size="sm"
              variant="default"
              onClick={() => handleStartEditing(selectedSection)}
            >
              开始编辑
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default WordPreview;
