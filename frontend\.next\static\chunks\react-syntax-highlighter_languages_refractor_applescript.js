"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_applescript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/applescript.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/applescript.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = applescript\napplescript.displayName = 'applescript'\napplescript.aliases = []\nfunction applescript(Prism) {\n  Prism.languages.applescript = {\n    comment: [\n      // Allow one level of nesting\n      /\\(\\*(?:\\(\\*(?:[^*]|\\*(?!\\)))*\\*\\)|(?!\\(\\*)[\\s\\S])*?\\*\\)/,\n      /--.+/,\n      /#.+/\n    ],\n    string: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e-?\\d+)?\\b/i,\n    operator: [\n      /[&=≠≤≥*+\\-\\/÷^]|[<>]=?/,\n      /\\b(?:(?:begin|end|start)s? with|(?:contains?|(?:does not|doesn't) contain)|(?:is|isn't|is not) (?:contained by|in)|(?:(?:is|isn't|is not) )?(?:greater|less) than(?: or equal)?(?: to)?|(?:comes|(?:does not|doesn't) come) (?:after|before)|(?:is|isn't|is not) equal(?: to)?|(?:(?:does not|doesn't) equal|equal to|equals|is not|isn't)|(?:a )?(?:ref(?: to)?|reference to)|(?:and|as|div|mod|not|or))\\b/\n    ],\n    keyword:\n      /\\b(?:about|above|after|against|apart from|around|aside from|at|back|before|beginning|behind|below|beneath|beside|between|but|by|considering|continue|copy|does|eighth|else|end|equal|error|every|exit|false|fifth|first|for|fourth|from|front|get|given|global|if|ignoring|in|instead of|into|is|it|its|last|local|me|middle|my|ninth|of|on|onto|out of|over|prop|property|put|repeat|return|returning|second|set|seventh|since|sixth|some|tell|tenth|that|the|then|third|through|thru|timeout|times|to|transaction|true|try|until|where|while|whose|with|without)\\b/,\n    'class-name':\n      /\\b(?:POSIX file|RGB color|alias|application|boolean|centimeters|centimetres|class|constant|cubic centimeters|cubic centimetres|cubic feet|cubic inches|cubic meters|cubic metres|cubic yards|date|degrees Celsius|degrees Fahrenheit|degrees Kelvin|feet|file|gallons|grams|inches|integer|kilograms|kilometers|kilometres|list|liters|litres|meters|metres|miles|number|ounces|pounds|quarts|real|record|reference|script|square feet|square kilometers|square kilometres|square meters|square metres|square miles|square yards|text|yards)\\b/,\n    punctuation: /[{}():,¬«»《》]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/applescript.js\n"));

/***/ })

}]);