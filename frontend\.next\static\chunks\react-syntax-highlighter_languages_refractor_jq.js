"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jq"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jq.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jq.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = jq\njq.displayName = 'jq'\njq.aliases = []\nfunction jq(Prism) {\n  ;(function (Prism) {\n    var interpolation = /\\\\\\((?:[^()]|\\([^()]*\\))*\\)/.source\n    var string = RegExp(\n      /(^|[^\\\\])\"(?:[^\"\\r\\n\\\\]|\\\\[^\\r\\n(]|__)*\"/.source.replace(\n        /__/g,\n        function () {\n          return interpolation\n        }\n      )\n    )\n    var stringInterpolation = {\n      interpolation: {\n        pattern: RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + interpolation),\n        lookbehind: true,\n        inside: {\n          content: {\n            pattern: /^(\\\\\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          punctuation: /^\\\\\\(|\\)$/\n        }\n      }\n    }\n    var jq = (Prism.languages.jq = {\n      comment: /#.*/,\n      property: {\n        pattern: RegExp(string.source + /(?=\\s*:(?!:))/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      string: {\n        pattern: string,\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      function: {\n        pattern: /(\\bdef\\s+)[a-z_]\\w+/i,\n        lookbehind: true\n      },\n      variable: /\\B\\$\\w+/,\n      'property-literal': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        alias: 'property'\n      },\n      keyword:\n        /\\b(?:as|break|catch|def|elif|else|end|foreach|if|import|include|label|module|modulemeta|null|reduce|then|try|while)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      number: /(?:\\b\\d+\\.|\\B\\.)?\\b\\d+(?:[eE][+-]?\\d+)?\\b/,\n      operator: [\n        {\n          pattern: /\\|=?/,\n          alias: 'pipe'\n        },\n        /\\.\\.|[!=<>]?=|\\?\\/\\/|\\/\\/=?|[-+*/%]=?|[<>?]|\\b(?:and|not|or)\\b/\n      ],\n      'c-style-function': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n        alias: 'function'\n      },\n      punctuation: /::|[()\\[\\]{},:;]|\\.(?=\\s*[\\[\\w$])/,\n      dot: {\n        pattern: /\\./,\n        alias: 'important'\n      }\n    })\n    stringInterpolation.interpolation.inside.content.inside = jq\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jq.js\n"));

/***/ })

}]);