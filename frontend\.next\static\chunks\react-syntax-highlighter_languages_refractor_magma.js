"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_magma"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/magma.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/magma.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = magma\nmagma.displayName = 'magma'\nmagma.aliases = []\nfunction magma(Prism) {\n  Prism.languages.magma = {\n    output: {\n      pattern:\n        /^(>.*(?:\\r(?:\\n|(?!\\n))|\\n))(?!>)(?:.+|(?:\\r(?:\\n|(?!\\n))|\\n)(?!>).*)(?:(?:\\r(?:\\n|(?!\\n))|\\n)(?!>).*)*/m,\n      lookbehind: true,\n      greedy: true\n    },\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    // http://magma.maths.usyd.edu.au/magma/handbook/text/82\n    keyword:\n      /\\b(?:_|adj|and|assert|assert2|assert3|assigned|break|by|case|cat|catch|clear|cmpeq|cmpne|continue|declare|default|delete|diff|div|do|elif|else|end|eq|error|eval|exists|exit|for|forall|forward|fprintf|freeze|function|ge|gt|if|iload|import|in|intrinsic|is|join|le|load|local|lt|meet|mod|ne|not|notadj|notin|notsubset|or|print|printf|procedure|quit|random|read|readi|repeat|require|requirege|requirerange|restore|return|save|sdiff|select|subset|then|time|to|try|until|vprint|vprintf|vtime|when|where|while|xor)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    generator: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*<)/i,\n      alias: 'class-name'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern:\n        /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    operator: /->|[-+*/^~!|#=]|:=|\\.\\./,\n    punctuation: /[()[\\]{}<>,;.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/magma.js\n"));

/***/ })

}]);