"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_markup"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = markup\nmarkup.displayName = 'markup'\nmarkup.aliases = ['html', 'mathml', 'svg', 'xml', 'ssml', 'atom', 'rss']\nfunction markup(Prism) {\n  Prism.languages.markup = {\n    comment: {\n      pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n      greedy: true\n    },\n    prolog: {\n      pattern: /<\\?[\\s\\S]+?\\?>/,\n      greedy: true\n    },\n    doctype: {\n      // https://www.w3.org/TR/xml/#NT-doctypedecl\n      pattern:\n        /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n      greedy: true,\n      inside: {\n        'internal-subset': {\n          pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        string: {\n          pattern: /\"[^\"]*\"|'[^']*'/,\n          greedy: true\n        },\n        punctuation: /^<!|>$|[[\\]]/,\n        'doctype-tag': /^DOCTYPE/i,\n        name: /[^\\s<>'\"]+/\n      }\n    },\n    cdata: {\n      pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n      greedy: true\n    },\n    tag: {\n      pattern:\n        /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n      greedy: true,\n      inside: {\n        tag: {\n          pattern: /^<\\/?[^\\s>\\/]+/,\n          inside: {\n            punctuation: /^<\\/?/,\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        },\n        'special-attr': [],\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n          inside: {\n            punctuation: [\n              {\n                pattern: /^=/,\n                alias: 'attr-equals'\n              },\n              /\"|'/\n            ]\n          }\n        },\n        punctuation: /\\/?>/,\n        'attr-name': {\n          pattern: /[^\\s>\\/]+/,\n          inside: {\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        }\n      }\n    },\n    entity: [\n      {\n        pattern: /&[\\da-z]{1,8};/i,\n        alias: 'named-entity'\n      },\n      /&#x?[\\da-f]{1,8};/i\n    ]\n  }\n  Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n    Prism.languages.markup['entity']\n  Prism.languages.markup['doctype'].inside['internal-subset'].inside =\n    Prism.languages.markup // Plugin to make entity title show the real entity, idea by Roman Komarov\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type === 'entity') {\n      env.attributes['title'] = env.content.value.replace(/&amp;/, '&')\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    /**\n     * Adds an inlined language to markup.\n     *\n     * An example of an inlined language is CSS with `<style>` tags.\n     *\n     * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addInlined('style', 'css');\n     */\n    value: function addInlined(tagName, lang) {\n      var includedCdataInside = {}\n      includedCdataInside['language-' + lang] = {\n        pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n        lookbehind: true,\n        inside: Prism.languages[lang]\n      }\n      includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i\n      var inside = {\n        'included-cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          inside: includedCdataInside\n        }\n      }\n      inside['language-' + lang] = {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages[lang]\n      }\n      var def = {}\n      def[tagName] = {\n        pattern: RegExp(\n          /(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(\n            /__/g,\n            function () {\n              return tagName\n            }\n          ),\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: inside\n      }\n      Prism.languages.insertBefore('markup', 'cdata', def)\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    /**\n     * Adds an pattern to highlight languages embedded in HTML attributes.\n     *\n     * An example of an inlined language is CSS with `style` attributes.\n     *\n     * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addAttribute('style', 'css');\n     */\n    value: function (attrName, lang) {\n      Prism.languages.markup.tag.inside['special-attr'].push({\n        pattern: RegExp(\n          /(^|[\"'\\s])/.source +\n            '(?:' +\n            attrName +\n            ')' +\n            /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n          'i'\n        ),\n        lookbehind: true,\n        inside: {\n          'attr-name': /^[^\\s=]+/,\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              value: {\n                pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                lookbehind: true,\n                alias: [lang, 'language-' + lang],\n                inside: Prism.languages[lang]\n              },\n              punctuation: [\n                {\n                  pattern: /^=/,\n                  alias: 'attr-equals'\n                },\n                /\"|'/\n              ]\n            }\n          }\n        }\n      })\n    }\n  })\n  Prism.languages.html = Prism.languages.markup\n  Prism.languages.mathml = Prism.languages.markup\n  Prism.languages.svg = Prism.languages.markup\n  Prism.languages.xml = Prism.languages.extend('markup', {})\n  Prism.languages.ssml = Prism.languages.xml\n  Prism.languages.atom = Prism.languages.xml\n  Prism.languages.rss = Prism.languages.xml\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markup.js\n"));

/***/ })

}]);