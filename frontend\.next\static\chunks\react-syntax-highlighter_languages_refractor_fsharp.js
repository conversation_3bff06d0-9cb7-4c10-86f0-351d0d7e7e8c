"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_fsharp"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fsharp.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fsharp.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = fsharp\nfsharp.displayName = 'fsharp'\nfsharp.aliases = []\nfunction fsharp(Prism) {\n  Prism.languages.fsharp = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\(\\*(?!\\))[\\s\\S]*?\\*\\)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(?:\"\"\"[\\s\\S]*?\"\"\"|@\"(?:\"\"|[^\"])*\"|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")B?/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:exception|inherit|interface|new|of|type)\\s+|\\w\\s*:\\s*|\\s:\\??>\\s*)[.\\w]+\\b(?:\\s*(?:->|\\*)\\s*[.\\w]+\\b)*(?!\\s*[:.])/,\n      lookbehind: true,\n      inside: {\n        operator: /->|\\*/,\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:let|return|use|yield)(?:!\\B|\\b)|\\b(?:abstract|and|as|asr|assert|atomic|base|begin|break|checked|class|component|const|constraint|constructor|continue|default|delegate|do|done|downcast|downto|eager|elif|else|end|event|exception|extern|external|false|finally|fixed|for|fun|function|functor|global|if|in|include|inherit|inline|interface|internal|land|lazy|lor|lsl|lsr|lxor|match|member|method|mixin|mod|module|mutable|namespace|new|not|null|object|of|open|or|override|parallel|private|process|protected|public|pure|rec|sealed|select|sig|static|struct|tailcall|then|to|trait|true|try|type|upcast|val|virtual|void|volatile|when|while|with)\\b/,\n    number: [\n      /\\b0x[\\da-fA-F]+(?:LF|lf|un)?\\b/,\n      /\\b0b[01]+(?:uy|y)?\\b/,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[fm]|e[+-]?\\d+)?\\b/i,\n      /\\b\\d+(?:[IlLsy]|UL|u[lsy]?)?\\b/\n    ],\n    operator:\n      /([<>~&^])\\1\\1|([*.:<>&])\\2|<-|->|[!=:]=|<?\\|{1,3}>?|\\??(?:<=|>=|<>|[-+*/%=<>])\\??|[!?^&]|~[+~-]|:>|:\\?>?/\n  })\n  Prism.languages.insertBefore('fsharp', 'keyword', {\n    preprocessor: {\n      pattern: /(^[\\t ]*)#.*/m,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        directive: {\n          pattern: /(^#)\\b(?:else|endif|if|light|line|nowarn)\\b/,\n          lookbehind: true,\n          alias: 'keyword'\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'punctuation', {\n    'computation-expression': {\n      pattern: /\\b[_a-z]\\w*(?=\\s*\\{)/i,\n      alias: 'keyword'\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'string', {\n    annotation: {\n      pattern: /\\[<.+?>\\]/,\n      greedy: true,\n      inside: {\n        punctuation: /^\\[<|>\\]$/,\n        'class-name': {\n          pattern: /^\\w+$|(^|;\\s*)[A-Z]\\w*(?=\\()/,\n          lookbehind: true\n        },\n        'annotation-content': {\n          pattern: /[\\s\\S]+/,\n          inside: Prism.languages.fsharp\n        }\n      }\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8}))'B?/,\n      greedy: true\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fsharp.js\n"));

/***/ })

}]);