"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_keyman"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/keyman.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/keyman.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = keyman\nkeyman.displayName = 'keyman'\nkeyman.aliases = []\nfunction keyman(Prism) {\n  Prism.languages.keyman = {\n    comment: {\n      pattern: /\\bc .*/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    'virtual-key': {\n      pattern:\n        /\\[\\s*(?:(?:ALT|CAPS|CTRL|LALT|LCTRL|NCAPS|RALT|RCTRL|SHIFT)\\s+)*(?:[TKU]_[\\w?]+|[A-E]\\d\\d?|\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*\\]/i,\n      greedy: true,\n      alias: 'function' // alias for styles\n    },\n    // https://help.keyman.com/developer/language/guide/headers\n    'header-keyword': {\n      pattern: /&\\w+/,\n      alias: 'bold' // alias for styles\n    },\n    'header-statement': {\n      pattern:\n        /\\b(?:bitmap|bitmaps|caps always off|caps on only|copyright|hotkey|language|layout|message|name|shift frees caps|version)\\b/i,\n      alias: 'bold' // alias for styles\n    },\n    'rule-keyword': {\n      pattern:\n        /\\b(?:any|baselayout|beep|call|context|deadkey|dk|if|index|layer|notany|nul|outs|platform|reset|return|save|set|store|use)\\b/i,\n      alias: 'keyword'\n    },\n    'structural-keyword': {\n      pattern: /\\b(?:ansi|begin|group|match|nomatch|unicode|using keys)\\b/i,\n      alias: 'keyword'\n    },\n    'compile-target': {\n      pattern: /\\$(?:keyman|keymanonly|keymanweb|kmfl|weaver):/i,\n      alias: 'property'\n    },\n    // U+####, x###, d### characters and numbers\n    number: /\\b(?:U\\+[\\dA-F]+|d\\d+|x[\\da-f]+|\\d+)\\b/i,\n    operator: /[+>\\\\$]|\\.\\./,\n    punctuation: /[()=,]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2tleW1hbi5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGtleW1hbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBrZXltYW5cbmtleW1hbi5kaXNwbGF5TmFtZSA9ICdrZXltYW4nXG5rZXltYW4uYWxpYXNlcyA9IFtdXG5mdW5jdGlvbiBrZXltYW4oUHJpc20pIHtcbiAgUHJpc20ubGFuZ3VhZ2VzLmtleW1hbiA9IHtcbiAgICBjb21tZW50OiB7XG4gICAgICBwYXR0ZXJuOiAvXFxiYyAuKi9pLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBzdHJpbmc6IHtcbiAgICAgIHBhdHRlcm46IC9cIlteXCJcXHJcXG5dKlwifCdbXidcXHJcXG5dKicvLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICAndmlydHVhbC1rZXknOiB7XG4gICAgICBwYXR0ZXJuOlxuICAgICAgICAvXFxbXFxzKig/Oig/OkFMVHxDQVBTfENUUkx8TEFMVHxMQ1RSTHxOQ0FQU3xSQUxUfFJDVFJMfFNISUZUKVxccyspKig/OltUS1VdX1tcXHc/XSt8W0EtRV1cXGRcXGQ/fFwiW15cIlxcclxcbl0qXCJ8J1teJ1xcclxcbl0qJylcXHMqXFxdL2ksXG4gICAgICBncmVlZHk6IHRydWUsXG4gICAgICBhbGlhczogJ2Z1bmN0aW9uJyAvLyBhbGlhcyBmb3Igc3R5bGVzXG4gICAgfSxcbiAgICAvLyBodHRwczovL2hlbHAua2V5bWFuLmNvbS9kZXZlbG9wZXIvbGFuZ3VhZ2UvZ3VpZGUvaGVhZGVyc1xuICAgICdoZWFkZXIta2V5d29yZCc6IHtcbiAgICAgIHBhdHRlcm46IC8mXFx3Ky8sXG4gICAgICBhbGlhczogJ2JvbGQnIC8vIGFsaWFzIGZvciBzdHlsZXNcbiAgICB9LFxuICAgICdoZWFkZXItc3RhdGVtZW50Jzoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgL1xcYig/OmJpdG1hcHxiaXRtYXBzfGNhcHMgYWx3YXlzIG9mZnxjYXBzIG9uIG9ubHl8Y29weXJpZ2h0fGhvdGtleXxsYW5ndWFnZXxsYXlvdXR8bWVzc2FnZXxuYW1lfHNoaWZ0IGZyZWVzIGNhcHN8dmVyc2lvbilcXGIvaSxcbiAgICAgIGFsaWFzOiAnYm9sZCcgLy8gYWxpYXMgZm9yIHN0eWxlc1xuICAgIH0sXG4gICAgJ3J1bGUta2V5d29yZCc6IHtcbiAgICAgIHBhdHRlcm46XG4gICAgICAgIC9cXGIoPzphbnl8YmFzZWxheW91dHxiZWVwfGNhbGx8Y29udGV4dHxkZWFka2V5fGRrfGlmfGluZGV4fGxheWVyfG5vdGFueXxudWx8b3V0c3xwbGF0Zm9ybXxyZXNldHxyZXR1cm58c2F2ZXxzZXR8c3RvcmV8dXNlKVxcYi9pLFxuICAgICAgYWxpYXM6ICdrZXl3b3JkJ1xuICAgIH0sXG4gICAgJ3N0cnVjdHVyYWwta2V5d29yZCc6IHtcbiAgICAgIHBhdHRlcm46IC9cXGIoPzphbnNpfGJlZ2lufGdyb3VwfG1hdGNofG5vbWF0Y2h8dW5pY29kZXx1c2luZyBrZXlzKVxcYi9pLFxuICAgICAgYWxpYXM6ICdrZXl3b3JkJ1xuICAgIH0sXG4gICAgJ2NvbXBpbGUtdGFyZ2V0Jzoge1xuICAgICAgcGF0dGVybjogL1xcJCg/OmtleW1hbnxrZXltYW5vbmx5fGtleW1hbndlYnxrbWZsfHdlYXZlcik6L2ksXG4gICAgICBhbGlhczogJ3Byb3BlcnR5J1xuICAgIH0sXG4gICAgLy8gVSsjIyMjLCB4IyMjLCBkIyMjIGNoYXJhY3RlcnMgYW5kIG51bWJlcnNcbiAgICBudW1iZXI6IC9cXGIoPzpVXFwrW1xcZEEtRl0rfGRcXGQrfHhbXFxkYS1mXSt8XFxkKylcXGIvaSxcbiAgICBvcGVyYXRvcjogL1srPlxcXFwkXXxcXC5cXC4vLFxuICAgIHB1bmN0dWF0aW9uOiAvWygpPSxdL1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/keyman.js\n"));

/***/ })

}]);