"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_kusto"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kusto.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kusto.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = kusto\nkusto.displayName = 'kusto'\nkusto.aliases = []\nfunction kusto(Prism) {\n  Prism.languages.kusto = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /```[\\s\\S]*?```|[hH]?(?:\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\.)*'|@(?:\"[^\\r\\n\"]*\"|'[^\\r\\n']*'))/,\n      greedy: true\n    },\n    verb: {\n      pattern: /(\\|\\s*)[a-z][\\w-]*/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    command: {\n      pattern: /\\.[a-z][a-z\\d-]*\\b/,\n      alias: 'keyword'\n    },\n    'class-name':\n      /\\b(?:bool|datetime|decimal|dynamic|guid|int|long|real|string|timespan)\\b/,\n    keyword:\n      /\\b(?:access|alias|and|anti|as|asc|auto|between|by|(?:contains|(?:ends|starts)with|has(?:perfix|suffix)?)(?:_cs)?|database|declare|desc|external|from|fullouter|has_all|in|ingestion|inline|inner|innerunique|into|(?:left|right)(?:anti(?:semi)?|inner|outer|semi)?|let|like|local|not|of|on|or|pattern|print|query_parameters|range|restrict|schema|set|step|table|tables|to|view|where|with|matches\\s+regex|nulls\\s+(?:first|last))(?![\\w-])/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/,\n    datetime: [\n      {\n        // RFC 822 + RFC 850\n        pattern:\n          /\\b(?:(?:Fri|Friday|Mon|Monday|Sat|Saturday|Sun|Sunday|Thu|Thursday|Tue|Tuesday|Wed|Wednesday)\\s*,\\s*)?\\d{1,2}(?:\\s+|-)(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)(?:\\s+|-)\\d{2}\\s+\\d{2}:\\d{2}(?::\\d{2})?(?:\\s*(?:\\b(?:[A-Z]|(?:[ECMT][DS]|GM|U)T)|[+-]\\d{4}))?\\b/,\n        alias: 'number'\n      },\n      {\n        // ISO 8601\n        pattern:\n          /[+-]?\\b(?:\\d{4}-\\d{2}-\\d{2}(?:[ T]\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)?|\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)Z?/,\n        alias: 'number'\n      }\n    ],\n    number:\n      /\\b(?:0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?)(?:(?:min|sec|[mnµ]s|[dhms]|microsecond|tick)\\b)?|[+-]?\\binf\\b/,\n    operator: /=>|[!=]~|[!=<>]=?|[-+*/%|]|\\.\\./,\n    punctuation: /[()\\[\\]{},;.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kusto.js\n"));

/***/ })

}]);