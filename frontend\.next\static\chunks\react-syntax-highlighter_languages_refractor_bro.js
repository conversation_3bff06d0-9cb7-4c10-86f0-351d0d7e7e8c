"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_bro"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bro.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bro.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = bro\nbro.displayName = 'bro'\nbro.aliases = []\nfunction bro(Prism) {\n  Prism.languages.bro = {\n    comment: {\n      pattern: /(^|[^\\\\$])#.*/,\n      lookbehind: true,\n      inside: {\n        italic: /\\b(?:FIXME|TODO|XXX)\\b/\n      }\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    boolean: /\\b[TF]\\b/,\n    function: {\n      pattern: /(\\b(?:event|function|hook)[ \\t]+)\\w+(?:::\\w+)?/,\n      lookbehind: true\n    },\n    builtin:\n      /(?:@(?:load(?:-(?:plugin|sigs))?|unload|prefixes|ifn?def|else|(?:end)?if|DIR|FILENAME))|(?:&?(?:add_func|create_expire|default|delete_func|encrypt|error_handler|expire_func|group|log|mergeable|optional|persistent|priority|raw_output|read_expire|redef|rotate_interval|rotate_size|synchronized|type_column|write_expire))/,\n    constant: {\n      pattern: /(\\bconst[ \\t]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:add|addr|alarm|any|bool|break|const|continue|count|delete|double|else|enum|event|export|file|for|function|global|hook|if|in|int|interval|local|module|next|of|opaque|pattern|port|print|record|return|schedule|set|string|subnet|table|time|timeout|using|vector|when)\\b/,\n    operator: /--?|\\+\\+?|!=?=?|<=?|>=?|==?=?|&&|\\|\\|?|\\?|\\*|\\/|~|\\^|%/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/bro.js\n"));

/***/ })

}]);