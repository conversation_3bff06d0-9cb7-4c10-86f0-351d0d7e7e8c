"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-stick-to-bottom@1.1.0_react@19.1.0";
exports.ids = ["vendor-chunks/use-stick-to-bottom@1.1.0_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/StickToBottom.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/StickToBottom.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StickToBottom: () => (/* binding */ StickToBottom),\n/* harmony export */   useStickToBottomContext: () => (/* binding */ useStickToBottomContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useStickToBottom_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useStickToBottom.js */ \"(ssr)/./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/useStickToBottom.js\");\n\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (c) StackBlitz. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n\n\nconst StickToBottomContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst useIsomorphicLayoutEffect = typeof window !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nfunction StickToBottom({ instance, children, resize, initial, mass, damping, stiffness, targetScrollTop: currentTargetScrollTop, contextRef, ...props }) {\n    const customTargetScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const targetScrollTop = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((target, elements) => {\n        const get = context?.targetScrollTop ?? currentTargetScrollTop;\n        return get?.(target, elements) ?? target;\n    }, [currentTargetScrollTop]);\n    const defaultInstance = (0,_useStickToBottom_js__WEBPACK_IMPORTED_MODULE_2__.useStickToBottom)({\n        mass,\n        damping,\n        stiffness,\n        resize,\n        initial,\n        targetScrollTop,\n    });\n    const { scrollRef, contentRef, scrollToBottom, stopScroll, isAtBottom, escapedFromLock, state, } = instance ?? defaultInstance;\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => ({\n        scrollToBottom,\n        stopScroll,\n        scrollRef,\n        isAtBottom,\n        escapedFromLock,\n        contentRef,\n        state,\n        get targetScrollTop() {\n            return customTargetScrollTop.current;\n        },\n        set targetScrollTop(targetScrollTop) {\n            customTargetScrollTop.current = targetScrollTop;\n        },\n    }), [\n        scrollToBottom,\n        isAtBottom,\n        contentRef,\n        scrollRef,\n        stopScroll,\n        escapedFromLock,\n        state,\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(contextRef, () => context, [context]);\n    useIsomorphicLayoutEffect(() => {\n        if (!scrollRef.current) {\n            return;\n        }\n        if (getComputedStyle(scrollRef.current).overflow === \"visible\") {\n            scrollRef.current.style.overflow = \"auto\";\n        }\n    }, []);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StickToBottomContext.Provider, { value: context, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ...props, children: typeof children === \"function\" ? children(context) : children }) }));\n}\n(function (StickToBottom) {\n    function Content({ children, ...props }) {\n        const context = useStickToBottomContext();\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ref: context.scrollRef, style: {\n                height: \"100%\",\n                width: \"100%\",\n            }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ...props, ref: context.contentRef, children: typeof children === \"function\" ? children(context) : children }) }));\n    }\n    StickToBottom.Content = Content;\n})(StickToBottom || (StickToBottom = {}));\n/**\n * Use this hook inside a <StickToBottom> component to gain access to whether the component is at the bottom of the scrollable area.\n */\nfunction useStickToBottomContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StickToBottomContext);\n    if (!context) {\n        throw new Error(\"use-stick-to-bottom component context must be used within a StickToBottom component\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/StickToBottom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/useStickToBottom.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/useStickToBottom.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStickToBottom: () => (/* binding */ useStickToBottom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (c) StackBlitz. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nconst DEFAULT_SPRING_ANIMATION = {\n    /**\n     * A value from 0 to 1, on how much to damp the animation.\n     * 0 means no damping, 1 means full damping.\n     *\n     * @default 0.7\n     */\n    damping: 0.7,\n    /**\n     * The stiffness of how fast/slow the animation gets up to speed.\n     *\n     * @default 0.05\n     */\n    stiffness: 0.05,\n    /**\n     * The inertial mass associated with the animation.\n     * Higher numbers make the animation slower.\n     *\n     * @default 1.25\n     */\n    mass: 1.25,\n};\nconst STICK_TO_BOTTOM_OFFSET_PX = 70;\nconst SIXTY_FPS_INTERVAL_MS = 1000 / 60;\nconst RETAIN_ANIMATION_DURATION_MS = 350;\nlet mouseDown = false;\nglobalThis.document?.addEventListener(\"mousedown\", () => {\n    mouseDown = true;\n});\nglobalThis.document?.addEventListener(\"mouseup\", () => {\n    mouseDown = false;\n});\nglobalThis.document?.addEventListener(\"click\", () => {\n    mouseDown = false;\n});\nconst useStickToBottom = (options = {}) => {\n    const [escapedFromLock, updateEscapedFromLock] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAtBottom, updateIsAtBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(options.initial !== false);\n    const [isNearBottom, setIsNearBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    optionsRef.current = options;\n    const isSelecting = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        if (!mouseDown) {\n            return false;\n        }\n        const selection = window.getSelection();\n        if (!selection || !selection.rangeCount) {\n            return false;\n        }\n        const range = selection.getRangeAt(0);\n        return (range.commonAncestorContainer.contains(scrollRef.current) ||\n            scrollRef.current?.contains(range.commonAncestorContainer));\n    }, []);\n    const setIsAtBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((isAtBottom) => {\n        state.isAtBottom = isAtBottom;\n        updateIsAtBottom(isAtBottom);\n    }, []);\n    const setEscapedFromLock = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((escapedFromLock) => {\n        state.escapedFromLock = escapedFromLock;\n        updateEscapedFromLock(escapedFromLock);\n    }, []);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: not needed\n    const state = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        let lastCalculation;\n        return {\n            escapedFromLock,\n            isAtBottom,\n            resizeDifference: 0,\n            accumulated: 0,\n            velocity: 0,\n            listeners: new Set(),\n            get scrollTop() {\n                return scrollRef.current?.scrollTop ?? 0;\n            },\n            set scrollTop(scrollTop) {\n                if (scrollRef.current) {\n                    scrollRef.current.scrollTop = scrollTop;\n                    state.ignoreScrollToTop = scrollRef.current.scrollTop;\n                }\n            },\n            get targetScrollTop() {\n                if (!scrollRef.current || !contentRef.current) {\n                    return 0;\n                }\n                return (scrollRef.current.scrollHeight - 1 - scrollRef.current.clientHeight);\n            },\n            get calculatedTargetScrollTop() {\n                if (!scrollRef.current || !contentRef.current) {\n                    return 0;\n                }\n                const { targetScrollTop } = this;\n                if (!options.targetScrollTop) {\n                    return targetScrollTop;\n                }\n                if (lastCalculation?.targetScrollTop === targetScrollTop) {\n                    return lastCalculation.calculatedScrollTop;\n                }\n                const calculatedScrollTop = Math.max(Math.min(options.targetScrollTop(targetScrollTop, {\n                    scrollElement: scrollRef.current,\n                    contentElement: contentRef.current,\n                }), targetScrollTop), 0);\n                lastCalculation = { targetScrollTop, calculatedScrollTop };\n                requestAnimationFrame(() => {\n                    lastCalculation = undefined;\n                });\n                return calculatedScrollTop;\n            },\n            get scrollDifference() {\n                return this.calculatedTargetScrollTop - this.scrollTop;\n            },\n            get isNearBottom() {\n                return this.scrollDifference <= STICK_TO_BOTTOM_OFFSET_PX;\n            },\n        };\n    }, []);\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((scrollOptions = {}) => {\n        if (typeof scrollOptions === \"string\") {\n            scrollOptions = { animation: scrollOptions };\n        }\n        if (!scrollOptions.preserveScrollPosition) {\n            setIsAtBottom(true);\n        }\n        const waitElapsed = Date.now() + (Number(scrollOptions.wait) || 0);\n        const behavior = mergeAnimations(optionsRef.current, scrollOptions.animation);\n        const { ignoreEscapes = false } = scrollOptions;\n        let durationElapsed;\n        let startTarget = state.calculatedTargetScrollTop;\n        if (scrollOptions.duration instanceof Promise) {\n            scrollOptions.duration.finally(() => {\n                durationElapsed = Date.now();\n            });\n        }\n        else {\n            durationElapsed = waitElapsed + (scrollOptions.duration ?? 0);\n        }\n        const next = async () => {\n            const promise = new Promise(requestAnimationFrame).then(() => {\n                if (!state.isAtBottom) {\n                    state.animation = undefined;\n                    return false;\n                }\n                const { scrollTop } = state;\n                const tick = performance.now();\n                const tickDelta = (tick - (state.lastTick ?? tick)) / SIXTY_FPS_INTERVAL_MS;\n                state.animation || (state.animation = { behavior, promise, ignoreEscapes });\n                if (state.animation.behavior === behavior) {\n                    state.lastTick = tick;\n                }\n                if (isSelecting()) {\n                    return next();\n                }\n                if (waitElapsed > Date.now()) {\n                    return next();\n                }\n                if (scrollTop < Math.min(startTarget, state.calculatedTargetScrollTop)) {\n                    if (state.animation?.behavior === behavior) {\n                        if (behavior === \"instant\") {\n                            state.scrollTop = state.calculatedTargetScrollTop;\n                            return next();\n                        }\n                        state.velocity =\n                            (behavior.damping * state.velocity +\n                                behavior.stiffness * state.scrollDifference) /\n                                behavior.mass;\n                        state.accumulated += state.velocity * tickDelta;\n                        state.scrollTop += state.accumulated;\n                        if (state.scrollTop !== scrollTop) {\n                            state.accumulated = 0;\n                        }\n                    }\n                    return next();\n                }\n                if (durationElapsed > Date.now()) {\n                    startTarget = state.calculatedTargetScrollTop;\n                    return next();\n                }\n                state.animation = undefined;\n                /**\n                 * If we're still below the target, then queue\n                 * up another scroll to the bottom with the last\n                 * requested animatino.\n                 */\n                if (state.scrollTop < state.calculatedTargetScrollTop) {\n                    return scrollToBottom({\n                        animation: mergeAnimations(optionsRef.current, optionsRef.current.resize),\n                        ignoreEscapes,\n                        duration: Math.max(0, durationElapsed - Date.now()) || undefined,\n                    });\n                }\n                return state.isAtBottom;\n            });\n            return promise.then((isAtBottom) => {\n                requestAnimationFrame(() => {\n                    if (!state.animation) {\n                        state.lastTick = undefined;\n                        state.velocity = 0;\n                    }\n                });\n                return isAtBottom;\n            });\n        };\n        if (scrollOptions.wait !== true) {\n            state.animation = undefined;\n        }\n        if (state.animation?.behavior === behavior) {\n            return state.animation.promise;\n        }\n        return next();\n    }, [setIsAtBottom, isSelecting, state]);\n    const stopScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        setEscapedFromLock(true);\n        setIsAtBottom(false);\n    }, [setEscapedFromLock, setIsAtBottom]);\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(({ target }) => {\n        if (target !== scrollRef.current) {\n            return;\n        }\n        const { scrollTop, ignoreScrollToTop } = state;\n        let { lastScrollTop = scrollTop } = state;\n        state.lastScrollTop = scrollTop;\n        state.ignoreScrollToTop = undefined;\n        if (ignoreScrollToTop && ignoreScrollToTop > scrollTop) {\n            /**\n             * When the user scrolls up while the animation plays, the `scrollTop` may\n             * not come in separate events; if this happens, to make sure `isScrollingUp`\n             * is correct, set the lastScrollTop to the ignored event.\n             */\n            lastScrollTop = ignoreScrollToTop;\n        }\n        setIsNearBottom(state.isNearBottom);\n        /**\n         * Scroll events may come before a ResizeObserver event,\n         * so in order to ignore resize events correctly we use a\n         * timeout.\n         *\n         * @see https://github.com/WICG/resize-observer/issues/25#issuecomment-248757228\n         */\n        setTimeout(() => {\n            /**\n             * When theres a resize difference ignore the resize event.\n             */\n            if (state.resizeDifference || scrollTop === ignoreScrollToTop) {\n                return;\n            }\n            if (isSelecting()) {\n                setEscapedFromLock(true);\n                setIsAtBottom(false);\n                return;\n            }\n            const isScrollingDown = scrollTop > lastScrollTop;\n            const isScrollingUp = scrollTop < lastScrollTop;\n            if (state.animation?.ignoreEscapes) {\n                state.scrollTop = lastScrollTop;\n                return;\n            }\n            if (isScrollingUp) {\n                setEscapedFromLock(true);\n                setIsAtBottom(false);\n            }\n            if (isScrollingDown) {\n                setEscapedFromLock(false);\n            }\n            if (!state.escapedFromLock && state.isNearBottom) {\n                setIsAtBottom(true);\n            }\n        }, 1);\n    }, [setEscapedFromLock, setIsAtBottom, isSelecting, state]);\n    const handleWheel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(({ target, deltaY }) => {\n        let element = target;\n        while (![\"scroll\", \"auto\"].includes(getComputedStyle(element).overflow)) {\n            if (!element.parentElement) {\n                return;\n            }\n            element = element.parentElement;\n        }\n        /**\n         * The browser may cancel the scrolling from the mouse wheel\n         * if we update it from the animation in meantime.\n         * To prevent this, always escape when the wheel is scrolled up.\n         */\n        if (element === scrollRef.current &&\n            deltaY < 0 &&\n            scrollRef.current.scrollHeight > scrollRef.current.clientHeight &&\n            !state.animation?.ignoreEscapes) {\n            setEscapedFromLock(true);\n            setIsAtBottom(false);\n        }\n    }, [setEscapedFromLock, setIsAtBottom, state]);\n    const scrollRef = useRefCallback((scroll) => {\n        scrollRef.current?.removeEventListener(\"scroll\", handleScroll);\n        scrollRef.current?.removeEventListener(\"wheel\", handleWheel);\n        scroll?.addEventListener(\"scroll\", handleScroll, { passive: true });\n        scroll?.addEventListener(\"wheel\", handleWheel, { passive: true });\n    }, []);\n    const contentRef = useRefCallback((content) => {\n        state.resizeObserver?.disconnect();\n        if (!content) {\n            return;\n        }\n        let previousHeight;\n        state.resizeObserver = new ResizeObserver(([entry]) => {\n            const { height } = entry.contentRect;\n            const difference = height - (previousHeight ?? height);\n            state.resizeDifference = difference;\n            /**\n             * Sometimes the browser can overscroll past the target,\n             * so check for this and adjust appropriately.\n             */\n            if (state.scrollTop > state.targetScrollTop) {\n                state.scrollTop = state.targetScrollTop;\n            }\n            setIsNearBottom(state.isNearBottom);\n            if (difference >= 0) {\n                /**\n                 * If it's a positive resize, scroll to the bottom when\n                 * we're already at the bottom.\n                 */\n                const animation = mergeAnimations(optionsRef.current, previousHeight\n                    ? optionsRef.current.resize\n                    : optionsRef.current.initial);\n                scrollToBottom({\n                    animation,\n                    wait: true,\n                    preserveScrollPosition: true,\n                    duration: animation === \"instant\" ? undefined : RETAIN_ANIMATION_DURATION_MS,\n                });\n            }\n            else {\n                /**\n                 * Else if it's a negative resize, check if we're near the bottom\n                 * if we are want to un-escape from the lock, because the resize\n                 * could have caused the container to be at the bottom.\n                 */\n                if (state.isNearBottom) {\n                    setEscapedFromLock(false);\n                    setIsAtBottom(true);\n                }\n            }\n            previousHeight = height;\n            /**\n             * Reset the resize difference after the scroll event\n             * has fired. Requires a rAF to wait for the scroll event,\n             * and a setTimeout to wait for the other timeout we have in\n             * resizeObserver in case the scroll event happens after the\n             * resize event.\n             */\n            requestAnimationFrame(() => {\n                setTimeout(() => {\n                    if (state.resizeDifference === difference) {\n                        state.resizeDifference = 0;\n                    }\n                }, 1);\n            });\n        });\n        state.resizeObserver?.observe(content);\n    }, []);\n    return {\n        contentRef,\n        scrollRef,\n        scrollToBottom,\n        stopScroll,\n        isAtBottom: isAtBottom || isNearBottom,\n        isNearBottom,\n        escapedFromLock,\n        state,\n    };\n};\nfunction useRefCallback(callback, deps) {\n    // biome-ignore lint/correctness/useExhaustiveDependencies: not needed\n    const result = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n        result.current = ref;\n        return callback(ref);\n    }, deps);\n    return result;\n}\nconst animationCache = new Map();\nfunction mergeAnimations(...animations) {\n    const result = { ...DEFAULT_SPRING_ANIMATION };\n    let instant = false;\n    for (const animation of animations) {\n        if (animation === \"instant\") {\n            instant = true;\n            continue;\n        }\n        if (typeof animation !== \"object\") {\n            continue;\n        }\n        instant = false;\n        result.damping = animation.damping ?? result.damping;\n        result.stiffness = animation.stiffness ?? result.stiffness;\n        result.mass = animation.mass ?? result.mass;\n    }\n    const key = JSON.stringify(result);\n    if (!animationCache.has(key)) {\n        animationCache.set(key, Object.freeze(result));\n    }\n    return instant ? \"instant\" : animationCache.get(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/use-stick-to-bottom@1.1.0_react@19.1.0/node_modules/use-stick-to-bottom/dist/useStickToBottom.js\n");

/***/ })

};
;