"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_peoplecode"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/peoplecode.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/peoplecode.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = peoplecode\npeoplecode.displayName = 'peoplecode'\npeoplecode.aliases = ['pcode']\nfunction peoplecode(Prism) {\n  Prism.languages.peoplecode = {\n    comment: RegExp(\n      [\n        // C-style multiline comments\n        /\\/\\*[\\s\\S]*?\\*\\//.source, // REM comments\n        /\\bREM[^;]*;/.source, // Nested <* *> comments\n        /<\\*(?:[^<*]|\\*(?!>)|<(?!\\*)|<\\*(?:(?!\\*>)[\\s\\S])*\\*>)*\\*>/.source, // /+ +/ comments\n        /\\/\\+[\\s\\S]*?\\+\\//.source\n      ].join('|')\n    ),\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'(?!')|\"(?:\"\"|[^\"\\r\\n])*\"(?!\")/,\n      greedy: true\n    },\n    variable: /%\\w+/,\n    'function-definition': {\n      pattern: /((?:^|[^\\w-])(?:function|method)\\s+)\\w+/i,\n      lookbehind: true,\n      alias: 'function'\n    },\n    'class-name': {\n      pattern:\n        /((?:^|[^-\\w])(?:as|catch|class|component|create|extends|global|implements|instance|local|of|property|returns)\\s+)\\w+(?::\\w+)*/i,\n      lookbehind: true,\n      inside: {\n        punctuation: /:/\n      }\n    },\n    keyword:\n      /\\b(?:abstract|alias|as|catch|class|component|constant|create|declare|else|end-(?:class|evaluate|for|function|get|if|method|set|try|while)|evaluate|extends|for|function|get|global|if|implements|import|instance|library|local|method|null|of|out|peopleCode|private|program|property|protected|readonly|ref|repeat|returns?|set|step|then|throw|to|try|until|value|when(?:-other)?|while)\\b/i,\n    'operator-keyword': {\n      pattern: /\\b(?:and|not|or)\\b/i,\n      alias: 'operator'\n    },\n    function: /[_a-z]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /<>|[<>]=?|!=|\\*\\*|[-+*/|=@]/,\n    punctuation: /[:.;,()[\\]]/\n  }\n  Prism.languages.pcode = Prism.languages.peoplecode\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/peoplecode.js\n"));

/***/ })

}]);