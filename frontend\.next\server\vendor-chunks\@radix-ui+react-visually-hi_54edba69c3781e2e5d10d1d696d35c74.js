"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74";
exports.ids = ["vendor-chunks/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_7b933c94c4e5e4ade0d694d5b36d9593/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_54edba69c3781e2e5d10d1d696d35c74/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;