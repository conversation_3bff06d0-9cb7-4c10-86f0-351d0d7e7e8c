"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_q"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/q.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/q.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = q\nq.displayName = 'q'\nq.aliases = []\nfunction q(Prism) {\n  Prism.languages.q = {\n    string: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n    comment: [\n      // From http://code.kx.com/wiki/Reference/Slash:\n      // When / is following a space (or a right parenthesis, bracket, or brace), it is ignored with the rest of the line.\n      {\n        pattern: /([\\t )\\]}])\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }, // From http://code.kx.com/wiki/Reference/Slash:\n      // A line which has / as its first character and contains at least one other non-whitespace character is a whole-line comment and is ignored entirely.\n      // A / on a line by itself begins a multiline comment which is terminated by the next \\ on a line by itself.\n      // If a / is not matched by a \\, the multiline comment is unterminated and continues to end of file.\n      // The / and \\ must be the first char on the line, but may be followed by any amount of whitespace.\n      {\n        pattern:\n          /(^|\\r?\\n|\\r)\\/[\\t ]*(?:(?:\\r?\\n|\\r)(?:.*(?:\\r?\\n|\\r(?!\\n)))*?(?:\\\\(?=[\\t ]*(?:\\r?\\n|\\r))|$)|\\S.*)/,\n        lookbehind: true,\n        greedy: true\n      }, // From http://code.kx.com/wiki/Reference/Slash:\n      // A \\ on a line by itself with no preceding matching / will comment to end of file.\n      {\n        pattern: /^\\\\[\\t ]*(?:\\r?\\n|\\r)[\\s\\S]+/m,\n        greedy: true\n      },\n      {\n        pattern: /^#!.+/m,\n        greedy: true\n      }\n    ],\n    symbol: /`(?::\\S+|[\\w.]*)/,\n    datetime: {\n      pattern:\n        /0N[mdzuvt]|0W[dtz]|\\d{4}\\.\\d\\d(?:m|\\.\\d\\d(?:T(?:\\d\\d(?::\\d\\d(?::\\d\\d(?:[.:]\\d\\d\\d)?)?)?)?)?[dz]?)|\\d\\d:\\d\\d(?::\\d\\d(?:[.:]\\d\\d\\d)?)?[uvt]?/,\n      alias: 'number'\n    },\n    // The negative look-ahead prevents bad highlighting\n    // of verbs 0: and 1:\n    number:\n      /\\b(?![01]:)(?:0N[hje]?|0W[hj]?|0[wn]|0x[\\da-fA-F]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?[hjfeb]?)/,\n    keyword:\n      /\\\\\\w+\\b|\\b(?:abs|acos|aj0?|all|and|any|asc|asin|asof|atan|attr|avgs?|binr?|by|ceiling|cols|cor|cos|count|cov|cross|csv|cut|delete|deltas|desc|dev|differ|distinct|div|do|dsave|ej|enlist|eval|except|exec|exit|exp|fby|fills|first|fkeys|flip|floor|from|get|getenv|group|gtime|hclose|hcount|hdel|hopen|hsym|iasc|identity|idesc|if|ij|in|insert|inter|inv|keys?|last|like|list|ljf?|load|log|lower|lsq|ltime|ltrim|mavg|maxs?|mcount|md5|mdev|med|meta|mins?|mmax|mmin|mmu|mod|msum|neg|next|not|null|or|over|parse|peach|pj|plist|prds?|prev|prior|rand|rank|ratios|raze|read0|read1|reciprocal|reval|reverse|rload|rotate|rsave|rtrim|save|scan|scov|sdev|select|set|setenv|show|signum|sin|sqrt|ssr?|string|sublist|sums?|sv|svar|system|tables|tan|til|trim|txf|type|uj|ungroup|union|update|upper|upsert|value|var|views?|vs|wavg|where|while|within|wj1?|wsum|ww|xasc|xbar|xcols?|xdesc|xexp|xgroup|xkey|xlog|xprev|xrank)\\b/,\n    adverb: {\n      pattern: /['\\/\\\\]:?|\\beach\\b/,\n      alias: 'function'\n    },\n    verb: {\n      pattern: /(?:\\B\\.\\B|\\b[01]:|<[=>]?|>=?|[:+\\-*%,!?~=|$&#@^]):?|\\b_\\b:?/,\n      alias: 'operator'\n    },\n    punctuation: /[(){}\\[\\];.]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/q.js\n"));

/***/ })

}]);