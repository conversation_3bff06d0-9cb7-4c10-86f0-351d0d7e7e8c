"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jsstacktrace"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsstacktrace.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsstacktrace.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = jsstacktrace\njsstacktrace.displayName = 'jsstacktrace'\njsstacktrace.aliases = []\nfunction jsstacktrace(Prism) {\n  Prism.languages.jsstacktrace = {\n    'error-message': {\n      pattern: /^\\S.*/m,\n      alias: 'string'\n    },\n    'stack-frame': {\n      pattern: /(^[ \\t]+)at[ \\t].*/m,\n      lookbehind: true,\n      inside: {\n        'not-my-code': {\n          pattern:\n            /^at[ \\t]+(?!\\s)(?:node\\.js|<unknown>|.*(?:node_modules|\\(<anonymous>\\)|\\(<unknown>|<anonymous>$|\\(internal\\/|\\(node\\.js)).*/m,\n          alias: 'comment'\n        },\n        filename: {\n          pattern: /(\\bat\\s+(?!\\s)|\\()(?:[a-zA-Z]:)?[^():]+(?=:)/,\n          lookbehind: true,\n          alias: 'url'\n        },\n        function: {\n          pattern:\n            /(\\bat\\s+(?:new\\s+)?)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF<][.$\\w\\xA0-\\uFFFF<>]*/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()]/,\n        keyword: /\\b(?:at|new)\\b/,\n        alias: {\n          pattern: /\\[(?:as\\s+)?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF][$\\w\\xA0-\\uFFFF]*\\]/,\n          alias: 'variable'\n        },\n        'line-number': {\n          pattern: /:\\d+(?::\\d+)?\\b/,\n          alias: 'number',\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jsstacktrace.js\n"));

/***/ })

}]);