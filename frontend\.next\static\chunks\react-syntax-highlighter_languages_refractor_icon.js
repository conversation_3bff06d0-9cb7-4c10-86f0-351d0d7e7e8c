"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_icon"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icon.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icon.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = icon\nicon.displayName = 'icon'\nicon.aliases = []\nfunction icon(Prism) {\n  Prism.languages.icon = {\n    comment: /#.*/,\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n_]|\\\\.|_(?!\\1)(?:\\r\\n|[\\s\\S]))*\\1/,\n      greedy: true\n    },\n    number: /\\b(?:\\d+r[a-z\\d]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b|\\.\\d+\\b/i,\n    'builtin-keyword': {\n      pattern:\n        /&(?:allocated|ascii|clock|collections|cset|current|date|dateline|digits|dump|e|error(?:number|text|value)?|errout|fail|features|file|host|input|lcase|letters|level|line|main|null|output|phi|pi|pos|progname|random|regions|source|storage|subject|time|trace|ucase|version)\\b/,\n      alias: 'variable'\n    },\n    directive: {\n      pattern: /\\$\\w+/,\n      alias: 'builtin'\n    },\n    keyword:\n      /\\b(?:break|by|case|create|default|do|else|end|every|fail|global|if|initial|invocable|link|local|next|not|of|procedure|record|repeat|return|static|suspend|then|to|until|while)\\b/,\n    function: /\\b(?!\\d)\\w+(?=\\s*[({]|\\s*!\\s*\\[)/,\n    operator:\n      /[+-]:(?!=)|(?:[\\/?@^%&]|\\+\\+?|--?|==?=?|~==?=?|\\*\\*?|\\|\\|\\|?|<(?:->?|<?=?)|>>?=?)(?::=)?|:(?:=:?)?|[!.\\\\|~]/,\n    punctuation: /[\\[\\](){},;]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icon.js\n"));

/***/ })

}]);