"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nevod"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nevod.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nevod.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nevod\nnevod.displayName = 'nevod'\nnevod.aliases = []\nfunction nevod(Prism) {\n  Prism.languages.nevod = {\n    comment: /\\/\\/.*|(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$))/,\n    string: {\n      pattern: /(?:\"(?:\"\"|[^\"])*\"(?!\")|'(?:''|[^'])*'(?!'))!?\\*?/,\n      greedy: true,\n      inside: {\n        'string-attrs': /!$|!\\*$|\\*$/\n      }\n    },\n    namespace: {\n      pattern: /(@namespace\\s+)[a-zA-Z0-9\\-.]+(?=\\s*\\{)/,\n      lookbehind: true\n    },\n    pattern: {\n      pattern:\n        /(@pattern\\s+)?#?[a-zA-Z0-9\\-.]+(?:\\s*\\(\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]+\\s*(?:,\\s*(?:~\\s*)?[a-zA-Z0-9\\-.]*)*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      inside: {\n        'pattern-name': {\n          pattern: /^#?[a-zA-Z0-9\\-.]+/,\n          alias: 'class-name'\n        },\n        fields: {\n          pattern: /\\(.*\\)/,\n          inside: {\n            'field-name': {\n              pattern: /[a-zA-Z0-9\\-.]+/,\n              alias: 'variable'\n            },\n            punctuation: /[,()]/,\n            operator: {\n              pattern: /~/,\n              alias: 'field-hidden-mark'\n            }\n          }\n        }\n      }\n    },\n    search: {\n      pattern: /(@search\\s+|#)[a-zA-Z0-9\\-.]+(?:\\.\\*)?(?=\\s*;)/,\n      alias: 'function',\n      lookbehind: true\n    },\n    keyword:\n      /@(?:having|inside|namespace|outside|pattern|require|search|where)\\b/,\n    'standard-pattern': {\n      pattern:\n        /\\b(?:Alpha|AlphaNum|Any|Blank|End|LineBreak|Num|NumAlpha|Punct|Space|Start|Symbol|Word|WordBreak)\\b(?:\\([a-zA-Z0-9\\-.,\\s+]*\\))?/,\n      inside: {\n        'standard-pattern-name': {\n          pattern: /^[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        quantifier: {\n          pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n          alias: 'number'\n        },\n        'standard-pattern-attr': {\n          pattern: /[a-zA-Z0-9\\-.]+/,\n          alias: 'builtin'\n        },\n        punctuation: /[,()]/\n      }\n    },\n    quantifier: {\n      pattern: /\\b\\d+(?:\\s*\\+|\\s*-\\s*\\d+)?(?!\\w)/,\n      alias: 'number'\n    },\n    operator: [\n      {\n        pattern: /=/,\n        alias: 'pattern-def'\n      },\n      {\n        pattern: /&/,\n        alias: 'conjunction'\n      },\n      {\n        pattern: /~/,\n        alias: 'exception'\n      },\n      {\n        pattern: /\\?/,\n        alias: 'optionality'\n      },\n      {\n        pattern: /[[\\]]/,\n        alias: 'repetition'\n      },\n      {\n        pattern: /[{}]/,\n        alias: 'variation'\n      },\n      {\n        pattern: /[+_]/,\n        alias: 'sequence'\n      },\n      {\n        pattern: /\\.{2,3}/,\n        alias: 'span'\n      }\n    ],\n    'field-capture': [\n      {\n        pattern:\n          /([a-zA-Z0-9\\-.]+\\s*\\()\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+(?:\\s*,\\s*[a-zA-Z0-9\\-.]+\\s*:\\s*[a-zA-Z0-9\\-.]+)*(?=\\s*\\))/,\n        lookbehind: true,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      },\n      {\n        pattern: /[a-zA-Z0-9\\-.]+\\s*:/,\n        inside: {\n          'field-name': {\n            pattern: /[a-zA-Z0-9\\-.]+/,\n            alias: 'variable'\n          },\n          colon: /:/\n        }\n      }\n    ],\n    punctuation: /[:;,()]/,\n    name: /[a-zA-Z0-9\\-.]+/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nevod.js\n"));

/***/ })

}]);