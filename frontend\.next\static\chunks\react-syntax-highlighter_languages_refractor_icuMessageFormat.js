"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_icuMessageFormat"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icu-message-format.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icu-message-format.js ***!
  \**********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = icuMessageFormat\nicuMessageFormat.displayName = 'icuMessageFormat'\nicuMessageFormat.aliases = []\nfunction icuMessageFormat(Prism) {\n  // https://unicode-org.github.io/icu/userguide/format_parse/messages/\n  // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/MessageFormat.html\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @param {number} level\n     * @returns {string}\n     */\n    function nested(source, level) {\n      if (level <= 0) {\n        return /[]/.source\n      } else {\n        return source.replace(/<SELF>/g, function () {\n          return nested(source, level - 1)\n        })\n      }\n    }\n    var stringPattern = /'[{}:=,](?:[^']|'')*'(?!')/\n    var escape = {\n      pattern: /''/,\n      greedy: true,\n      alias: 'operator'\n    }\n    var string = {\n      pattern: stringPattern,\n      greedy: true,\n      inside: {\n        escape: escape\n      }\n    }\n    var argumentSource = nested(\n      /\\{(?:[^{}']|'(?![{},'])|''|<STR>|<SELF>)*\\}/.source.replace(\n        /<STR>/g,\n        function () {\n          return stringPattern.source\n        }\n      ),\n      8\n    )\n    var nestedMessage = {\n      pattern: RegExp(argumentSource),\n      inside: {\n        message: {\n          pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'message-delimiter': {\n          pattern: /./,\n          alias: 'punctuation'\n        }\n      }\n    }\n    Prism.languages['icu-message-format'] = {\n      argument: {\n        pattern: RegExp(argumentSource),\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n            lookbehind: true,\n            inside: {\n              'argument-name': {\n                pattern: /^(\\s*)[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'choice-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4c/classicu_1_1ChoiceFormat.html#details\n                pattern: /^(\\s*,\\s*choice\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  punctuation: /\\|/,\n                  range: {\n                    pattern: /^(\\s*)[+-]?(?:\\d+(?:\\.\\d*)?|\\u221e)\\s*[<#\\u2264]/,\n                    lookbehind: true,\n                    inside: {\n                      operator: /[<#\\u2264]/,\n                      number: /\\S+/\n                    }\n                  },\n                  rest: null // see below\n                }\n              },\n              'plural-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/PluralFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern:\n                  /^(\\s*,\\s*(?:plural|selectordinal)\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  offset: /^offset:\\s*\\d+/,\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /=\\d+|[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^(?:few|many|one|other|two|zero)$/\n                    }\n                  }\n                }\n              },\n              'select-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/SelectFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern: /^(\\s*,\\s*select\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^other$/\n                    }\n                  }\n                }\n              },\n              keyword: /\\b(?:choice|plural|select|selectordinal)\\b/,\n              'arg-type': {\n                pattern: /\\b(?:date|duration|number|ordinal|spellout|time)\\b/,\n                alias: 'keyword'\n              },\n              'arg-skeleton': {\n                pattern: /(,\\s*)::[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'arg-style': {\n                pattern:\n                  /(,\\s*)(?:currency|full|integer|long|medium|percent|short)(?=\\s*$)/,\n                lookbehind: true\n              },\n              'arg-style-text': {\n                pattern: RegExp(\n                  /(^\\s*,\\s*(?=\\S))/.source +\n                    nested(/(?:[^{}']|'[^']*'|\\{(?:<SELF>)?\\})+/.source, 8) +\n                    '$'\n                ),\n                lookbehind: true,\n                alias: 'string'\n              },\n              punctuation: /,/\n            }\n          },\n          'argument-delimiter': {\n            pattern: /./,\n            alias: 'operator'\n          }\n        }\n      },\n      escape: escape,\n      string: string\n    }\n    nestedMessage.inside.message.inside = Prism.languages['icu-message-format']\n    Prism.languages['icu-message-format'].argument.inside.content.inside[\n      'choice-style'\n    ].inside.rest = Prism.languages['icu-message-format']\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/icu-message-format.js\n"));

/***/ })

}]);