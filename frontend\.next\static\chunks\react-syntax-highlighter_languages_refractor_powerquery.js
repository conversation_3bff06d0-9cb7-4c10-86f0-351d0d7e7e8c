"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_powerquery"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powerquery.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powerquery.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = powerquery\npowerquery.displayName = 'powerquery'\npowerquery.aliases = []\nfunction powerquery(Prism) {\n  // https://docs.microsoft.com/en-us/powerquery-m/power-query-m-language-specification\n  Prism.languages.powerquery = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'quoted-identifier': {\n      pattern: /#\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:#!)?\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    constant: [\n      /\\bDay\\.(?:Friday|Monday|Saturday|Sunday|Thursday|Tuesday|Wednesday)\\b/,\n      /\\bTraceLevel\\.(?:Critical|Error|Information|Verbose|Warning)\\b/,\n      /\\bOccurrence\\.(?:All|First|Last)\\b/,\n      /\\bOrder\\.(?:Ascending|Descending)\\b/,\n      /\\bRoundingMode\\.(?:AwayFromZero|Down|ToEven|TowardZero|Up)\\b/,\n      /\\bMissingField\\.(?:Error|Ignore|UseNull)\\b/,\n      /\\bQuoteStyle\\.(?:Csv|None)\\b/,\n      /\\bJoinKind\\.(?:FullOuter|Inner|LeftAnti|LeftOuter|RightAnti|RightOuter)\\b/,\n      /\\bGroupKind\\.(?:Global|Local)\\b/,\n      /\\bExtraValues\\.(?:Error|Ignore|List)\\b/,\n      /\\bJoinAlgorithm\\.(?:Dynamic|LeftHash|LeftIndex|PairwiseHash|RightHash|RightIndex|SortMerge)\\b/,\n      /\\bJoinSide\\.(?:Left|Right)\\b/,\n      /\\bPrecision\\.(?:Decimal|Double)\\b/,\n      /\\bRelativePosition\\.From(?:End|Start)\\b/,\n      /\\bTextEncoding\\.(?:Ascii|BigEndianUnicode|Unicode|Utf16|Utf8|Windows)\\b/,\n      /\\b(?:Any|Binary|Date|DateTime|DateTimeZone|Duration|Function|Int16|Int32|Int64|Int8|List|Logical|None|Number|Record|Table|Text|Time)\\.Type\\b/,\n      /\\bnull\\b/\n    ],\n    boolean: /\\b(?:false|true)\\b/,\n    keyword:\n      /\\b(?:and|as|each|else|error|if|in|is|let|meta|not|nullable|optional|or|otherwise|section|shared|then|try|type)\\b|#(?:binary|date|datetime|datetimezone|duration|infinity|nan|sections|shared|table|time)\\b/,\n    function: {\n      pattern: /(^|[^#\\w.])[a-z_][\\w.]*(?=\\s*\\()/i,\n      lookbehind: true\n    },\n    'data-type': {\n      pattern:\n        /\\b(?:any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|number|record|table|text|time)\\b/,\n      alias: 'class-name'\n    },\n    number: {\n      pattern:\n        /\\b0x[\\da-f]+\\b|(?:[+-]?(?:\\b\\d+\\.)?\\b\\d+|[+-]\\.\\d+|(^|[^.])\\B\\.\\d+)(?:e[+-]?\\d+)?\\b/i,\n      lookbehind: true\n    },\n    operator: /[-+*\\/&?@^]|<(?:=>?|>)?|>=?|=>?|\\.\\.\\.?/,\n    punctuation: /[,;\\[\\](){}]/\n  }\n  Prism.languages.pq = Prism.languages['powerquery']\n  Prism.languages.mscript = Prism.languages['powerquery']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/powerquery.js\n"));

/***/ })

}]);