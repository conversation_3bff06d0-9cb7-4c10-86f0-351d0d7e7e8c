"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_jolie"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jolie.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jolie.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = jolie\njolie.displayName = 'jolie'\njolie.aliases = []\nfunction jolie(Prism) {\n  Prism.languages.jolie = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /((?:\\b(?:as|courier|embed|in|inputPort|outputPort|service)\\b|@)[ \\t]*)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:as|cH|comp|concurrent|constants|courier|cset|csets|default|define|else|embed|embedded|execution|exit|extender|for|foreach|forward|from|global|if|import|in|include|init|inputPort|install|instanceof|interface|is_defined|linkIn|linkOut|main|new|nullProcess|outputPort|over|private|provide|public|scope|sequential|service|single|spawn|synchronized|this|throw|throws|type|undef|until|while|with)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*[@(])/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?l?/i,\n    operator: /-[-=>]?|\\+[+=]?|<[<=]?|[>=*!]=?|&&|\\|\\||[?\\/%^@|]/,\n    punctuation: /[()[\\]{},;.:]/,\n    builtin:\n      /\\b(?:Byte|any|bool|char|double|enum|float|int|length|long|ranges|regex|string|undefined|void)\\b/\n  })\n  Prism.languages.insertBefore('jolie', 'keyword', {\n    aggregates: {\n      pattern:\n        /(\\bAggregates\\s*:\\s*)(?:\\w+(?:\\s+with\\s+\\w+)?\\s*,\\s*)*\\w+(?:\\s+with\\s+\\w+)?/,\n      lookbehind: true,\n      inside: {\n        keyword: /\\bwith\\b/,\n        'class-name': /\\w+/,\n        punctuation: /,/\n      }\n    },\n    redirects: {\n      pattern:\n        /(\\bRedirects\\s*:\\s*)(?:\\w+\\s*=>\\s*\\w+\\s*,\\s*)*(?:\\w+\\s*=>\\s*\\w+)/,\n      lookbehind: true,\n      inside: {\n        punctuation: /,/,\n        'class-name': /\\w+/,\n        operator: /=>/\n      }\n    },\n    property: {\n      pattern:\n        /\\b(?:Aggregates|[Ii]nterfaces|Java|Javascript|Jolie|[Ll]ocation|OneWay|[Pp]rotocol|Redirects|RequestResponse)\\b(?=[ \\t]*:)/\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/jolie.js\n"));

/***/ })

}]);