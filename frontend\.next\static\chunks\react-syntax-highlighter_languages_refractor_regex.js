"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_regex"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/regex.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/regex.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = regex\nregex.displayName = 'regex'\nregex.aliases = []\nfunction regex(Prism) {\n  ;(function (Prism) {\n    var specialEscape = {\n      pattern: /\\\\[\\\\(){}[\\]^$+*?|.]/,\n      alias: 'escape'\n    }\n    var escape =\n      /\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/\n    var charSet = {\n      pattern: /\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var charSetWithoutDot = {\n      pattern: /\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var rangeChar = '(?:[^\\\\\\\\-]|' + escape.source + ')'\n    var range = RegExp(rangeChar + '-' + rangeChar) // the name of a capturing group\n    var groupName = {\n      pattern: /(<|')[^<>']+(?=[>']$)/,\n      lookbehind: true,\n      alias: 'variable'\n    }\n    Prism.languages.regex = {\n      'char-class': {\n        pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/,\n        lookbehind: true,\n        inside: {\n          'char-class-negation': {\n            pattern: /(^\\[)\\^/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          'char-class-punctuation': {\n            pattern: /^\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          range: {\n            pattern: range,\n            inside: {\n              escape: escape,\n              'range-punctuation': {\n                pattern: /-/,\n                alias: 'operator'\n              }\n            }\n          },\n          'special-escape': specialEscape,\n          'char-set': charSetWithoutDot,\n          escape: escape\n        }\n      },\n      'special-escape': specialEscape,\n      'char-set': charSet,\n      backreference: [\n        {\n          // a backreference which is not an octal escape\n          pattern: /\\\\(?![123][0-7]{2})[1-9]/,\n          alias: 'keyword'\n        },\n        {\n          pattern: /\\\\k<[^<>']+>/,\n          alias: 'keyword',\n          inside: {\n            'group-name': groupName\n          }\n        }\n      ],\n      anchor: {\n        pattern: /[$^]|\\\\[ABbGZz]/,\n        alias: 'function'\n      },\n      escape: escape,\n      group: [\n        {\n          // https://docs.oracle.com/javase/10/docs/api/java/util/regex/Pattern.html\n          // https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference?view=netframework-4.7.2#grouping-constructs\n          // (), (?<name>), (?'name'), (?>), (?:), (?=), (?!), (?<=), (?<!), (?is-m), (?i-m:)\n          pattern:\n            /\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,\n          alias: 'punctuation',\n          inside: {\n            'group-name': groupName\n          }\n        },\n        {\n          pattern: /\\)/,\n          alias: 'punctuation'\n        }\n      ],\n      quantifier: {\n        pattern: /(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/,\n        alias: 'number'\n      },\n      alternation: {\n        pattern: /\\|/,\n        alias: 'keyword'\n      }\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixFQUFFLGFBQWEsRUFBRSxJQUFJLGFBQWEsUUFBUSxJQUFJLFlBQVksRUFBRTtBQUNwRjtBQUNBLGdDQUFnQyxJQUFJLElBQUk7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLElBQUksSUFBSTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLEVBQUU7QUFDdEM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGNBQWM7QUFDM0M7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWZyYWN0b3JAMy42LjBcXG5vZGVfbW9kdWxlc1xccmVmcmFjdG9yXFxsYW5nXFxyZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSByZWdleFxucmVnZXguZGlzcGxheU5hbWUgPSAncmVnZXgnXG5yZWdleC5hbGlhc2VzID0gW11cbmZ1bmN0aW9uIHJlZ2V4KFByaXNtKSB7XG4gIDsoZnVuY3Rpb24gKFByaXNtKSB7XG4gICAgdmFyIHNwZWNpYWxFc2NhcGUgPSB7XG4gICAgICBwYXR0ZXJuOiAvXFxcXFtcXFxcKCl7fVtcXF1eJCsqP3wuXS8sXG4gICAgICBhbGlhczogJ2VzY2FwZSdcbiAgICB9XG4gICAgdmFyIGVzY2FwZSA9XG4gICAgICAvXFxcXCg/OnhbXFxkYS1mQS1GXXsyfXx1W1xcZGEtZkEtRl17NH18dVxce1tcXGRhLWZBLUZdK1xcfXwwWzAtN117MCwyfXxbMTIzXVswLTddezJ9fGNbYS16QS1aXXwuKS9cbiAgICB2YXIgY2hhclNldCA9IHtcbiAgICAgIHBhdHRlcm46IC9cXC58XFxcXFt3c2RdfFxcXFxwXFx7W157fV0rXFx9L2ksXG4gICAgICBhbGlhczogJ2NsYXNzLW5hbWUnXG4gICAgfVxuICAgIHZhciBjaGFyU2V0V2l0aG91dERvdCA9IHtcbiAgICAgIHBhdHRlcm46IC9cXFxcW3dzZF18XFxcXHBcXHtbXnt9XStcXH0vaSxcbiAgICAgIGFsaWFzOiAnY2xhc3MtbmFtZSdcbiAgICB9XG4gICAgdmFyIHJhbmdlQ2hhciA9ICcoPzpbXlxcXFxcXFxcLV18JyArIGVzY2FwZS5zb3VyY2UgKyAnKSdcbiAgICB2YXIgcmFuZ2UgPSBSZWdFeHAocmFuZ2VDaGFyICsgJy0nICsgcmFuZ2VDaGFyKSAvLyB0aGUgbmFtZSBvZiBhIGNhcHR1cmluZyBncm91cFxuICAgIHZhciBncm91cE5hbWUgPSB7XG4gICAgICBwYXR0ZXJuOiAvKDx8JylbXjw+J10rKD89Wz4nXSQpLyxcbiAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICBhbGlhczogJ3ZhcmlhYmxlJ1xuICAgIH1cbiAgICBQcmlzbS5sYW5ndWFnZXMucmVnZXggPSB7XG4gICAgICAnY2hhci1jbGFzcyc6IHtcbiAgICAgICAgcGF0dGVybjogLygoPzpefFteXFxcXF0pKD86XFxcXFxcXFwpKilcXFsoPzpbXlxcXFxcXF1dfFxcXFxbXFxzXFxTXSkqXFxdLyxcbiAgICAgICAgbG9va2JlaGluZDogdHJ1ZSxcbiAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgJ2NoYXItY2xhc3MtbmVnYXRpb24nOiB7XG4gICAgICAgICAgICBwYXR0ZXJuOiAvKF5cXFspXFxeLyxcbiAgICAgICAgICAgIGxvb2tiZWhpbmQ6IHRydWUsXG4gICAgICAgICAgICBhbGlhczogJ29wZXJhdG9yJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgJ2NoYXItY2xhc3MtcHVuY3R1YXRpb24nOiB7XG4gICAgICAgICAgICBwYXR0ZXJuOiAvXlxcW3xcXF0kLyxcbiAgICAgICAgICAgIGFsaWFzOiAncHVuY3R1YXRpb24nXG4gICAgICAgICAgfSxcbiAgICAgICAgICByYW5nZToge1xuICAgICAgICAgICAgcGF0dGVybjogcmFuZ2UsXG4gICAgICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICAgICAgZXNjYXBlOiBlc2NhcGUsXG4gICAgICAgICAgICAgICdyYW5nZS1wdW5jdHVhdGlvbic6IHtcbiAgICAgICAgICAgICAgICBwYXR0ZXJuOiAvLS8sXG4gICAgICAgICAgICAgICAgYWxpYXM6ICdvcGVyYXRvcidcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgJ3NwZWNpYWwtZXNjYXBlJzogc3BlY2lhbEVzY2FwZSxcbiAgICAgICAgICAnY2hhci1zZXQnOiBjaGFyU2V0V2l0aG91dERvdCxcbiAgICAgICAgICBlc2NhcGU6IGVzY2FwZVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ3NwZWNpYWwtZXNjYXBlJzogc3BlY2lhbEVzY2FwZSxcbiAgICAgICdjaGFyLXNldCc6IGNoYXJTZXQsXG4gICAgICBiYWNrcmVmZXJlbmNlOiBbXG4gICAgICAgIHtcbiAgICAgICAgICAvLyBhIGJhY2tyZWZlcmVuY2Ugd2hpY2ggaXMgbm90IGFuIG9jdGFsIGVzY2FwZVxuICAgICAgICAgIHBhdHRlcm46IC9cXFxcKD8hWzEyM11bMC03XXsyfSlbMS05XS8sXG4gICAgICAgICAgYWxpYXM6ICdrZXl3b3JkJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgcGF0dGVybjogL1xcXFxrPFtePD4nXSs+LyxcbiAgICAgICAgICBhbGlhczogJ2tleXdvcmQnLFxuICAgICAgICAgIGluc2lkZToge1xuICAgICAgICAgICAgJ2dyb3VwLW5hbWUnOiBncm91cE5hbWVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBhbmNob3I6IHtcbiAgICAgICAgcGF0dGVybjogL1skXl18XFxcXFtBQmJHWnpdLyxcbiAgICAgICAgYWxpYXM6ICdmdW5jdGlvbidcbiAgICAgIH0sXG4gICAgICBlc2NhcGU6IGVzY2FwZSxcbiAgICAgIGdyb3VwOiBbXG4gICAgICAgIHtcbiAgICAgICAgICAvLyBodHRwczovL2RvY3Mub3JhY2xlLmNvbS9qYXZhc2UvMTAvZG9jcy9hcGkvamF2YS91dGlsL3JlZ2V4L1BhdHRlcm4uaHRtbFxuICAgICAgICAgIC8vIGh0dHBzOi8vZG9jcy5taWNyb3NvZnQuY29tL2VuLXVzL2RvdG5ldC9zdGFuZGFyZC9iYXNlLXR5cGVzL3JlZ3VsYXItZXhwcmVzc2lvbi1sYW5ndWFnZS1xdWljay1yZWZlcmVuY2U/dmlldz1uZXRmcmFtZXdvcmstNC43LjIjZ3JvdXBpbmctY29uc3RydWN0c1xuICAgICAgICAgIC8vICgpLCAoPzxuYW1lPiksICg/J25hbWUnKSwgKD8+KSwgKD86KSwgKD89KSwgKD8hKSwgKD88PSksICg/PCEpLCAoP2lzLW0pLCAoP2ktbTopXG4gICAgICAgICAgcGF0dGVybjpcbiAgICAgICAgICAgIC9cXCgoPzpcXD8oPzo8W148PiddKz58J1tePD4nXSsnfFs+Ol18PD9bPSFdfFtpZG1uc3V4VV0rKD86LVtpZG1uc3V4VV0rKT86PykpPy8sXG4gICAgICAgICAgYWxpYXM6ICdwdW5jdHVhdGlvbicsXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICAnZ3JvdXAtbmFtZSc6IGdyb3VwTmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHBhdHRlcm46IC9cXCkvLFxuICAgICAgICAgIGFsaWFzOiAncHVuY3R1YXRpb24nXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBxdWFudGlmaWVyOiB7XG4gICAgICAgIHBhdHRlcm46IC8oPzpbKyo/XXxcXHtcXGQrKD86LFxcZCopP1xcfSlbPytdPy8sXG4gICAgICAgIGFsaWFzOiAnbnVtYmVyJ1xuICAgICAgfSxcbiAgICAgIGFsdGVybmF0aW9uOiB7XG4gICAgICAgIHBhdHRlcm46IC9cXHwvLFxuICAgICAgICBhbGlhczogJ2tleXdvcmQnXG4gICAgICB9XG4gICAgfVxuICB9KShQcmlzbSlcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/regex.js\n"));

/***/ })

}]);