"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_gap"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gap.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gap.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = gap\ngap.displayName = 'gap'\ngap.aliases = []\nfunction gap(Prism) {\n  // https://www.gap-system.org/Manuals/doc/ref/chap4.html\n  // https://www.gap-system.org/Manuals/doc/ref/chap27.html\n  Prism.languages.gap = {\n    shell: {\n      pattern: /^gap>[\\s\\S]*?(?=^gap>|$(?![\\s\\S]))/m,\n      greedy: true,\n      inside: {\n        gap: {\n          pattern: /^(gap>).+(?:(?:\\r(?:\\n|(?!\\n))|\\n)>.*)*/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        punctuation: /^gap>/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /(^|[^\\\\'\"])(?:'(?:[^\\r\\n\\\\']|\\\\.){1,10}'|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"(?!\")|\"\"\"[\\s\\S]*?\"\"\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        continuation: {\n          pattern: /([\\r\\n])>/,\n          lookbehind: true,\n          alias: 'punctuation'\n        }\n      }\n    },\n    keyword:\n      /\\b(?:Assert|Info|IsBound|QUIT|TryNextMethod|Unbind|and|atomic|break|continue|do|elif|else|end|fi|for|function|if|in|local|mod|not|od|or|quit|readonly|readwrite|rec|repeat|return|then|until|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern:\n        /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    continuation: {\n      pattern: /([\\r\\n])>/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    operator: /->|[-+*/^~=!]|<>|[<>]=?|:=|\\.\\./,\n    punctuation: /[()[\\]{},;.:]/\n  }\n  Prism.languages.gap.shell.inside.gap.inside = Prism.languages.gap\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gap.js\n"));

/***/ })

}]);