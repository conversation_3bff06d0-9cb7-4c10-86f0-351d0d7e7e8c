"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_javadoc"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/java.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/java.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = java\njava.displayName = 'java'\njava.aliases = []\nfunction java(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/ // full package (optional) + parent classes (optional)\n    var classNamePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the java naming conventions\n    var className = {\n      pattern: RegExp(classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /\\./\n      }\n    }\n    Prism.languages.java = Prism.languages.extend('clike', {\n      string: {\n        pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        lookbehind: true,\n        greedy: true\n      },\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      function: [\n        Prism.languages.clike.function,\n        {\n          pattern: /(::\\s*)[a-z_]\\w*/,\n          lookbehind: true\n        }\n      ],\n      number:\n        /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n      operator: {\n        pattern:\n          /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n        lookbehind: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'string', {\n      'triple-quoted-string': {\n        // http://openjdk.java.net/jeps/355#Description\n        pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n        greedy: true,\n        alias: 'string'\n      },\n      char: {\n        pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'class-name', {\n      annotation: {\n        pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      },\n      namespace: {\n        pattern: RegExp(\n          /(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(\n            /<keyword>/g,\n            function () {\n              return keywords.source\n            }\n          )\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/java.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoc.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoc.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorJava = __webpack_require__(/*! ./java.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/java.js\")\nvar refractorJavadoclike = __webpack_require__(/*! ./javadoclike.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js\")\nmodule.exports = javadoc\njavadoc.displayName = 'javadoc'\njavadoc.aliases = []\nfunction javadoc(Prism) {\n  Prism.register(refractorJava)\n  Prism.register(refractorJavadoclike)\n  ;(function (Prism) {\n    var codeLinePattern = /(^(?:[\\t ]*(?:\\*\\s*)*))[^*\\s].*$/m\n    var memberReference = /#\\s*\\w+(?:\\s*\\([^()]*\\))?/.source\n    var reference =\n      /(?:\\b[a-zA-Z]\\w+\\s*\\.\\s*)*\\b[A-Z]\\w*(?:\\s*<mem>)?|<mem>/.source.replace(\n        /<mem>/g,\n        function () {\n          return memberReference\n        }\n      )\n    Prism.languages.javadoc = Prism.languages.extend('javadoclike', {})\n    Prism.languages.insertBefore('javadoc', 'keyword', {\n      reference: {\n        pattern: RegExp(\n          /(@(?:exception|link|linkplain|see|throws|value)\\s+(?:\\*\\s*)?)/\n            .source +\n            '(?:' +\n            reference +\n            ')'\n        ),\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /(#\\s*)\\w+(?=\\s*\\()/,\n            lookbehind: true\n          },\n          field: {\n            pattern: /(#\\s*)\\w+/,\n            lookbehind: true\n          },\n          namespace: {\n            pattern: /\\b(?:[a-z]\\w*\\s*\\.\\s*)+/,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          'class-name': /\\b[A-Z]\\w*/,\n          keyword: Prism.languages.java.keyword,\n          punctuation: /[#()[\\],.]/\n        }\n      },\n      'class-name': {\n        // @param <T> the first generic type parameter\n        pattern: /(@param\\s+)<[A-Z]\\w*>/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[.<>]/\n        }\n      },\n      'code-section': [\n        {\n          pattern:\n            /(\\{@code\\s+(?!\\s))(?:[^\\s{}]|\\s+(?![\\s}])|\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\})+(?=\\s*\\})/,\n          lookbehind: true,\n          inside: {\n            code: {\n              // there can't be any HTML inside of {@code} tags\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: Prism.languages.java,\n              alias: 'language-java'\n            }\n          }\n        },\n        {\n          pattern:\n            /(<(code|pre|tt)>(?!<code>)\\s*)\\S(?:\\S|\\s+\\S)*?(?=\\s*<\\/\\2>)/,\n          lookbehind: true,\n          inside: {\n            line: {\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: {\n                // highlight HTML tags and entities\n                tag: Prism.languages.markup.tag,\n                entity: Prism.languages.markup.entity,\n                code: {\n                  // everything else is Java code\n                  pattern: /.+/,\n                  inside: Prism.languages.java,\n                  alias: 'language-java'\n                }\n              }\n            }\n          }\n        }\n      ],\n      tag: Prism.languages.markup.tag,\n      entity: Prism.languages.markup.entity\n    })\n    Prism.languages.javadoclike.addSupport('java', Prism.languages.javadoc)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = javadoclike\njavadoclike.displayName = 'javadoclike'\njavadoclike.aliases = []\nfunction javadoclike(Prism) {\n  ;(function (Prism) {\n    var javaDocLike = (Prism.languages.javadoclike = {\n      parameter: {\n        pattern:\n          /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    })\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment'\n      var grammar = Prism.languages[lang]\n      if (!grammar) {\n        return\n      }\n      var token = grammar[tokenName]\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {}\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        }\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition)\n        token = grammar[tokenName]\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        }\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            }\n          }\n          callback(token[i])\n        }\n      } else {\n        callback(token)\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages]\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {}\n          }\n          pattern.inside.rest = docLanguage\n        })\n      })\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    })\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js\n"));

/***/ })

}]);