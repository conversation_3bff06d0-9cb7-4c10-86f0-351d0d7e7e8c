"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_coq"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coq.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coq.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = coq\ncoq.displayName = 'coq'\ncoq.aliases = []\nfunction coq(Prism) {\n  ;(function (Prism) {\n    // https://github.com/coq/coq\n    var commentSource = /\\(\\*(?:[^(*]|\\((?!\\*)|\\*(?!\\))|<self>)*\\*\\)/.source\n    for (var i = 0; i < 2; i++) {\n      commentSource = commentSource.replace(/<self>/g, function () {\n        return commentSource\n      })\n    }\n    commentSource = commentSource.replace(/<self>/g, '[]')\n    Prism.languages.coq = {\n      comment: RegExp(commentSource),\n      string: {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      },\n      attribute: [\n        {\n          pattern: RegExp(\n            /#\\[(?:[^\\[\\](\"]|\"(?:[^\"]|\"\")*\"(?!\")|\\((?!\\*)|<comment>)*\\]/.source.replace(\n              /<comment>/g,\n              function () {\n                return commentSource\n              }\n            )\n          ),\n          greedy: true,\n          alias: 'attr-name',\n          inside: {\n            comment: RegExp(commentSource),\n            string: {\n              pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n              greedy: true\n            },\n            operator: /=/,\n            punctuation: /^#\\[|\\]$|[,()]/\n          }\n        },\n        {\n          pattern:\n            /\\b(?:Cumulative|Global|Local|Monomorphic|NonCumulative|Polymorphic|Private|Program)\\b/,\n          alias: 'attr-name'\n        }\n      ],\n      keyword:\n        /\\b(?:Abort|About|Add|Admit|Admitted|All|Arguments|As|Assumptions|Axiom|Axioms|Back|BackTo|Backtrace|BinOp|BinOpSpec|BinRel|Bind|Blacklist|Canonical|Case|Cd|Check|Class|Classes|Close|CoFixpoint|CoInductive|Coercion|Coercions|Collection|Combined|Compute|Conjecture|Conjectures|Constant|Constants|Constraint|Constructors|Context|Corollary|Create|CstOp|Custom|Cut|Debug|Declare|Defined|Definition|Delimit|Dependencies|Dependent|Derive|Diffs|Drop|Elimination|End|Entry|Equality|Eval|Example|Existential|Existentials|Existing|Export|Extern|Extraction|Fact|Fail|Field|File|Firstorder|Fixpoint|Flags|Focus|From|Funclass|Function|Functional|GC|Generalizable|Goal|Grab|Grammar|Graph|Guarded|Haskell|Heap|Hide|Hint|HintDb|Hints|Hypotheses|Hypothesis|IF|Identity|Immediate|Implicit|Implicits|Import|Include|Induction|Inductive|Infix|Info|Initial|InjTyp|Inline|Inspect|Instance|Instances|Intro|Intros|Inversion|Inversion_clear|JSON|Language|Left|Lemma|Let|Lia|Libraries|Library|Load|LoadPath|Locate|Ltac|Ltac2|ML|Match|Method|Minimality|Module|Modules|Morphism|Next|NoInline|Notation|Number|OCaml|Obligation|Obligations|Opaque|Open|Optimize|Parameter|Parameters|Parametric|Path|Paths|Prenex|Preterm|Primitive|Print|Profile|Projections|Proof|Prop|PropBinOp|PropOp|PropUOp|Property|Proposition|Pwd|Qed|Quit|Rec|Record|Recursive|Redirect|Reduction|Register|Relation|Remark|Remove|Require|Reserved|Reset|Resolve|Restart|Rewrite|Right|Ring|Rings|SProp|Saturate|Save|Scheme|Scope|Scopes|Search|SearchHead|SearchPattern|SearchRewrite|Section|Separate|Set|Setoid|Show|Signatures|Solve|Solver|Sort|Sortclass|Sorted|Spec|Step|Strategies|Strategy|String|Structure|SubClass|Subgraph|SuchThat|Tactic|Term|TestCompile|Theorem|Time|Timeout|To|Transparent|Type|Typeclasses|Types|Typing|UnOp|UnOpSpec|Undelimit|Undo|Unfocus|Unfocused|Unfold|Universe|Universes|Unshelve|Variable|Variables|Variant|Verbose|View|Visibility|Zify|_|apply|as|at|by|cofix|else|end|exists|exists2|fix|for|forall|fun|if|in|let|match|measure|move|removed|return|struct|then|using|wf|where|with)\\b/,\n      number:\n        /\\b(?:0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]+)?(?:p[+-]?\\d[\\d_]*)?|\\d[\\d_]*(?:\\.[\\d_]+)?(?:e[+-]?\\d[\\d_]*)?)\\b/i,\n      punct: {\n        pattern: /@\\{|\\{\\||\\[=|:>/,\n        alias: 'punctuation'\n      },\n      operator:\n        /\\/\\\\|\\\\\\/|\\.{2,3}|:{1,2}=|\\*\\*|[-=]>|<(?:->?|[+:=>]|<:)|>(?:=|->)|\\|[-|]?|[-!%&*+/<=>?@^~']/,\n      punctuation: /\\.\\(|`\\(|@\\{|`\\{|\\{\\||\\[=|:>|[:.,;(){}\\[\\]]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coq.js\n"));

/***/ })

}]);