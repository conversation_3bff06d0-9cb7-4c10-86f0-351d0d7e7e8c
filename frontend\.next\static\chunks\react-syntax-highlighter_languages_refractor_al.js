"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_al"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/al.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/al.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = al\nal.displayName = 'al'\nal.aliases = []\nfunction al(Prism) {\n  // based on https://github.com/microsoft/AL/blob/master/grammar/alsyntax.tmlanguage\n  Prism.languages.al = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /'(?:''|[^'\\r\\n])*'(?!')|\"(?:\"\"|[^\"\\r\\n])*\"(?!\")/,\n      greedy: true\n    },\n    function: {\n      pattern:\n        /(\\b(?:event|procedure|trigger)\\s+|(?:^|[^.])\\.\\s*)[a-z_]\\w*(?=\\s*\\()/i,\n      lookbehind: true\n    },\n    keyword: [\n      // keywords\n      /\\b(?:array|asserterror|begin|break|case|do|downto|else|end|event|exit|for|foreach|function|if|implements|in|indataset|interface|internal|local|of|procedure|program|protected|repeat|runonclient|securityfiltering|suppressdispose|temporary|then|to|trigger|until|var|while|with|withevents)\\b/i, // objects and metadata that are used like keywords\n      /\\b(?:action|actions|addafter|addbefore|addfirst|addlast|area|assembly|chartpart|codeunit|column|controladdin|cuegroup|customizes|dataitem|dataset|dotnet|elements|enum|enumextension|extends|field|fieldattribute|fieldelement|fieldgroup|fieldgroups|fields|filter|fixed|grid|group|key|keys|label|labels|layout|modify|moveafter|movebefore|movefirst|movelast|page|pagecustomization|pageextension|part|profile|query|repeater|report|requestpage|schema|separator|systempart|table|tableelement|tableextension|textattribute|textelement|type|usercontrol|value|xmlport)\\b/i\n    ],\n    number:\n      /\\b(?:0x[\\da-f]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?)(?:F|LL?|U(?:LL?)?)?\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    variable: /\\b(?:Curr(?:FieldNo|Page|Report)|x?Rec|RequestOptionsPage)\\b/,\n    'class-name':\n      /\\b(?:automation|biginteger|bigtext|blob|boolean|byte|char|clienttype|code|completiontriggererrorlevel|connectiontype|database|dataclassification|datascope|date|dateformula|datetime|decimal|defaultlayout|dialog|dictionary|dotnetassembly|dotnettypedeclaration|duration|errorinfo|errortype|executioncontext|executionmode|fieldclass|fieldref|fieldtype|file|filterpagebuilder|guid|httpclient|httpcontent|httpheaders|httprequestmessage|httpresponsemessage|instream|integer|joker|jsonarray|jsonobject|jsontoken|jsonvalue|keyref|list|moduledependencyinfo|moduleinfo|none|notification|notificationscope|objecttype|option|outstream|pageresult|record|recordid|recordref|reportformat|securityfilter|sessionsettings|tableconnectiontype|tablefilter|testaction|testfield|testfilterfield|testpage|testpermissions|testrequestpage|text|textbuilder|textconst|textencoding|time|transactionmodel|transactiontype|variant|verbosity|version|view|views|webserviceactioncontext|webserviceactionresultcode|xmlattribute|xmlattributecollection|xmlcdata|xmlcomment|xmldeclaration|xmldocument|xmldocumenttype|xmlelement|xmlnamespacemanager|xmlnametable|xmlnode|xmlnodelist|xmlprocessinginstruction|xmlreadoptions|xmltext|xmlwriteoptions)\\b/i,\n    operator: /\\.\\.|:[=:]|[-+*/]=?|<>|[<>]=?|=|\\b(?:and|div|mod|not|or|xor)\\b/i,\n    punctuation: /[()\\[\\]{}:.;,]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/al.js\n"));

/***/ })

}]);