"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_apex"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apex.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apex.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorSql = __webpack_require__(/*! ./sql.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js\")\nmodule.exports = apex\napex.displayName = 'apex'\napex.aliases = []\nfunction apex(Prism) {\n  Prism.register(refractorSql)\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:(?:after|before)(?=\\s+[a-z])|abstract|activate|and|any|array|as|asc|autonomous|begin|bigdecimal|blob|boolean|break|bulk|by|byte|case|cast|catch|char|class|collect|commit|const|continue|currency|date|datetime|decimal|default|delete|desc|do|double|else|end|enum|exception|exit|export|extends|final|finally|float|for|from|get(?=\\s*[{};])|global|goto|group|having|hint|if|implements|import|in|inner|insert|instanceof|int|integer|interface|into|join|like|limit|list|long|loop|map|merge|new|not|null|nulls|number|object|of|on|or|outer|override|package|parallel|pragma|private|protected|public|retrieve|return|rollback|select|set|short|sObject|sort|static|string|super|switch|synchronized|system|testmethod|then|this|throw|time|transaction|transient|trigger|try|undelete|update|upsert|using|virtual|void|webservice|when|where|while|(?:inherited|with|without)\\s+sharing)\\b/i\n    var className =\n      /\\b(?:(?=[a-z_]\\w*\\s*[<\\[])|(?!<keyword>))[A-Z_]\\w*(?:\\s*\\.\\s*[A-Z_]\\w*)*\\b(?:\\s*(?:\\[\\s*\\]|<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>))*/.source.replace(\n        /<keyword>/g,\n        function () {\n          return keywords.source\n        }\n      )\n    /** @param {string} pattern */\n    function insertClassName(pattern) {\n      return RegExp(\n        pattern.replace(/<CLASS-NAME>/g, function () {\n          return className\n        }),\n        'i'\n      )\n    }\n    var classNameInside = {\n      keyword: keywords,\n      punctuation: /[()\\[\\]{};,:.<>]/\n    }\n    Prism.languages.apex = {\n      comment: Prism.languages.clike.comment,\n      string: Prism.languages.clike.string,\n      sql: {\n        pattern: /((?:[=,({:]|\\breturn)\\s*)\\[[^\\[\\]]*\\]/i,\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-sql',\n        inside: Prism.languages.sql\n      },\n      annotation: {\n        pattern: /@\\w+\\b/,\n        alias: 'punctuation'\n      },\n      'class-name': [\n        {\n          pattern: insertClassName(\n            /(\\b(?:class|enum|extends|implements|instanceof|interface|new|trigger\\s+\\w+\\s+on)\\s+)<CLASS-NAME>/\n              .source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // cast\n          pattern: insertClassName(\n            /(\\(\\s*)<CLASS-NAME>(?=\\s*\\)\\s*[\\w(])/.source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // variable/parameter declaration and return types\n          pattern: insertClassName(/<CLASS-NAME>(?=\\s*\\w+\\s*[;=,(){:])/.source),\n          inside: classNameInside\n        }\n      ],\n      trigger: {\n        pattern: /(\\btrigger\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      keyword: keywords,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      boolean: /\\b(?:false|true)\\b/i,\n      number: /(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d+|L)?)\\b/i,\n      operator:\n        /[!=](?:==?)?|\\?\\.?|&&|\\|\\||--|\\+\\+|[-+*/^&|]=?|:|<<?=?|>{1,3}=?/,\n      punctuation: /[()\\[\\]{};,.]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = sql\nsql.displayName = 'sql'\nsql.aliases = []\nfunction sql(Prism) {\n  Prism.languages.sql = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n      lookbehind: true\n    },\n    variable: [\n      {\n        pattern: /@([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1/,\n        greedy: true\n      },\n      /@[\\w.$]+/\n    ],\n    string: {\n      pattern: /(^|[^@\\\\])(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\]|\\2\\2)*\\2/,\n      greedy: true,\n      lookbehind: true\n    },\n    identifier: {\n      pattern: /(^|[^@\\\\])`(?:\\\\[\\s\\S]|[^`\\\\]|``)*`/,\n      greedy: true,\n      lookbehind: true,\n      inside: {\n        punctuation: /^`|`$/\n      }\n    },\n    function:\n      /\\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\\s*\\()/i,\n    // Should we highlight user defined functions too?\n    keyword:\n      /\\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\\b/i,\n    boolean: /\\b(?:FALSE|NULL|TRUE)\\b/i,\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i,\n    operator:\n      /[-+*\\/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\\b/i,\n    punctuation: /[;[\\]()`,.]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js\n"));

/***/ })

}]);