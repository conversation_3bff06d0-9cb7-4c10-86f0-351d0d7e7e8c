"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_mermaid"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mermaid.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mermaid.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = mermaid\nmermaid.displayName = 'mermaid'\nmermaid.aliases = []\nfunction mermaid(Prism) {\n  Prism.languages.mermaid = {\n    comment: {\n      pattern: /%%.*/,\n      greedy: true\n    },\n    style: {\n      pattern:\n        /^([ \\t]*(?:classDef|linkStyle|style)[ \\t]+[\\w$-]+[ \\t]+)\\w.*[^\\s;]/m,\n      lookbehind: true,\n      inside: {\n        property: /\\b\\w[\\w-]*(?=[ \\t]*:)/,\n        operator: /:/,\n        punctuation: /,/\n      }\n    },\n    'inter-arrow-label': {\n      pattern:\n        /([^<>ox.=-])(?:-[-.]|==)(?![<>ox.=-])[ \\t]*(?:\"[^\"\\r\\n]*\"|[^\\s\".=-](?:[^\\r\\n.=-]*[^\\s.=-])?)[ \\t]*(?:\\.+->?|--+[->]|==+[=>])(?![<>ox.=-])/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        arrow: {\n          pattern: /(?:\\.+->?|--+[->]|==+[=>])$/,\n          alias: 'operator'\n        },\n        label: {\n          pattern: /^([\\s\\S]{2}[ \\t]*)\\S(?:[\\s\\S]*\\S)?/,\n          lookbehind: true,\n          alias: 'property'\n        },\n        'arrow-head': {\n          pattern: /^\\S+/,\n          alias: ['arrow', 'operator']\n        }\n      }\n    },\n    arrow: [\n      // This might look complex but it really isn't.\n      // There are many possible arrows (see tests) and it's impossible to fit all of them into one pattern. The\n      // problem is that we only have one lookbehind per pattern. However, we cannot disallow too many arrow\n      // characters in the one lookbehind because that would create too many false negatives. So we have to split the\n      // arrows into different patterns.\n      {\n        // ER diagram\n        pattern: /(^|[^{}|o.-])[|}][|o](?:--|\\.\\.)[|o][|{](?![{}|o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // flow chart\n        // (?:==+|--+|-\\.*-)\n        pattern:\n          /(^|[^<>ox.=-])(?:[<ox](?:==+|--+|-\\.*-)[>ox]?|(?:==+|--+|-\\.*-)[>ox]|===+|---+|-\\.+-)(?![<>ox.=-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // sequence diagram\n        pattern:\n          /(^|[^<>()x-])(?:--?(?:>>|[x>)])(?![<>()x])|(?:<<|[x<(])--?(?!-))/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // class diagram\n        pattern:\n          /(^|[^<>|*o.-])(?:[*o]--|--[*o]|<\\|?(?:--|\\.\\.)|(?:--|\\.\\.)\\|?>|--|\\.\\.)(?![<>|*o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    ],\n    label: {\n      pattern: /(^|[^|<])\\|(?:[^\\r\\n\"|]|\"[^\"\\r\\n]*\")+\\|/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    text: {\n      pattern: /(?:[(\\[{]+|\\b>)(?:[^\\r\\n\"()\\[\\]{}]|\"[^\"\\r\\n]*\")+(?:[)\\]}]+|>)/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"/,\n      greedy: true\n    },\n    annotation: {\n      pattern:\n        /<<(?:abstract|choice|enumeration|fork|interface|join|service)>>|\\[\\[(?:choice|fork|join)\\]\\]/i,\n      alias: 'important'\n    },\n    keyword: [\n      // This language has both case-sensitive and case-insensitive keywords\n      {\n        pattern:\n          /(^[ \\t]*)(?:action|callback|class|classDef|classDiagram|click|direction|erDiagram|flowchart|gantt|gitGraph|graph|journey|link|linkStyle|pie|requirementDiagram|sequenceDiagram|stateDiagram|stateDiagram-v2|style|subgraph)(?![\\w$-])/m,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern:\n          /(^[ \\t]*)(?:activate|alt|and|as|autonumber|deactivate|else|end(?:[ \\t]+note)?|loop|opt|par|participant|rect|state|note[ \\t]+(?:over|(?:left|right)[ \\t]+of))(?![\\w$-])/im,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    entity: /#[a-z0-9]+;/,\n    operator: {\n      pattern: /(\\w[ \\t]*)&(?=[ \\t]*\\w)|:::|:/,\n      lookbehind: true\n    },\n    punctuation: /[(){};]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/mermaid.js\n"));

/***/ })

}]);