"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_solutionFile"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solution-file.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solution-file.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = solutionFile\nsolutionFile.displayName = 'solutionFile'\nsolutionFile.aliases = []\nfunction solutionFile(Prism) {\n  ;(function (Prism) {\n    var guid = {\n      // https://en.wikipedia.org/wiki/Universally_unique_identifier#Format\n      pattern: /\\{[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}\\}/i,\n      alias: 'constant',\n      inside: {\n        punctuation: /[{}]/\n      }\n    }\n    Prism.languages['solution-file'] = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n        greedy: true,\n        inside: {\n          guid: guid\n        }\n      },\n      object: {\n        // Foo\n        //   Bar(\"abs\") = 9\n        //   EndBar\n        //   Prop = TRUE\n        // EndFoo\n        pattern:\n          /^([ \\t]*)(?:([A-Z]\\w*)\\b(?=.*(?:\\r\\n?|\\n)(?:\\1[ \\t].*(?:\\r\\n?|\\n))*\\1End\\2(?=[ \\t]*$))|End[A-Z]\\w*(?=[ \\t]*$))/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'keyword'\n      },\n      property: {\n        pattern: /^([ \\t]*)(?!\\s)[^\\r\\n\"#=()]*[^\\s\"#=()](?=\\s*=)/m,\n        lookbehind: true,\n        inside: {\n          guid: guid\n        }\n      },\n      guid: guid,\n      number: /\\b\\d+(?:\\.\\d+)*\\b/,\n      boolean: /\\b(?:FALSE|TRUE)\\b/,\n      operator: /=/,\n      punctuation: /[(),]/\n    }\n    Prism.languages['sln'] = Prism.languages['solution-file']\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/solution-file.js\n"));

/***/ })

}]);