"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_systemd"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/systemd.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/systemd.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = systemd\nsystemd.displayName = 'systemd'\nsystemd.aliases = []\nfunction systemd(Prism) {\n  // https://www.freedesktop.org/software/systemd/man/systemd.syntax.html\n  ;(function (Prism) {\n    var comment = {\n      pattern: /^[;#].*/m,\n      greedy: true\n    }\n    var quotesSource = /\"(?:[^\\r\\n\"\\\\]|\\\\(?:[^\\r]|\\r\\n?))*\"(?!\\S)/.source\n    Prism.languages.systemd = {\n      comment: comment,\n      section: {\n        pattern: /^\\[[^\\n\\r\\[\\]]*\\](?=[ \\t]*$)/m,\n        greedy: true,\n        inside: {\n          punctuation: /^\\[|\\]$/,\n          'section-name': {\n            pattern: /[\\s\\S]+/,\n            alias: 'selector'\n          }\n        }\n      },\n      key: {\n        pattern: /^[^\\s=]+(?=[ \\t]*=)/m,\n        greedy: true,\n        alias: 'attr-name'\n      },\n      value: {\n        // This pattern is quite complex because of two properties:\n        //  1) Quotes (strings) must be preceded by a space. Since we can't use lookbehinds, we have to \"resolve\"\n        //     the lookbehind. You will see this in the main loop where spaces are handled separately.\n        //  2) Line continuations.\n        //     After line continuations, empty lines and comments are ignored so we have to consume them.\n        pattern: RegExp(\n          /(=[ \\t]*(?!\\s))/.source + // the value either starts with quotes or not\n            '(?:' +\n            quotesSource +\n            '|(?=[^\"\\r\\n]))' + // main loop\n            '(?:' +\n            (/[^\\s\\\\]/.source + // handle spaces separately because of quotes\n              '|' +\n              '[ \\t]+(?:(?![ \\t\"])|' +\n              quotesSource +\n              ')' + // line continuation\n              '|' +\n              /\\\\[\\r\\n]+(?:[#;].*[\\r\\n]+)*(?![#;])/.source) +\n            ')*'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'attr-value',\n        inside: {\n          comment: comment,\n          quoted: {\n            pattern: RegExp(/(^|\\s)/.source + quotesSource),\n            lookbehind: true,\n            greedy: true\n          },\n          punctuation: /\\\\$/m,\n          boolean: {\n            pattern: /^(?:false|no|off|on|true|yes)$/,\n            greedy: true\n          }\n        }\n      },\n      punctuation: /=/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/systemd.js\n"));

/***/ })

}]);