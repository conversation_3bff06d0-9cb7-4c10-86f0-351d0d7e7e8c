"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_pcaxis"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pcaxis.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pcaxis.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = pcaxis\npcaxis.displayName = 'pcaxis'\npcaxis.aliases = ['px']\nfunction pcaxis(Prism) {\n  Prism.languages.pcaxis = {\n    string: /\"[^\"]*\"/,\n    keyword: {\n      pattern:\n        /((?:^|;)\\s*)[-A-Z\\d]+(?:\\s*\\[[-\\w]+\\])?(?:\\s*\\(\"[^\"]*\"(?:,\\s*\"[^\"]*\")*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        keyword: /^[-A-Z\\d]+/,\n        language: {\n          pattern: /^(\\s*)\\[[-\\w]+\\]/,\n          lookbehind: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            property: /[-\\w]+/\n          }\n        },\n        'sub-key': {\n          pattern: /^(\\s*)\\S[\\s\\S]*/,\n          lookbehind: true,\n          inside: {\n            parameter: {\n              pattern: /\"[^\"]*\"/,\n              alias: 'property'\n            },\n            punctuation: /^\\(|\\)$|,/\n          }\n        }\n      }\n    },\n    operator: /=/,\n    tlist: {\n      pattern:\n        /TLIST\\s*\\(\\s*\\w+(?:(?:\\s*,\\s*\"[^\"]*\")+|\\s*,\\s*\"[^\"]*\"-\"[^\"]*\")?\\s*\\)/,\n      greedy: true,\n      inside: {\n        function: /^TLIST/,\n        property: {\n          pattern: /^(\\s*\\(\\s*)\\w+/,\n          lookbehind: true\n        },\n        string: /\"[^\"]*\"/,\n        punctuation: /[(),]/,\n        operator: /-/\n      }\n    },\n    punctuation: /[;,]/,\n    number: {\n      pattern: /(^|\\s)\\d+(?:\\.\\d+)?(?!\\S)/,\n      lookbehind: true\n    },\n    boolean: /NO|YES/\n  }\n  Prism.languages.px = Prism.languages.pcaxis\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pcaxis.js\n"));

/***/ })

}]);