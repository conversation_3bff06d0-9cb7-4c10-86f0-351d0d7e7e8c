"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_antlr4"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/antlr4.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/antlr4.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = antlr4\nantlr4.displayName = 'antlr4'\nantlr4.aliases = ['g4']\nfunction antlr4(Prism) {\n  Prism.languages.antlr4 = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    string: {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])*'/,\n      greedy: true\n    },\n    'character-class': {\n      pattern: /\\[(?:\\\\.|[^\\\\\\]\\r\\n])*\\]/,\n      greedy: true,\n      alias: 'regex',\n      inside: {\n        range: {\n          pattern: /([^[]|(?:^|[^\\\\])(?:\\\\\\\\)*\\\\\\[)-(?!\\])/,\n          lookbehind: true,\n          alias: 'punctuation'\n        },\n        escape:\n          /\\\\(?:u(?:[a-fA-F\\d]{4}|\\{[a-fA-F\\d]+\\})|[pP]\\{[=\\w-]+\\}|[^\\r\\nupP])/,\n        punctuation: /[\\[\\]]/\n      }\n    },\n    action: {\n      pattern: /\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\}/,\n      greedy: true,\n      inside: {\n        content: {\n          // this might be C, C++, Python, Java, C#, or any other language ANTLR4 compiles to\n          pattern: /(\\{)[\\s\\S]+(?=\\})/,\n          lookbehind: true\n        },\n        punctuation: /[{}]/\n      }\n    },\n    command: {\n      pattern:\n        /(->\\s*(?!\\s))(?:\\s*(?:,\\s*)?\\b[a-z]\\w*(?:\\s*\\([^()\\r\\n]*\\))?)+(?=\\s*;)/i,\n      lookbehind: true,\n      inside: {\n        function: /\\b\\w+(?=\\s*(?:[,(]|$))/,\n        punctuation: /[,()]/\n      }\n    },\n    annotation: {\n      pattern: /@\\w+(?:::\\w+)*/,\n      alias: 'keyword'\n    },\n    label: {\n      pattern: /#[ \\t]*\\w+/,\n      alias: 'punctuation'\n    },\n    keyword:\n      /\\b(?:catch|channels|finally|fragment|grammar|import|lexer|locals|mode|options|parser|returns|throws|tokens)\\b/,\n    definition: [\n      {\n        pattern: /\\b[a-z]\\w*(?=\\s*:)/,\n        alias: ['rule', 'class-name']\n      },\n      {\n        pattern: /\\b[A-Z]\\w*(?=\\s*:)/,\n        alias: ['token', 'constant']\n      }\n    ],\n    constant: /\\b[A-Z][A-Z_]*\\b/,\n    operator: /\\.\\.|->|[|~]|[*+?]\\??/,\n    punctuation: /[;:()=]/\n  }\n  Prism.languages.g4 = Prism.languages.antlr4\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/antlr4.js\n"));

/***/ })

}]);