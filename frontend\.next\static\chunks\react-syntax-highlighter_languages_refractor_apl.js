"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_apl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apl.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apl.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = apl\napl.displayName = 'apl'\napl.aliases = []\nfunction apl(Prism) {\n  Prism.languages.apl = {\n    comment: /(?:⍝|#[! ]).*$/m,\n    string: {\n      pattern: /'(?:[^'\\r\\n]|'')*'/,\n      greedy: true\n    },\n    number:\n      /¯?(?:\\d*\\.?\\b\\d+(?:e[+¯]?\\d+)?|¯|∞)(?:j¯?(?:(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:e[+¯]?\\d+)?|¯|∞))?/i,\n    statement: /:[A-Z][a-z][A-Za-z]*\\b/,\n    'system-function': {\n      pattern: /⎕[A-Z]+/i,\n      alias: 'function'\n    },\n    constant: /[⍬⌾#⎕⍞]/,\n    function: /[-+×÷⌈⌊∣|⍳⍸?*⍟○!⌹<≤=>≥≠≡≢∊⍷∪∩~∨∧⍱⍲⍴,⍪⌽⊖⍉↑↓⊂⊃⊆⊇⌷⍋⍒⊤⊥⍕⍎⊣⊢⍁⍂≈⍯↗¤→]/,\n    'monadic-operator': {\n      pattern: /[\\\\\\/⌿⍀¨⍨⌶&∥]/,\n      alias: 'operator'\n    },\n    'dyadic-operator': {\n      pattern: /[.⍣⍠⍤∘⌸@⌺⍥]/,\n      alias: 'operator'\n    },\n    assignment: {\n      pattern: /←/,\n      alias: 'keyword'\n    },\n    punctuation: /[\\[;\\]()◇⋄]/,\n    dfn: {\n      pattern: /[{}⍺⍵⍶⍹∇⍫:]/,\n      alias: 'builtin'\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/apl.js\n"));

/***/ })

}]);