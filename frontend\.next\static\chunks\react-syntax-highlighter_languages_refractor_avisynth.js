"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_avisynth"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avisynth.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avisynth.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = avisynth\navisynth.displayName = 'avisynth'\navisynth.aliases = ['avs']\nfunction avisynth(Prism) {\n  // http://avisynth.nl/index.php/The_full_AviSynth_grammar\n  ;(function (Prism) {\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return replacements[+index]\n      })\n    }\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    var types = /bool|clip|float|int|string|val/.source\n    var internals = [\n      // bools\n      /is(?:bool|clip|float|int|string)|defined|(?:(?:internal)?function|var)?exists?/\n        .source, // control\n      /apply|assert|default|eval|import|nop|select|undefined/.source, // global\n      /opt_(?:allowfloataudio|avipadscanlines|dwchannelmask|enable_(?:b64a|planartopackedrgb|v210|y3_10_10|y3_10_16)|usewaveextensible|vdubplanarhack)|set(?:cachemode|maxcpu|memorymax|planarlegacyalignment|workingdir)/\n        .source, // conv\n      /hex(?:value)?|value/.source, // numeric\n      /abs|ceil|continued(?:denominator|numerator)?|exp|floor|fmod|frac|log(?:10)?|max|min|muldiv|pi|pow|rand|round|sign|spline|sqrt/\n        .source, // trig\n      /a?sinh?|a?cosh?|a?tan[2h]?/.source, // bit\n      /(?:bit(?:and|not|x?or|[lr]?shift[aslu]?|sh[lr]|sa[lr]|[lr]rotatel?|ro[rl]|te?st|set(?:count)?|cl(?:ea)?r|ch(?:an)?ge?))/\n        .source, // runtime\n      /average(?:[bgr]|chroma[uv]|luma)|(?:[rgb]|chroma[uv]|luma|rgb|[yuv](?=difference(?:fromprevious|tonext)))difference(?:fromprevious|tonext)?|[yuvrgb]plane(?:median|min|max|minmaxdifference)/\n        .source, // script\n      /getprocessinfo|logmsg|script(?:dir(?:utf8)?|file(?:utf8)?|name(?:utf8)?)|setlogparams/\n        .source, // string\n      /chr|(?:fill|find|left|mid|replace|rev|right)str|format|[lu]case|ord|str(?:cmpi?|fromutf8|len|toutf8)|time|trim(?:all|left|right)/\n        .source, // version\n      /isversionorgreater|version(?:number|string)/.source, // helper\n      /buildpixeltype|colorspacenametopixeltype/.source, // avsplus\n      /addautoloaddir|on(?:cpu|cuda)|prefetch|setfiltermtmode/.source\n    ].join('|')\n    var properties = [\n      // content\n      /has(?:audio|video)/.source, // resolution\n      /height|width/.source, // framerate\n      /frame(?:count|rate)|framerate(?:denominator|numerator)/.source, // interlacing\n      /getparity|is(?:field|frame)based/.source, // color format\n      /bitspercomponent|componentsize|hasalpha|is(?:planar(?:rgba?)?|interleaved|rgb(?:24|32|48|64)?|y(?:8|u(?:va?|y2))?|yv(?:12|16|24|411)|420|422|444|packedrgb)|numcomponents|pixeltype/\n        .source, // audio\n      /audio(?:bits|channels|duration|length(?:[fs]|hi|lo)?|rate)|isaudio(?:float|int)/\n        .source\n    ].join('|')\n    var filters = [\n      // source\n      /avi(?:file)?source|directshowsource|image(?:reader|source|sourceanim)|opendmlsource|segmented(?:avisource|directshowsource)|wavsource/\n        .source, // color\n      /coloryuv|convertbacktoyuy2|convertto(?:RGB(?:24|32|48|64)|(?:planar)?RGBA?|Y8?|YV(?:12|16|24|411)|YUVA?(?:411|420|422|444)|YUY2)|fixluminance|gr[ae]yscale|invert|levels|limiter|mergea?rgb|merge(?:chroma|luma)|rgbadjust|show(?:alpha|blue|green|red)|swapuv|tweak|[uv]toy8?|ytouv/\n        .source, // overlay\n      /(?:colorkey|reset)mask|layer|mask(?:hs)?|merge|overlay|subtract/.source, // geometry\n      /addborders|(?:bicubic|bilinear|blackman|gauss|lanczos4|lanczos|point|sinc|spline(?:16|36|64))resize|crop(?:bottom)?|flip(?:horizontal|vertical)|(?:horizontal|vertical)?reduceby2|letterbox|skewrows|turn(?:180|left|right)/\n        .source, // pixel\n      /blur|fixbrokenchromaupsampling|generalconvolution|(?:spatial|temporal)soften|sharpen/\n        .source, // timeline\n      /trim|(?:un)?alignedsplice|(?:assume|assumescaled|change|convert)FPS|(?:delete|duplicate)frame|dissolve|fade(?:in|io|out)[02]?|freezeframe|interleave|loop|reverse|select(?:even|odd|(?:range)?every)/\n        .source, // interlace\n      /assume[bt]ff|assume(?:field|frame)based|bob|complementparity|doubleweave|peculiarblend|pulldown|separate(?:columns|fields|rows)|swapfields|weave(?:columns|rows)?/\n        .source, // audio\n      /amplify(?:db)?|assumesamplerate|audiodub(?:ex)?|audiotrim|convertaudioto(?:(?:8|16|24|32)bit|float)|converttomono|delayaudio|ensurevbrmp3sync|get(?:left|right)?channel|kill(?:audio|video)|mergechannels|mixaudio|monotostereo|normalize|resampleaudio|ssrc|supereq|timestretch/\n        .source, // conditional\n      /animate|applyrange|conditional(?:filter|reader|select)|frameevaluate|scriptclip|tcp(?:server|source)|writefile(?:end|if|start)?/\n        .source, // export\n      /imagewriter/.source, // debug\n      /blackness|blankclip|colorbars(?:hd)?|compare|dumpfiltergraph|echo|histogram|info|messageclip|preroll|setgraphanalysis|show(?:framenumber|smpte|time)|showfiveversions|stack(?:horizontal|vertical)|subtitle|tone|version/\n        .source\n    ].join('|')\n    var allinternals = [internals, properties, filters].join('|')\n    Prism.languages.avisynth = {\n      comment: [\n        {\n          // Matches [* *] nestable block comments, but only supports 1 level of nested comments\n          // /\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|<self>)*\\*\\]/\n          pattern:\n            /(^|[^\\\\])\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\]))*\\*\\])*\\*\\]/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches /* */ block comments\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches # comments\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // Handle before strings because optional arguments are surrounded by double quotes\n      argument: {\n        pattern: re(/\\b(?:<<0>>)\\s+(\"?)\\w+\\1/.source, [types], 'i'),\n        inside: {\n          keyword: /^\\w+/\n        }\n      },\n      // Optional argument assignment\n      'argument-label': {\n        pattern: /([,(][\\s\\\\]*)\\w+\\s*=(?!=)/,\n        lookbehind: true,\n        inside: {\n          'argument-name': {\n            pattern: /^\\w+/,\n            alias: 'punctuation'\n          },\n          punctuation: /=$/\n        }\n      },\n      string: [\n        {\n          // triple double-quoted\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true\n        },\n        {\n          // single double-quoted\n          pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n          greedy: true,\n          inside: {\n            constant: {\n              // These *are* case-sensitive!\n              pattern:\n                /\\b(?:DEFAULT_MT_MODE|(?:MAINSCRIPT|PROGRAM|SCRIPT)DIR|(?:MACHINE|USER)_(?:CLASSIC|PLUS)_PLUGINS)\\b/\n            }\n          }\n        }\n      ],\n      // The special \"last\" variable that takes the value of the last implicitly returned clip\n      variable: /\\b(?:last)\\b/i,\n      boolean: /\\b(?:false|no|true|yes)\\b/i,\n      keyword:\n        /\\b(?:catch|else|for|function|global|if|return|try|while|__END__)\\b/i,\n      constant: /\\bMT_(?:MULTI_INSTANCE|NICE_FILTER|SERIALIZED|SPECIAL_MT)\\b/,\n      // AviSynth's internal functions, filters, and properties\n      'builtin-function': {\n        pattern: re(/\\b(?:<<0>>)\\b/.source, [allinternals], 'i'),\n        alias: 'function'\n      },\n      'type-cast': {\n        pattern: re(/\\b(?:<<0>>)(?=\\s*\\()/.source, [types], 'i'),\n        alias: 'keyword'\n      },\n      // External/user-defined filters\n      function: {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()|(\\.)[a-z_]\\w*\\b/i,\n        lookbehind: true\n      },\n      // Matches a \\ as the first or last character on a line\n      'line-continuation': {\n        pattern: /(^[ \\t]*)\\\\|\\\\(?=[ \\t]*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      number:\n        /\\B\\$(?:[\\da-f]{6}|[\\da-f]{8})\\b|(?:(?:\\b|\\B-)\\d+(?:\\.\\d*)?\\b|\\B\\.\\d+\\b)/i,\n      operator: /\\+\\+?|[!=<>]=?|&&|\\|\\||[?:*/%-]/,\n      punctuation: /[{}\\[\\]();,.]/\n    }\n    Prism.languages.avs = Prism.languages.avisynth\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avisynth.js\n"));

/***/ })

}]);