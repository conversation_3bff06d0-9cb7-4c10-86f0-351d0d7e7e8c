"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_plsql"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/plsql.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/plsql.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar refractorSql = __webpack_require__(/*! ./sql.js */ \"(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js\")\nmodule.exports = plsql\nplsql.displayName = 'plsql'\nplsql.aliases = []\nfunction plsql(Prism) {\n  Prism.register(refractorSql)\n  Prism.languages.plsql = Prism.languages.extend('sql', {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|--.*/,\n      greedy: true\n    },\n    // https://docs.oracle.com/en/database/oracle/oracle-database/21/lnpls/plsql-reserved-words-keywords.html\n    keyword:\n      /\\b(?:A|ACCESSIBLE|ADD|AGENT|AGGREGATE|ALL|ALTER|AND|ANY|ARRAY|AS|ASC|AT|ATTRIBUTE|AUTHID|AVG|BEGIN|BETWEEN|BFILE_BASE|BINARY|BLOB_BASE|BLOCK|BODY|BOTH|BOUND|BULK|BY|BYTE|C|CALL|CALLING|CASCADE|CASE|CHAR|CHARACTER|CHARSET|CHARSETFORM|CHARSETID|CHAR_BASE|CHECK|CLOB_BASE|CLONE|CLOSE|CLUSTER|CLUSTERS|COLAUTH|COLLECT|COLUMNS|COMMENT|COMMIT|COMMITTED|COMPILED|COMPRESS|CONNECT|CONSTANT|CONSTRUCTOR|CONTEXT|CONTINUE|CONVERT|COUNT|CRASH|CREATE|CREDENTIAL|CURRENT|CURSOR|CUSTOMDATUM|DANGLING|DATA|DATE|DATE_BASE|DAY|DECLARE|DEFAULT|DEFINE|DELETE|DESC|DETERMINISTIC|DIRECTORY|DISTINCT|DOUBLE|DROP|DURATION|ELEMENT|ELSE|ELSIF|EMPTY|END|ESCAPE|EXCEPT|EXCEPTION|EXCEPTIONS|EXCLUSIVE|EXECUTE|EXISTS|EXIT|EXTERNAL|FETCH|FINAL|FIRST|FIXED|FLOAT|FOR|FORALL|FORCE|FROM|FUNCTION|GENERAL|GOTO|GRANT|GROUP|HASH|HAVING|HEAP|HIDDEN|HOUR|IDENTIFIED|IF|IMMEDIATE|IMMUTABLE|IN|INCLUDING|INDEX|INDEXES|INDICATOR|INDICES|INFINITE|INSERT|INSTANTIABLE|INT|INTERFACE|INTERSECT|INTERVAL|INTO|INVALIDATE|IS|ISOLATION|JAVA|LANGUAGE|LARGE|LEADING|LENGTH|LEVEL|LIBRARY|LIKE|LIKE2|LIKE4|LIKEC|LIMIT|LIMITED|LOCAL|LOCK|LONG|LOOP|MAP|MAX|MAXLEN|MEMBER|MERGE|MIN|MINUS|MINUTE|MOD|MODE|MODIFY|MONTH|MULTISET|MUTABLE|NAME|NAN|NATIONAL|NATIVE|NCHAR|NEW|NOCOMPRESS|NOCOPY|NOT|NOWAIT|NULL|NUMBER_BASE|OBJECT|OCICOLL|OCIDATE|OCIDATETIME|OCIDURATION|OCIINTERVAL|OCILOBLOCATOR|OCINUMBER|OCIRAW|OCIREF|OCIREFCURSOR|OCIROWID|OCISTRING|OCITYPE|OF|OLD|ON|ONLY|OPAQUE|OPEN|OPERATOR|OPTION|OR|ORACLE|ORADATA|ORDER|ORGANIZATION|ORLANY|ORLVARY|OTHERS|OUT|OVERLAPS|OVERRIDING|PACKAGE|PARALLEL_ENABLE|PARAMETER|PARAMETERS|PARENT|PARTITION|PASCAL|PERSISTABLE|PIPE|PIPELINED|PLUGGABLE|POLYMORPHIC|PRAGMA|PRECISION|PRIOR|PRIVATE|PROCEDURE|PUBLIC|RAISE|RANGE|RAW|READ|RECORD|REF|REFERENCE|RELIES_ON|REM|REMAINDER|RENAME|RESOURCE|RESULT|RESULT_CACHE|RETURN|RETURNING|REVERSE|REVOKE|ROLLBACK|ROW|SAMPLE|SAVE|SAVEPOINT|SB1|SB2|SB4|SECOND|SEGMENT|SELECT|SELF|SEPARATE|SEQUENCE|SERIALIZABLE|SET|SHARE|SHORT|SIZE|SIZE_T|SOME|SPARSE|SQL|SQLCODE|SQLDATA|SQLNAME|SQLSTATE|STANDARD|START|STATIC|STDDEV|STORED|STRING|STRUCT|STYLE|SUBMULTISET|SUBPARTITION|SUBSTITUTABLE|SUBTYPE|SUM|SYNONYM|TABAUTH|TABLE|TDO|THE|THEN|TIME|TIMESTAMP|TIMEZONE_ABBR|TIMEZONE_HOUR|TIMEZONE_MINUTE|TIMEZONE_REGION|TO|TRAILING|TRANSACTION|TRANSACTIONAL|TRUSTED|TYPE|UB1|UB2|UB4|UNDER|UNION|UNIQUE|UNPLUG|UNSIGNED|UNTRUSTED|UPDATE|USE|USING|VALIST|VALUE|VALUES|VARIABLE|VARIANCE|VARRAY|VARYING|VIEW|VIEWS|VOID|WHEN|WHERE|WHILE|WITH|WORK|WRAPPED|WRITE|YEAR|ZONE)\\b/i,\n    // https://docs.oracle.com/en/database/oracle/oracle-database/21/lnpls/plsql-language-fundamentals.html#GUID-96A42F7C-7A71-4B90-8255-CA9C8BD9722E\n    operator: /:=?|=>|[<>^~!]=|\\.\\.|\\|\\||\\*\\*|[-+*/%<>=@]/\n  })\n  Prism.languages.insertBefore('plsql', 'operator', {\n    label: {\n      pattern: /<<\\s*\\w+\\s*>>/,\n      alias: 'symbol'\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/plsql.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = sql\nsql.displayName = 'sql'\nsql.aliases = []\nfunction sql(Prism) {\n  Prism.languages.sql = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n      lookbehind: true\n    },\n    variable: [\n      {\n        pattern: /@([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])+\\1/,\n        greedy: true\n      },\n      /@[\\w.$]+/\n    ],\n    string: {\n      pattern: /(^|[^@\\\\])(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\]|\\2\\2)*\\2/,\n      greedy: true,\n      lookbehind: true\n    },\n    identifier: {\n      pattern: /(^|[^@\\\\])`(?:\\\\[\\s\\S]|[^`\\\\]|``)*`/,\n      greedy: true,\n      lookbehind: true,\n      inside: {\n        punctuation: /^`|`$/\n      }\n    },\n    function:\n      /\\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\\s*\\()/i,\n    // Should we highlight user defined functions too?\n    keyword:\n      /\\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\\b/i,\n    boolean: /\\b(?:FALSE|NULL|TRUE)\\b/i,\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+\\b/i,\n    operator:\n      /[-+*\\/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\\b/i,\n    punctuation: /[;[\\]()`,.]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sql.js\n"));

/***/ })

}]);