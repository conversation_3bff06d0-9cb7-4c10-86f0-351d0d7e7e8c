"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nginx"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nginx.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nginx.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nginx\nnginx.displayName = 'nginx'\nnginx.aliases = []\nfunction nginx(Prism) {\n  ;(function (Prism) {\n    var variable =\n      /\\$(?:\\w[a-z\\d]*(?:_[^\\x00-\\x1F\\s\"'\\\\()$]*)?|\\{[^}\\s\"'\\\\]+\\})/i\n    Prism.languages.nginx = {\n      comment: {\n        pattern: /(^|[\\s{};])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      directive: {\n        pattern:\n          /(^|\\s)\\w(?:[^;{}\"'\\\\\\s]|\\\\.|\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*'|\\s+(?:#.*(?!.)|(?![#\\s])))*?(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\\\\\)*)(?:\"(?:[^\"\\\\]|\\\\.)*\"|'(?:[^'\\\\]|\\\\.)*')/,\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              escape: {\n                pattern: /\\\\[\"'\\\\nrt]/,\n                alias: 'entity'\n              },\n              variable: variable\n            }\n          },\n          comment: {\n            pattern: /(\\s)#.*/,\n            lookbehind: true,\n            greedy: true\n          },\n          keyword: {\n            pattern: /^\\S+/,\n            greedy: true\n          },\n          // other patterns\n          boolean: {\n            pattern: /(\\s)(?:off|on)(?!\\S)/,\n            lookbehind: true\n          },\n          number: {\n            pattern: /(\\s)\\d+[a-z]*(?!\\S)/i,\n            lookbehind: true\n          },\n          variable: variable\n        }\n      },\n      punctuation: /[{};]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nginx.js\n"));

/***/ })

}]);