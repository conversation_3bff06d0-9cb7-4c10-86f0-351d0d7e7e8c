"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_nsis"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nsis.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nsis.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = nsis\nnsis.displayName = 'nsis'\nnsis.aliases = []\nfunction nsis(Prism) {\n  /**\n   * Original by Jan T. Sott (http://github.com/idleberg)\n   *\n   * Includes all commands and plug-ins shipped with NSIS 3.08\n   */\n  Prism.languages.nsis = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|[#;].*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword: {\n      pattern:\n        /(^[\\t ]*)(?:Abort|Add(?:BrandingImage|Size)|AdvSplash|Allow(?:RootDirInstall|SkipFiles)|AutoCloseWindow|BG(?:Font|Gradient|Image)|Banner|BrandingText|BringToFront|CRCCheck|Call(?:InstDLL)?|Caption|ChangeUI|CheckBitmap|ClearErrors|CompletedText|ComponentText|CopyFiles|Create(?:Directory|Font|ShortCut)|Delete(?:INISec|INIStr|RegKey|RegValue)?|Detail(?:Print|sButtonText)|Dialer|Dir(?:Text|Var|Verify)|EnableWindow|Enum(?:RegKey|RegValue)|Exch|Exec(?:Shell(?:Wait)?|Wait)?|ExpandEnvStrings|File(?:BufSize|Close|ErrorText|Open|Read|ReadByte|ReadUTF16LE|ReadWord|Seek|Write|WriteByte|WriteUTF16LE|WriteWord)?|Find(?:Close|First|Next|Window)|FlushINI|Get(?:CurInstType|CurrentAddress|DLLVersion(?:Local)?|DlgItem|ErrorLevel|FileTime(?:Local)?|FullPathName|Function(?:Address|End)?|InstDirError|LabelAddress|TempFileName)|Goto|HideWindow|Icon|If(?:Abort|Errors|FileExists|RebootFlag|Silent)|InitPluginsDir|InstProgressFlags|Inst(?:Type(?:GetText|SetText)?)|Install(?:ButtonText|Colors|Dir(?:RegKey)?)|Int(?:64|Ptr)?CmpU?|Int(?:64)?Fmt|Int(?:Ptr)?Op|IsWindow|Lang(?:DLL|String)|License(?:BkColor|Data|ForceSelection|LangString|Text)|LoadLanguageFile|LockWindow|Log(?:Set|Text)|Manifest(?:DPIAware|SupportedOS)|Math|MessageBox|MiscButtonText|NSISdl|Name|Nop|OutFile|PE(?:DllCharacteristics|SubsysVer)|Page(?:Callbacks)?|Pop|Push|Quit|RMDir|Read(?:EnvStr|INIStr|RegDWORD|RegStr)|Reboot|RegDLL|Rename|RequestExecutionLevel|ReserveFile|Return|SearchPath|Section(?:End|GetFlags|GetInstTypes|GetSize|GetText|Group|In|SetFlags|SetInstTypes|SetSize|SetText)?|SendMessage|Set(?:AutoClose|BrandingImage|Compress|Compressor(?:DictSize)?|CtlColors|CurInstType|DatablockOptimize|DateSave|Details(?:Print|View)|ErrorLevel|Errors|FileAttributes|Font|OutPath|Overwrite|PluginUnload|RebootFlag|RegView|ShellVarContext|Silent)|Show(?:InstDetails|UninstDetails|Window)|Silent(?:Install|UnInstall)|Sleep|SpaceTexts|Splash|StartMenu|Str(?:CmpS?|Cpy|Len)|SubCaption|System|UnRegDLL|Unicode|UninstPage|Uninstall(?:ButtonText|Caption|Icon|SubCaption|Text)|UserInfo|VI(?:AddVersionKey|FileVersion|ProductVersion)|VPatch|Var|WindowIcon|Write(?:INIStr|Reg(?:Bin|DWORD|ExpandStr|MultiStr|None|Str)|Uninstaller)|XPStyle|ns(?:Dialogs|Exec))\\b/m,\n      lookbehind: true\n    },\n    property:\n      /\\b(?:ARCHIVE|FILE_(?:ATTRIBUTE_ARCHIVE|ATTRIBUTE_NORMAL|ATTRIBUTE_OFFLINE|ATTRIBUTE_READONLY|ATTRIBUTE_SYSTEM|ATTRIBUTE_TEMPORARY)|HK(?:(?:CR|CU|LM)(?:32|64)?|DD|PD|U)|HKEY_(?:CLASSES_ROOT|CURRENT_CONFIG|CURRENT_USER|DYN_DATA|LOCAL_MACHINE|PERFORMANCE_DATA|USERS)|ID(?:ABORT|CANCEL|IGNORE|NO|OK|RETRY|YES)|MB_(?:ABORTRETRYIGNORE|DEFBUTTON1|DEFBUTTON2|DEFBUTTON3|DEFBUTTON4|ICONEXCLAMATION|ICONINFORMATION|ICONQUESTION|ICONSTOP|OK|OKCANCEL|RETRYCANCEL|RIGHT|RTLREADING|SETFOREGROUND|TOPMOST|USERICON|YESNO)|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SYSTEM|TEMPORARY|admin|all|auto|both|colored|false|force|hide|highest|lastused|leave|listonly|none|normal|notset|off|on|open|print|show|silent|silentlog|smooth|textonly|true|user)\\b/,\n    constant: /\\$\\{[!\\w\\.:\\^-]+\\}|\\$\\([!\\w\\.:\\^-]+\\)/,\n    variable: /\\$\\w[\\w\\.]*/,\n    number: /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n    operator: /--?|\\+\\+?|<=?|>=?|==?=?|&&?|\\|\\|?|[?*\\/~^%]/,\n    punctuation: /[{}[\\];(),.:]/,\n    important: {\n      pattern:\n        /(^[\\t ]*)!(?:addincludedir|addplugindir|appendfile|cd|define|delfile|echo|else|endif|error|execute|finalize|getdllversion|gettlbversion|if|ifdef|ifmacrodef|ifmacrondef|ifndef|include|insertmacro|macro|macroend|makensis|packhdr|pragma|searchparse|searchreplace|system|tempfile|undef|verbose|warning)\\b/im,\n      lookbehind: true\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL25zaXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRDtBQUNoRDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsbUJBQW1CLGNBQWM7QUFDakM7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVmcmFjdG9yQDMuNi4wXFxub2RlX21vZHVsZXNcXHJlZnJhY3RvclxcbGFuZ1xcbnNpcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBuc2lzXG5uc2lzLmRpc3BsYXlOYW1lID0gJ25zaXMnXG5uc2lzLmFsaWFzZXMgPSBbXVxuZnVuY3Rpb24gbnNpcyhQcmlzbSkge1xuICAvKipcbiAgICogT3JpZ2luYWwgYnkgSmFuIFQuIFNvdHQgKGh0dHA6Ly9naXRodWIuY29tL2lkbGViZXJnKVxuICAgKlxuICAgKiBJbmNsdWRlcyBhbGwgY29tbWFuZHMgYW5kIHBsdWctaW5zIHNoaXBwZWQgd2l0aCBOU0lTIDMuMDhcbiAgICovXG4gIFByaXNtLmxhbmd1YWdlcy5uc2lzID0ge1xuICAgIGNvbW1lbnQ6IHtcbiAgICAgIHBhdHRlcm46IC8oXnxbXlxcXFxdKSg/OlxcL1xcKltcXHNcXFNdKj9cXCpcXC98WyM7XS4qKS8sXG4gICAgICBsb29rYmVoaW5kOiB0cnVlLFxuICAgICAgZ3JlZWR5OiB0cnVlXG4gICAgfSxcbiAgICBzdHJpbmc6IHtcbiAgICAgIHBhdHRlcm46IC8oXCJ8JykoPzpcXFxcLnwoPyFcXDEpW15cXFxcXFxyXFxuXSkqXFwxLyxcbiAgICAgIGdyZWVkeTogdHJ1ZVxuICAgIH0sXG4gICAga2V5d29yZDoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgLyheW1xcdCBdKikoPzpBYm9ydHxBZGQoPzpCcmFuZGluZ0ltYWdlfFNpemUpfEFkdlNwbGFzaHxBbGxvdyg/OlJvb3REaXJJbnN0YWxsfFNraXBGaWxlcyl8QXV0b0Nsb3NlV2luZG93fEJHKD86Rm9udHxHcmFkaWVudHxJbWFnZSl8QmFubmVyfEJyYW5kaW5nVGV4dHxCcmluZ1RvRnJvbnR8Q1JDQ2hlY2t8Q2FsbCg/Okluc3RETEwpP3xDYXB0aW9ufENoYW5nZVVJfENoZWNrQml0bWFwfENsZWFyRXJyb3JzfENvbXBsZXRlZFRleHR8Q29tcG9uZW50VGV4dHxDb3B5RmlsZXN8Q3JlYXRlKD86RGlyZWN0b3J5fEZvbnR8U2hvcnRDdXQpfERlbGV0ZSg/OklOSVNlY3xJTklTdHJ8UmVnS2V5fFJlZ1ZhbHVlKT98RGV0YWlsKD86UHJpbnR8c0J1dHRvblRleHQpfERpYWxlcnxEaXIoPzpUZXh0fFZhcnxWZXJpZnkpfEVuYWJsZVdpbmRvd3xFbnVtKD86UmVnS2V5fFJlZ1ZhbHVlKXxFeGNofEV4ZWMoPzpTaGVsbCg/OldhaXQpP3xXYWl0KT98RXhwYW5kRW52U3RyaW5nc3xGaWxlKD86QnVmU2l6ZXxDbG9zZXxFcnJvclRleHR8T3BlbnxSZWFkfFJlYWRCeXRlfFJlYWRVVEYxNkxFfFJlYWRXb3JkfFNlZWt8V3JpdGV8V3JpdGVCeXRlfFdyaXRlVVRGMTZMRXxXcml0ZVdvcmQpP3xGaW5kKD86Q2xvc2V8Rmlyc3R8TmV4dHxXaW5kb3cpfEZsdXNoSU5JfEdldCg/OkN1ckluc3RUeXBlfEN1cnJlbnRBZGRyZXNzfERMTFZlcnNpb24oPzpMb2NhbCk/fERsZ0l0ZW18RXJyb3JMZXZlbHxGaWxlVGltZSg/OkxvY2FsKT98RnVsbFBhdGhOYW1lfEZ1bmN0aW9uKD86QWRkcmVzc3xFbmQpP3xJbnN0RGlyRXJyb3J8TGFiZWxBZGRyZXNzfFRlbXBGaWxlTmFtZSl8R290b3xIaWRlV2luZG93fEljb258SWYoPzpBYm9ydHxFcnJvcnN8RmlsZUV4aXN0c3xSZWJvb3RGbGFnfFNpbGVudCl8SW5pdFBsdWdpbnNEaXJ8SW5zdFByb2dyZXNzRmxhZ3N8SW5zdCg/OlR5cGUoPzpHZXRUZXh0fFNldFRleHQpPyl8SW5zdGFsbCg/OkJ1dHRvblRleHR8Q29sb3JzfERpcig/OlJlZ0tleSk/KXxJbnQoPzo2NHxQdHIpP0NtcFU/fEludCg/OjY0KT9GbXR8SW50KD86UHRyKT9PcHxJc1dpbmRvd3xMYW5nKD86RExMfFN0cmluZyl8TGljZW5zZSg/OkJrQ29sb3J8RGF0YXxGb3JjZVNlbGVjdGlvbnxMYW5nU3RyaW5nfFRleHQpfExvYWRMYW5ndWFnZUZpbGV8TG9ja1dpbmRvd3xMb2coPzpTZXR8VGV4dCl8TWFuaWZlc3QoPzpEUElBd2FyZXxTdXBwb3J0ZWRPUyl8TWF0aHxNZXNzYWdlQm94fE1pc2NCdXR0b25UZXh0fE5TSVNkbHxOYW1lfE5vcHxPdXRGaWxlfFBFKD86RGxsQ2hhcmFjdGVyaXN0aWNzfFN1YnN5c1Zlcil8UGFnZSg/OkNhbGxiYWNrcyk/fFBvcHxQdXNofFF1aXR8Uk1EaXJ8UmVhZCg/OkVudlN0cnxJTklTdHJ8UmVnRFdPUkR8UmVnU3RyKXxSZWJvb3R8UmVnRExMfFJlbmFtZXxSZXF1ZXN0RXhlY3V0aW9uTGV2ZWx8UmVzZXJ2ZUZpbGV8UmV0dXJufFNlYXJjaFBhdGh8U2VjdGlvbig/OkVuZHxHZXRGbGFnc3xHZXRJbnN0VHlwZXN8R2V0U2l6ZXxHZXRUZXh0fEdyb3VwfElufFNldEZsYWdzfFNldEluc3RUeXBlc3xTZXRTaXplfFNldFRleHQpP3xTZW5kTWVzc2FnZXxTZXQoPzpBdXRvQ2xvc2V8QnJhbmRpbmdJbWFnZXxDb21wcmVzc3xDb21wcmVzc29yKD86RGljdFNpemUpP3xDdGxDb2xvcnN8Q3VySW5zdFR5cGV8RGF0YWJsb2NrT3B0aW1pemV8RGF0ZVNhdmV8RGV0YWlscyg/OlByaW50fFZpZXcpfEVycm9yTGV2ZWx8RXJyb3JzfEZpbGVBdHRyaWJ1dGVzfEZvbnR8T3V0UGF0aHxPdmVyd3JpdGV8UGx1Z2luVW5sb2FkfFJlYm9vdEZsYWd8UmVnVmlld3xTaGVsbFZhckNvbnRleHR8U2lsZW50KXxTaG93KD86SW5zdERldGFpbHN8VW5pbnN0RGV0YWlsc3xXaW5kb3cpfFNpbGVudCg/Okluc3RhbGx8VW5JbnN0YWxsKXxTbGVlcHxTcGFjZVRleHRzfFNwbGFzaHxTdGFydE1lbnV8U3RyKD86Q21wUz98Q3B5fExlbil8U3ViQ2FwdGlvbnxTeXN0ZW18VW5SZWdETEx8VW5pY29kZXxVbmluc3RQYWdlfFVuaW5zdGFsbCg/OkJ1dHRvblRleHR8Q2FwdGlvbnxJY29ufFN1YkNhcHRpb258VGV4dCl8VXNlckluZm98VkkoPzpBZGRWZXJzaW9uS2V5fEZpbGVWZXJzaW9ufFByb2R1Y3RWZXJzaW9uKXxWUGF0Y2h8VmFyfFdpbmRvd0ljb258V3JpdGUoPzpJTklTdHJ8UmVnKD86QmlufERXT1JEfEV4cGFuZFN0cnxNdWx0aVN0cnxOb25lfFN0cil8VW5pbnN0YWxsZXIpfFhQU3R5bGV8bnMoPzpEaWFsb2dzfEV4ZWMpKVxcYi9tLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgIH0sXG4gICAgcHJvcGVydHk6XG4gICAgICAvXFxiKD86QVJDSElWRXxGSUxFXyg/OkFUVFJJQlVURV9BUkNISVZFfEFUVFJJQlVURV9OT1JNQUx8QVRUUklCVVRFX09GRkxJTkV8QVRUUklCVVRFX1JFQURPTkxZfEFUVFJJQlVURV9TWVNURU18QVRUUklCVVRFX1RFTVBPUkFSWSl8SEsoPzooPzpDUnxDVXxMTSkoPzozMnw2NCk/fEREfFBEfFUpfEhLRVlfKD86Q0xBU1NFU19ST09UfENVUlJFTlRfQ09ORklHfENVUlJFTlRfVVNFUnxEWU5fREFUQXxMT0NBTF9NQUNISU5FfFBFUkZPUk1BTkNFX0RBVEF8VVNFUlMpfElEKD86QUJPUlR8Q0FOQ0VMfElHTk9SRXxOT3xPS3xSRVRSWXxZRVMpfE1CXyg/OkFCT1JUUkVUUllJR05PUkV8REVGQlVUVE9OMXxERUZCVVRUT04yfERFRkJVVFRPTjN8REVGQlVUVE9ONHxJQ09ORVhDTEFNQVRJT058SUNPTklORk9STUFUSU9OfElDT05RVUVTVElPTnxJQ09OU1RPUHxPS3xPS0NBTkNFTHxSRVRSWUNBTkNFTHxSSUdIVHxSVExSRUFESU5HfFNFVEZPUkVHUk9VTkR8VE9QTU9TVHxVU0VSSUNPTnxZRVNOTyl8Tk9STUFMfE9GRkxJTkV8UkVBRE9OTFl8U0hDVFh8U0hFTExfQ09OVEVYVHxTWVNURU18VEVNUE9SQVJZfGFkbWlufGFsbHxhdXRvfGJvdGh8Y29sb3JlZHxmYWxzZXxmb3JjZXxoaWRlfGhpZ2hlc3R8bGFzdHVzZWR8bGVhdmV8bGlzdG9ubHl8bm9uZXxub3JtYWx8bm90c2V0fG9mZnxvbnxvcGVufHByaW50fHNob3d8c2lsZW50fHNpbGVudGxvZ3xzbW9vdGh8dGV4dG9ubHl8dHJ1ZXx1c2VyKVxcYi8sXG4gICAgY29uc3RhbnQ6IC9cXCRcXHtbIVxcd1xcLjpcXF4tXStcXH18XFwkXFwoWyFcXHdcXC46XFxeLV0rXFwpLyxcbiAgICB2YXJpYWJsZTogL1xcJFxcd1tcXHdcXC5dKi8sXG4gICAgbnVtYmVyOiAvXFxiMHhbXFxkQS1GYS1mXStcXGJ8KD86XFxiXFxkKyg/OlxcLlxcZCopP3xcXEJcXC5cXGQrKSg/OltFZV0tP1xcZCspPy8sXG4gICAgb3BlcmF0b3I6IC8tLT98XFwrXFwrP3w8PT98Pj0/fD09Pz0/fCYmP3xcXHxcXHw/fFs/KlxcL35eJV0vLFxuICAgIHB1bmN0dWF0aW9uOiAvW3t9W1xcXTsoKSwuOl0vLFxuICAgIGltcG9ydGFudDoge1xuICAgICAgcGF0dGVybjpcbiAgICAgICAgLyheW1xcdCBdKikhKD86YWRkaW5jbHVkZWRpcnxhZGRwbHVnaW5kaXJ8YXBwZW5kZmlsZXxjZHxkZWZpbmV8ZGVsZmlsZXxlY2hvfGVsc2V8ZW5kaWZ8ZXJyb3J8ZXhlY3V0ZXxmaW5hbGl6ZXxnZXRkbGx2ZXJzaW9ufGdldHRsYnZlcnNpb258aWZ8aWZkZWZ8aWZtYWNyb2RlZnxpZm1hY3JvbmRlZnxpZm5kZWZ8aW5jbHVkZXxpbnNlcnRtYWNyb3xtYWNyb3xtYWNyb2VuZHxtYWtlbnNpc3xwYWNraGRyfHByYWdtYXxzZWFyY2hwYXJzZXxzZWFyY2hyZXBsYWNlfHN5c3RlbXx0ZW1wZmlsZXx1bmRlZnx2ZXJib3NlfHdhcm5pbmcpXFxiL2ltLFxuICAgICAgbG9va2JlaGluZDogdHJ1ZVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/nsis.js\n"));

/***/ })

}]);