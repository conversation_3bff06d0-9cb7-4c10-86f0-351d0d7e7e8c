"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_gdscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gdscript.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gdscript.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = gdscript\ngdscript.displayName = 'gdscript'\ngdscript.aliases = []\nfunction gdscript(Prism) {\n  Prism.languages.gdscript = {\n    comment: /#.*/,\n    string: {\n      pattern:\n        /@?(?:(\"|')(?:(?!\\1)[^\\n\\\\]|\\\\[\\s\\S])*\\1(?!\"|')|\"\"\"(?:[^\\\\]|\\\\[\\s\\S])*?\"\"\")/,\n      greedy: true\n    },\n    'class-name': {\n      // class_name Foo, extends Bar, class InnerClass\n      // export(int) var baz, export(int, 0) var i\n      // as Node\n      // const FOO: int = 9, var bar: bool = true\n      // func add(reference: Item, amount: int) -> Item:\n      pattern:\n        /(^(?:class|class_name|extends)[ \\t]+|^export\\([ \\t]*|\\bas[ \\t]+|(?:\\b(?:const|var)[ \\t]|[,(])[ \\t]*\\w+[ \\t]*:[ \\t]*|->[ \\t]*)[a-zA-Z_]\\w*/m,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:and|as|assert|break|breakpoint|class|class_name|const|continue|elif|else|enum|export|extends|for|func|if|in|is|master|mastersync|match|not|null|onready|or|pass|preload|puppet|puppetsync|remote|remotesync|return|self|setget|signal|static|tool|var|while|yield)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*\\()/i,\n    variable: /\\$\\w+/,\n    number: [\n      /\\b0b[01_]+\\b|\\b0x[\\da-fA-F_]+\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.[\\d_]+)(?:e[+-]?[\\d_]+)?\\b/,\n      /\\b(?:INF|NAN|PI|TAU)\\b/\n    ],\n    constant: /\\b[A-Z][A-Z_\\d]*\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /->|:=|&&|\\|\\||<<|>>|[-+*/%&|!<>=]=?|[~^]/,\n    punctuation: /[.:,;()[\\]{}]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/gdscript.js\n"));

/***/ })

}]);