"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_splunkSpl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/splunk-spl.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/splunk-spl.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = splunkSpl\nsplunkSpl.displayName = 'splunkSpl'\nsplunkSpl.aliases = []\nfunction splunkSpl(Prism) {\n  Prism.languages['splunk-spl'] = {\n    comment: /`comment\\(\"(?:\\\\.|[^\\\\\"])*\"\\)`/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"])*\"/,\n      greedy: true\n    },\n    // https://docs.splunk.com/Documentation/Splunk/7.3.0/SearchReference/ListOfSearchCommands\n    keyword:\n      /\\b(?:abstract|accum|addcoltotals|addinfo|addtotals|analyzefields|anomalies|anomalousvalue|anomalydetection|append|appendcols|appendcsv|appendlookup|appendpipe|arules|associate|audit|autoregress|bin|bucket|bucketdir|chart|cluster|cofilter|collect|concurrency|contingency|convert|correlate|datamodel|dbinspect|dedup|delete|delta|diff|erex|eval|eventcount|eventstats|extract|fieldformat|fields|fieldsummary|filldown|fillnull|findtypes|folderize|foreach|format|from|gauge|gentimes|geom|geomfilter|geostats|head|highlight|history|iconify|input|inputcsv|inputlookup|iplocation|join|kmeans|kv|kvform|loadjob|localize|localop|lookup|makecontinuous|makemv|makeresults|map|mcollect|metadata|metasearch|meventcollect|mstats|multikv|multisearch|mvcombine|mvexpand|nomv|outlier|outputcsv|outputlookup|outputtext|overlap|pivot|predict|rangemap|rare|regex|relevancy|reltime|rename|replace|rest|return|reverse|rex|rtorder|run|savedsearch|script|scrub|search|searchtxn|selfjoin|sendemail|set|setfields|sichart|sirare|sistats|sitimechart|sitop|sort|spath|stats|strcat|streamstats|table|tags|tail|timechart|timewrap|top|transaction|transpose|trendline|tscollect|tstats|typeahead|typelearner|typer|union|uniq|untable|where|x11|xmlkv|xmlunescape|xpath|xyseries)\\b/i,\n    'operator-word': {\n      pattern: /\\b(?:and|as|by|not|or|xor)\\b/i,\n      alias: 'operator'\n    },\n    function: /\\b\\w+(?=\\s*\\()/,\n    property: /\\b\\w+(?=\\s*=(?!=))/,\n    date: {\n      // MM/DD/YYYY(:HH:MM:SS)?\n      pattern: /\\b\\d{1,2}\\/\\d{1,2}\\/\\d{1,4}(?:(?::\\d{1,2}){3})?\\b/,\n      alias: 'number'\n    },\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    boolean: /\\b(?:f|false|t|true)\\b/i,\n    operator: /[<>=]=?|[-+*/%|]/,\n    punctuation: /[()[\\],]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/splunk-spl.js\n"));

/***/ })

}]);