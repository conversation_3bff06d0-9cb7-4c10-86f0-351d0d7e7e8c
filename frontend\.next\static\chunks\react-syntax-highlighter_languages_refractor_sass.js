"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_sass"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sass.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sass.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = sass\nsass.displayName = 'sass'\nsass.aliases = []\nfunction sass(Prism) {\n  ;(function (Prism) {\n    Prism.languages.sass = Prism.languages.extend('css', {\n      // Sass comments don't need to be closed, only indented\n      comment: {\n        pattern: /^([ \\t]*)\\/[\\/*].*(?:(?:\\r?\\n|\\r)\\1[ \\t].+)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('sass', 'atrule', {\n      // We want to consume the whole line\n      'atrule-line': {\n        // Includes support for = and + shortcuts\n        pattern: /^(?:[ \\t]*)[@+=].+/m,\n        greedy: true,\n        inside: {\n          atrule: /(?:@[\\w-]+|[+=])/\n        }\n      }\n    })\n    delete Prism.languages.sass.atrule\n    var variable = /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n    var operator = [\n      /[+*\\/%]|[=!]=|<=?|>=?|\\b(?:and|not|or)\\b/,\n      {\n        pattern: /(\\s)-(?=\\s)/,\n        lookbehind: true\n      }\n    ]\n    Prism.languages.insertBefore('sass', 'property', {\n      // We want to consume the whole line\n      'variable-line': {\n        pattern: /^[ \\t]*\\$.+/m,\n        greedy: true,\n        inside: {\n          punctuation: /:/,\n          variable: variable,\n          operator: operator\n        }\n      },\n      // We want to consume the whole line\n      'property-line': {\n        pattern: /^[ \\t]*(?:[^:\\s]+ *:.*|:[^:\\s].*)/m,\n        greedy: true,\n        inside: {\n          property: [\n            /[^:\\s]+(?=\\s*:)/,\n            {\n              pattern: /(:)[^:\\s]+/,\n              lookbehind: true\n            }\n          ],\n          punctuation: /:/,\n          variable: variable,\n          operator: operator,\n          important: Prism.languages.sass.important\n        }\n      }\n    })\n    delete Prism.languages.sass.property\n    delete Prism.languages.sass.important // Now that whole lines for other patterns are consumed,\n    // what's left should be selectors\n    Prism.languages.insertBefore('sass', 'punctuation', {\n      selector: {\n        pattern:\n          /^([ \\t]*)\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*(?:,(?:\\r?\\n|\\r)\\1[ \\t]+\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/sass.js\n"));

/***/ })

}]);