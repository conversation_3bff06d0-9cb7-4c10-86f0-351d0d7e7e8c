"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dart"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dart.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dart.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dart\ndart.displayName = 'dart'\ndart.aliases = []\nfunction dart(Prism) {\n  ;(function (Prism) {\n    var keywords = [\n      /\\b(?:async|sync|yield)\\*/,\n      /\\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\\b/\n    ] // Handles named imports, such as http.Client\n    var packagePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the dart naming conventions\n    var className = {\n      pattern: RegExp(packagePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        }\n      }\n    }\n    Prism.languages.dart = Prism.languages.extend('clike', {\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            packagePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      operator:\n        /\\bis!|\\b(?:as|is)\\b|\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?/\n    })\n    Prism.languages.insertBefore('dart', 'string', {\n      'string-literal': {\n        pattern:\n          /r?(?:(\"\"\"|''')[\\s\\S]*?\\1|([\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2(?!\\2))/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\$\\{?|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                inside: Prism.languages.dart\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      },\n      string: undefined\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      metadata: {\n        pattern: /@\\w+/,\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<[\\w\\s,.&?]*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      }\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dart.js\n"));

/***/ })

}]);