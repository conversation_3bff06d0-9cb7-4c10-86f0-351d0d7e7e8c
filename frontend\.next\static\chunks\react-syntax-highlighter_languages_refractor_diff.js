"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_diff"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/diff.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/diff.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = diff\ndiff.displayName = 'diff'\ndiff.aliases = []\nfunction diff(Prism) {\n  ;(function (Prism) {\n    Prism.languages.diff = {\n      coord: [\n        // Match all kinds of coord lines (prefixed by \"+++\", \"---\" or \"***\").\n        /^(?:\\*{3}|-{3}|\\+{3}).*$/m, // Match \"@@ ... @@\" coord lines in unified diff.\n        /^@@.*@@$/m, // Match coord lines in normal diff (starts with a number).\n        /^\\d.*$/m\n      ] // deleted, inserted, unchanged, diff\n    }\n    /**\n     * A map from the name of a block to its line prefix.\n     *\n     * @type {Object<string, string>}\n     */\n    var PREFIXES = {\n      'deleted-sign': '-',\n      'deleted-arrow': '<',\n      'inserted-sign': '+',\n      'inserted-arrow': '>',\n      unchanged: ' ',\n      diff: '!'\n    } // add a token for each prefix\n    Object.keys(PREFIXES).forEach(function (name) {\n      var prefix = PREFIXES[name]\n      var alias = []\n      if (!/^\\w+$/.test(name)) {\n        // \"deleted-sign\" -> \"deleted\"\n        alias.push(/\\w+/.exec(name)[0])\n      }\n      if (name === 'diff') {\n        alias.push('bold')\n      }\n      Prism.languages.diff[name] = {\n        pattern: RegExp(\n          '^(?:[' + prefix + '].*(?:\\r\\n?|\\n|(?![\\\\s\\\\S])))+',\n          'm'\n        ),\n        alias: alias,\n        inside: {\n          line: {\n            pattern: /(.)(?=[\\s\\S]).*(?:\\r\\n?|\\n)?/,\n            lookbehind: true\n          },\n          prefix: {\n            pattern: /[\\s\\S]/,\n            alias: /\\w+/.exec(name)[0]\n          }\n        }\n      }\n    }) // make prefixes available to Diff plugin\n    Object.defineProperty(Prism.languages.diff, 'PREFIXES', {\n      value: PREFIXES\n    })\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/diff.js\n"));

/***/ })

}]);