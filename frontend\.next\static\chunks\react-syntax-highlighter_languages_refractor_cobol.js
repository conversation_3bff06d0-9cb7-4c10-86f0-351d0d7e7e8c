"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_cobol"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cobol.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cobol.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = cobol\ncobol.displayName = 'cobol'\ncobol.aliases = []\nfunction cobol(Prism) {\n  Prism.languages.cobol = {\n    comment: {\n      pattern: /\\*>.*|(^[ \\t]*)\\*.*/m,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /[xzgn]?(?:\"(?:[^\\r\\n\"]|\"\")*\"(?!\")|'(?:[^\\r\\n']|'')*'(?!'))/i,\n      greedy: true\n    },\n    level: {\n      pattern: /(^[ \\t]*)\\d+\\b/m,\n      lookbehind: true,\n      greedy: true,\n      alias: 'number'\n    },\n    'class-name': {\n      // https://github.com/antlr/grammars-v4/blob/42edd5b687d183b5fa679e858a82297bd27141e7/cobol85/Cobol85.g4#L1015\n      pattern:\n        /(\\bpic(?:ture)?\\s+)(?:(?:[-\\w$/,:*+<>]|\\.(?!\\s|$))(?:\\(\\d+\\))?)+/i,\n      lookbehind: true,\n      inside: {\n        number: {\n          pattern: /(\\()\\d+/,\n          lookbehind: true\n        },\n        punctuation: /[()]/\n      }\n    },\n    keyword: {\n      pattern:\n        /(^|[^\\w-])(?:ABORT|ACCEPT|ACCESS|ADD|ADDRESS|ADVANCING|AFTER|ALIGNED|ALL|ALPHABET|ALPHABETIC|ALPHABETIC-LOWER|ALPHABETIC-UPPER|ALPHANUMERIC|ALPHANUMERIC-EDITED|ALSO|ALTER|ALTERNATE|ANY|ARE|AREA|AREAS|AS|ASCENDING|ASCII|ASSIGN|ASSOCIATED-DATA|ASSOCIATED-DATA-LENGTH|AT|ATTRIBUTE|AUTHOR|AUTO|AUTO-SKIP|BACKGROUND-COLOR|BACKGROUND-COLOUR|BASIS|BEEP|BEFORE|BEGINNING|BELL|BINARY|BIT|BLANK|BLINK|BLOCK|BOTTOM|BOUNDS|BY|BYFUNCTION|BYTITLE|CALL|CANCEL|CAPABLE|CCSVERSION|CD|CF|CH|CHAINING|CHANGED|CHANNEL|CHARACTER|CHARACTERS|CLASS|CLASS-ID|CLOCK-UNITS|CLOSE|CLOSE-DISPOSITION|COBOL|CODE|CODE-SET|COL|COLLATING|COLUMN|COM-REG|COMMA|COMMITMENT|COMMON|COMMUNICATION|COMP|COMP-1|COMP-2|COMP-3|COMP-4|COMP-5|COMPUTATIONAL|COMPUTATIONAL-1|COMPUTATIONAL-2|COMPUTATIONAL-3|COMPUTATIONAL-4|COMPUTATIONAL-5|COMPUTE|CONFIGURATION|CONTAINS|CONTENT|CONTINUE|CONTROL|CONTROL-POINT|CONTROLS|CONVENTION|CONVERTING|COPY|CORR|CORRESPONDING|COUNT|CRUNCH|CURRENCY|CURSOR|DATA|DATA-BASE|DATE|DATE-COMPILED|DATE-WRITTEN|DAY|DAY-OF-WEEK|DBCS|DE|DEBUG-CONTENTS|DEBUG-ITEM|DEBUG-LINE|DEBUG-NAME|DEBUG-SUB-1|DEBUG-SUB-2|DEBUG-SUB-3|DEBUGGING|DECIMAL-POINT|DECLARATIVES|DEFAULT|DEFAULT-DISPLAY|DEFINITION|DELETE|DELIMITED|DELIMITER|DEPENDING|DESCENDING|DESTINATION|DETAIL|DFHRESP|DFHVALUE|DISABLE|DISK|DISPLAY|DISPLAY-1|DIVIDE|DIVISION|DONTCARE|DOUBLE|DOWN|DUPLICATES|DYNAMIC|EBCDIC|EGCS|EGI|ELSE|EMI|EMPTY-CHECK|ENABLE|END|END-ACCEPT|END-ADD|END-CALL|END-COMPUTE|END-DELETE|END-DIVIDE|END-EVALUATE|END-IF|END-MULTIPLY|END-OF-PAGE|END-PERFORM|END-READ|END-RECEIVE|END-RETURN|END-REWRITE|END-SEARCH|END-START|END-STRING|END-SUBTRACT|END-UNSTRING|END-WRITE|ENDING|ENTER|ENTRY|ENTRY-PROCEDURE|ENVIRONMENT|EOL|EOP|EOS|ERASE|ERROR|ESCAPE|ESI|EVALUATE|EVENT|EVERY|EXCEPTION|EXCLUSIVE|EXHIBIT|EXIT|EXPORT|EXTEND|EXTENDED|EXTERNAL|FD|FILE|FILE-CONTROL|FILLER|FINAL|FIRST|FOOTING|FOR|FOREGROUND-COLOR|FOREGROUND-COLOUR|FROM|FULL|FUNCTION|FUNCTION-POINTER|FUNCTIONNAME|GENERATE|GIVING|GLOBAL|GO|GOBACK|GRID|GROUP|HEADING|HIGH-VALUE|HIGH-VALUES|HIGHLIGHT|I-O|I-O-CONTROL|ID|IDENTIFICATION|IF|IMPLICIT|IMPORT|IN|INDEX|INDEXED|INDICATE|INITIAL|INITIALIZE|INITIATE|INPUT|INPUT-OUTPUT|INSPECT|INSTALLATION|INTEGER|INTO|INVALID|INVOKE|IS|JUST|JUSTIFIED|KANJI|KEPT|KEY|KEYBOARD|LABEL|LANGUAGE|LAST|LB|LD|LEADING|LEFT|LEFTLINE|LENGTH|LENGTH-CHECK|LIBACCESS|LIBPARAMETER|LIBRARY|LIMIT|LIMITS|LINAGE|LINAGE-COUNTER|LINE|LINE-COUNTER|LINES|LINKAGE|LIST|LOCAL|LOCAL-STORAGE|LOCK|LONG-DATE|LONG-TIME|LOW-VALUE|LOW-VALUES|LOWER|LOWLIGHT|MEMORY|MERGE|MESSAGE|MMDDYYYY|MODE|MODULES|MORE-LABELS|MOVE|MULTIPLE|MULTIPLY|NAMED|NATIONAL|NATIONAL-EDITED|NATIVE|NEGATIVE|NETWORK|NEXT|NO|NO-ECHO|NULL|NULLS|NUMBER|NUMERIC|NUMERIC-DATE|NUMERIC-EDITED|NUMERIC-TIME|OBJECT-COMPUTER|OCCURS|ODT|OF|OFF|OMITTED|ON|OPEN|OPTIONAL|ORDER|ORDERLY|ORGANIZATION|OTHER|OUTPUT|OVERFLOW|OVERLINE|OWN|PACKED-DECIMAL|PADDING|PAGE|PAGE-COUNTER|PASSWORD|PERFORM|PF|PH|PIC|PICTURE|PLUS|POINTER|PORT|POSITION|POSITIVE|PRINTER|PRINTING|PRIVATE|PROCEDURE|PROCEDURE-POINTER|PROCEDURES|PROCEED|PROCESS|PROGRAM|PROGRAM-ID|PROGRAM-LIBRARY|PROMPT|PURGE|QUEUE|QUOTE|QUOTES|RANDOM|RD|READ|READER|REAL|RECEIVE|RECEIVED|RECORD|RECORDING|RECORDS|RECURSIVE|REDEFINES|REEL|REF|REFERENCE|REFERENCES|RELATIVE|RELEASE|REMAINDER|REMARKS|REMOTE|REMOVAL|REMOVE|RENAMES|REPLACE|REPLACING|REPORT|REPORTING|REPORTS|REQUIRED|RERUN|RESERVE|RESET|RETURN|RETURN-CODE|RETURNING|REVERSE-VIDEO|REVERSED|REWIND|REWRITE|RF|RH|RIGHT|ROUNDED|RUN|SAME|SAVE|SCREEN|SD|SEARCH|SECTION|SECURE|SECURITY|SEGMENT|SEGMENT-LIMIT|SELECT|SEND|SENTENCE|SEPARATE|SEQUENCE|SEQUENTIAL|SET|SHARED|SHAREDBYALL|SHAREDBYRUNUNIT|SHARING|SHIFT-IN|SHIFT-OUT|SHORT-DATE|SIGN|SIZE|SORT|SORT-CONTROL|SORT-CORE-SIZE|SORT-FILE-SIZE|SORT-MERGE|SORT-MESSAGE|SORT-MODE-SIZE|SORT-RETURN|SOURCE|SOURCE-COMPUTER|SPACE|SPACES|SPECIAL-NAMES|STANDARD|STANDARD-1|STANDARD-2|START|STATUS|STOP|STRING|SUB-QUEUE-1|SUB-QUEUE-2|SUB-QUEUE-3|SUBTRACT|SUM|SUPPRESS|SYMBOL|SYMBOLIC|SYNC|SYNCHRONIZED|TABLE|TALLY|TALLYING|TAPE|TASK|TERMINAL|TERMINATE|TEST|TEXT|THEN|THREAD|THREAD-LOCAL|THROUGH|THRU|TIME|TIMER|TIMES|TITLE|TO|TODAYS-DATE|TODAYS-NAME|TOP|TRAILING|TRUNCATED|TYPE|TYPEDEF|UNDERLINE|UNIT|UNSTRING|UNTIL|UP|UPON|USAGE|USE|USING|VALUE|VALUES|VARYING|VIRTUAL|WAIT|WHEN|WHEN-COMPILED|WITH|WORDS|WORKING-STORAGE|WRITE|YEAR|YYYYDDD|YYYYMMDD|ZERO-FILL|ZEROES|ZEROS)(?![\\w-])/i,\n      lookbehind: true\n    },\n    boolean: {\n      pattern: /(^|[^\\w-])(?:false|true)(?![\\w-])/i,\n      lookbehind: true\n    },\n    number: {\n      pattern:\n        /(^|[^\\w-])(?:[+-]?(?:(?:\\d+(?:[.,]\\d+)?|[.,]\\d+)(?:e[+-]?\\d+)?|zero))(?![\\w-])/i,\n      lookbehind: true\n    },\n    operator: [\n      /<>|[<>]=?|[=+*/&]/,\n      {\n        pattern: /(^|[^\\w-])(?:-|and|equal|greater|less|not|or|than)(?![\\w-])/i,\n        lookbehind: true\n      }\n    ],\n    punctuation: /[.:,()]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cobol.js\n"));

/***/ })

}]);