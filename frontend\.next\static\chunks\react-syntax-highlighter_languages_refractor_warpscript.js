"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_warpscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/warpscript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/warpscript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = warpscript\nwarpscript.displayName = 'warpscript'\nwarpscript.aliases = []\nfunction warpscript(Prism) {\n  Prism.languages.warpscript = {\n    comment: /#.*|\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern:\n        /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'|<'(?:[^\\\\']|'(?!>)|\\\\.)*'>/,\n      greedy: true\n    },\n    variable: /\\$\\S+/,\n    macro: {\n      pattern: /@\\S+/,\n      alias: 'property'\n    },\n    // WarpScript doesn't have any keywords, these are all functions under the control category\n    // https://www.warp10.io/tags/control\n    keyword:\n      /\\b(?:BREAK|CHECKMACRO|CONTINUE|CUDF|DEFINED|DEFINEDMACRO|EVAL|FAIL|FOR|FOREACH|FORSTEP|IFT|IFTE|MSGFAIL|NRETURN|RETHROW|RETURN|SWITCH|TRY|UDF|UNTIL|WHILE)\\b/,\n    number:\n      /[+-]?\\b(?:NaN|Infinity|\\d+(?:\\.\\d*)?(?:[Ee][+-]?\\d+)?|0x[\\da-fA-F]+|0b[01]+)\\b/,\n    boolean: /\\b(?:F|T|false|true)\\b/,\n    punctuation: /<%|%>|[{}[\\]()]/,\n    // Some operators from the \"operators\" category\n    // https://www.warp10.io/tags/operators\n    operator:\n      /==|&&?|\\|\\|?|\\*\\*?|>>>?|<<|[<>!~]=?|[-/%^]|\\+!?|\\b(?:AND|NOT|OR)\\b/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/warpscript.js\n"));

/***/ })

}]);