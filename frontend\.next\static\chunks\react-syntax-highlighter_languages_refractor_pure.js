"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_pure"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pure.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pure.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = pure\npure.displayName = 'pure'\npure.aliases = []\nfunction pure(Prism) {\n  ;(function (Prism) {\n    // https://agraef.github.io/pure-docs/pure.html#lexical-matters\n    Prism.languages.pure = {\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n          lookbehind: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true\n        },\n        /#!.+/\n      ],\n      'inline-lang': {\n        pattern: /%<[\\s\\S]+?%>/,\n        greedy: true,\n        inside: {\n          lang: {\n            pattern: /(^%< *)-\\*-.+?-\\*-/,\n            lookbehind: true,\n            alias: 'comment'\n          },\n          delimiter: {\n            pattern: /^%<.*|%>$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        greedy: true\n      },\n      number: {\n        // The look-behind prevents wrong highlighting of the .. operator\n        pattern:\n          /((?:\\.\\.)?)(?:\\b(?:inf|nan)\\b|\\b0x[\\da-f]+|(?:\\b(?:0b)?\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?L?)/i,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:NULL|ans|break|bt|case|catch|cd|clear|const|def|del|dump|else|end|exit|extern|false|force|help|if|infix[lr]?|interface|let|ls|mem|namespace|nonfix|of|otherwise|outfix|override|postfix|prefix|private|public|pwd|quit|run|save|show|stats|then|throw|trace|true|type|underride|using|when|with)\\b/,\n      function:\n        /\\b(?:abs|add_(?:addr|constdef|(?:fundef|interface|macdef|typedef)(?:_at)?|vardef)|all|any|applp?|arity|bigintp?|blob(?:_crc|_size|p)?|boolp?|byte_c?string(?:_pointer)?|byte_(?:matrix|pointer)|calloc|cat|catmap|ceil|char[ps]?|check_ptrtag|chr|clear_sentry|clearsym|closurep?|cmatrixp?|cols?|colcat(?:map)?|colmap|colrev|colvector(?:p|seq)?|complex(?:_float_(?:matrix|pointer)|_matrix(?:_view)?|_pointer|p)?|conj|cookedp?|cst|cstring(?:_(?:dup|list|vector))?|curry3?|cyclen?|del_(?:constdef|fundef|interface|macdef|typedef|vardef)|delete|diag(?:mat)?|dim|dmatrixp?|do|double(?:_matrix(?:_view)?|_pointer|p)?|dowith3?|drop|dropwhile|eval(?:cmd)?|exactp|filter|fix|fixity|flip|float(?:_matrix|_pointer)|floor|fold[lr]1?|frac|free|funp?|functionp?|gcd|get(?:_(?:byte|constdef|double|float|fundef|int(?:64)?|interface(?:_typedef)?|long|macdef|pointer|ptrtag|sentry|short|string|typedef|vardef))?|globsym|hash|head|id|im|imatrixp?|index|inexactp|infp|init|insert|int(?:_matrix(?:_view)?|_pointer|p)?|int64_(?:matrix|pointer)|integerp?|iteraten?|iterwhile|join|keys?|lambdap?|last(?:err(?:pos)?)?|lcd|list[2p]?|listmap|make_ptrtag|malloc|map|matcat|matrixp?|max|member|min|nanp|nargs|nmatrixp?|null|numberp?|ord|pack(?:ed)?|pointer(?:_cast|_tag|_type|p)?|pow|pred|ptrtag|put(?:_(?:byte|double|float|int(?:64)?|long|pointer|short|string))?|rationalp?|re|realp?|realloc|recordp?|redim|reduce(?:_with)?|refp?|repeatn?|reverse|rlistp?|round|rows?|rowcat(?:map)?|rowmap|rowrev|rowvector(?:p|seq)?|same|scan[lr]1?|sentry|sgn|short_(?:matrix|pointer)|slice|smatrixp?|sort|split|str|strcat|stream|stride|string(?:_(?:dup|list|vector)|p)?|subdiag(?:mat)?|submat|subseq2?|substr|succ|supdiag(?:mat)?|symbolp?|tail|take|takewhile|thunkp?|transpose|trunc|tuplep?|typep|ubyte|uint(?:64)?|ulong|uncurry3?|unref|unzip3?|update|ushort|vals?|varp?|vector(?:p|seq)?|void|zip3?|zipwith3?)\\b/,\n      special: {\n        pattern: /\\b__[a-z]+__\\b/i,\n        alias: 'builtin'\n      },\n      // Any combination of operator chars can be an operator\n      // eslint-disable-next-line no-misleading-character-class\n      operator:\n        /(?:[!\"#$%&'*+,\\-.\\/:<=>?@\\\\^`|~\\u00a1-\\u00bf\\u00d7-\\u00f7\\u20d0-\\u2bff]|\\b_+\\b)+|\\b(?:and|div|mod|not|or)\\b/,\n      // FIXME: How can we prevent | and , to be highlighted as operator when they are used alone?\n      punctuation: /[(){}\\[\\];,|]/\n    }\n    var inlineLanguages = [\n      'c',\n      {\n        lang: 'c++',\n        alias: 'cpp'\n      },\n      'fortran'\n    ]\n    var inlineLanguageRe = /%< *-\\*- *<lang>\\d* *-\\*-[\\s\\S]+?%>/.source\n    inlineLanguages.forEach(function (lang) {\n      var alias = lang\n      if (typeof lang !== 'string') {\n        alias = lang.alias\n        lang = lang.lang\n      }\n      if (Prism.languages[alias]) {\n        var o = {}\n        o['inline-lang-' + alias] = {\n          pattern: RegExp(\n            inlineLanguageRe.replace(\n              '<lang>',\n              lang.replace(/([.+*?\\/\\\\(){}\\[\\]])/g, '\\\\$1')\n            ),\n            'i'\n          ),\n          inside: Prism.util.clone(Prism.languages.pure['inline-lang'].inside)\n        }\n        o['inline-lang-' + alias].inside.rest = Prism.util.clone(\n          Prism.languages[alias]\n        )\n        Prism.languages.insertBefore('pure', 'inline-lang', o)\n      }\n    }) // C is the default inline language\n    if (Prism.languages.c) {\n      Prism.languages.pure['inline-lang'].inside.rest = Prism.util.clone(\n        Prism.languages.c\n      )\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/pure.js\n"));

/***/ })

}]);