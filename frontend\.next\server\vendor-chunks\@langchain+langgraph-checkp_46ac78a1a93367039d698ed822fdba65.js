"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65";
exports.ids = ["vendor-chunks/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/base.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/base.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseCheckpointSaver: () => (/* binding */ BaseCheckpointSaver),\n/* harmony export */   WRITES_IDX_MAP: () => (/* binding */ WRITES_IDX_MAP),\n/* harmony export */   compareChannelVersions: () => (/* binding */ compareChannelVersions),\n/* harmony export */   copyCheckpoint: () => (/* binding */ copyCheckpoint),\n/* harmony export */   deepCopy: () => (/* binding */ deepCopy),\n/* harmony export */   emptyCheckpoint: () => (/* binding */ emptyCheckpoint),\n/* harmony export */   getCheckpointId: () => (/* binding */ getCheckpointId),\n/* harmony export */   maxChannelVersion: () => (/* binding */ maxChannelVersion)\n/* harmony export */ });\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/id.js\");\n/* harmony import */ var _serde_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serde/types.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js\");\n/* harmony import */ var _serde_jsonplus_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./serde/jsonplus.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/jsonplus.js\");\n\n\n\nfunction deepCopy(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n        return obj;\n    }\n    const newObj = Array.isArray(obj) ? [] : {};\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            newObj[key] = deepCopy(obj[key]);\n        }\n    }\n    return newObj;\n}\n/** @hidden */\nfunction emptyCheckpoint() {\n    return {\n        v: 1,\n        id: (0,_id_js__WEBPACK_IMPORTED_MODULE_0__.uuid6)(-2),\n        ts: new Date().toISOString(),\n        channel_values: {},\n        channel_versions: {},\n        versions_seen: {},\n        pending_sends: [],\n    };\n}\n/** @hidden */\nfunction copyCheckpoint(checkpoint) {\n    return {\n        v: checkpoint.v,\n        id: checkpoint.id,\n        ts: checkpoint.ts,\n        channel_values: { ...checkpoint.channel_values },\n        channel_versions: { ...checkpoint.channel_versions },\n        versions_seen: deepCopy(checkpoint.versions_seen),\n        pending_sends: [...checkpoint.pending_sends],\n    };\n}\nclass BaseCheckpointSaver {\n    constructor(serde) {\n        Object.defineProperty(this, \"serde\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new _serde_jsonplus_js__WEBPACK_IMPORTED_MODULE_2__.JsonPlusSerializer()\n        });\n        this.serde = serde || this.serde;\n    }\n    async get(config) {\n        const value = await this.getTuple(config);\n        return value ? value.checkpoint : undefined;\n    }\n    /**\n     * Generate the next version ID for a channel.\n     *\n     * Default is to use integer versions, incrementing by 1. If you override, you can use str/int/float versions,\n     * as long as they are monotonically increasing.\n     */\n    getNextVersion(current, _channel) {\n        if (typeof current === \"string\") {\n            throw new Error(\"Please override this method to use string versions.\");\n        }\n        return (current !== undefined && typeof current === \"number\" ? current + 1 : 1);\n    }\n}\nfunction compareChannelVersions(a, b) {\n    if (typeof a === \"number\" && typeof b === \"number\") {\n        return Math.sign(a - b);\n    }\n    return String(a).localeCompare(String(b));\n}\nfunction maxChannelVersion(...versions) {\n    return versions.reduce((max, version, idx) => {\n        if (idx === 0)\n            return version;\n        return compareChannelVersions(max, version) >= 0 ? max : version;\n    });\n}\n/**\n * Mapping from error type to error index.\n * Regular writes just map to their index in the list of writes being saved.\n * Special writes (e.g. errors) map to negative indices, to avoid those writes from\n * conflicting with regular writes.\n * Each Checkpointer implementation should use this mapping in put_writes.\n */\nconst WRITES_IDX_MAP = {\n    [_serde_types_js__WEBPACK_IMPORTED_MODULE_1__.ERROR]: -1,\n    [_serde_types_js__WEBPACK_IMPORTED_MODULE_1__.SCHEDULED]: -2,\n    [_serde_types_js__WEBPACK_IMPORTED_MODULE_1__.INTERRUPT]: -3,\n    [_serde_types_js__WEBPACK_IMPORTED_MODULE_1__.RESUME]: -4,\n};\nfunction getCheckpointId(config) {\n    return (config.configurable?.checkpoint_id || config.configurable?.thread_ts || \"\");\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/id.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/id.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uuid5: () => (/* binding */ uuid5),\n/* harmony export */   uuid6: () => (/* binding */ uuid6)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/v6.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/v5.js\");\n\nfunction uuid6(clockseq) {\n    return (0,uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({ clockseq });\n}\n// Skip UUID validation check, since UUID6s\n// generated with negative clockseq are not\n// technically compliant, but still work.\n// See: https://github.com/uuidjs/uuid/issues/511\nfunction uuid5(name, namespace) {\n    const namespaceBytes = namespace\n        .replace(/-/g, \"\")\n        .match(/.{2}/g)\n        .map((byte) => parseInt(byte, 16));\n    return (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(name, new Uint8Array(namespaceBytes));\n}\n//# sourceMappingURL=id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3QvaWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUN2QjtBQUNQLFdBQVcsZ0RBQUUsR0FBRyxVQUFVO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxrQkFBa0IsRUFBRTtBQUNwQjtBQUNBLFdBQVcsZ0RBQUU7QUFDYjtBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsYW5nY2hhaW4rbGFuZ2dyYXBoLWNoZWNrcF80NmFjNzhhMWE5MzM2NzAzOWQ2OThlZDgyMmZkYmE2NVxcbm9kZV9tb2R1bGVzXFxAbGFuZ2NoYWluXFxsYW5nZ3JhcGgtY2hlY2twb2ludFxcZGlzdFxcaWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdjUsIHY2IH0gZnJvbSBcInV1aWRcIjtcbmV4cG9ydCBmdW5jdGlvbiB1dWlkNihjbG9ja3NlcSkge1xuICAgIHJldHVybiB2Nih7IGNsb2Nrc2VxIH0pO1xufVxuLy8gU2tpcCBVVUlEIHZhbGlkYXRpb24gY2hlY2ssIHNpbmNlIFVVSUQ2c1xuLy8gZ2VuZXJhdGVkIHdpdGggbmVnYXRpdmUgY2xvY2tzZXEgYXJlIG5vdFxuLy8gdGVjaG5pY2FsbHkgY29tcGxpYW50LCBidXQgc3RpbGwgd29yay5cbi8vIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkL2lzc3Vlcy81MTFcbmV4cG9ydCBmdW5jdGlvbiB1dWlkNShuYW1lLCBuYW1lc3BhY2UpIHtcbiAgICBjb25zdCBuYW1lc3BhY2VCeXRlcyA9IG5hbWVzcGFjZVxuICAgICAgICAucmVwbGFjZSgvLS9nLCBcIlwiKVxuICAgICAgICAubWF0Y2goLy57Mn0vZylcbiAgICAgICAgLm1hcCgoYnl0ZSkgPT4gcGFyc2VJbnQoYnl0ZSwgMTYpKTtcbiAgICByZXR1cm4gdjUobmFtZSwgbmV3IFVpbnQ4QXJyYXkobmFtZXNwYWNlQnl0ZXMpKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/id.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/index.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/index.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncBatchedStore: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.AsyncBatchedStore),\n/* harmony export */   BaseCheckpointSaver: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.BaseCheckpointSaver),\n/* harmony export */   BaseStore: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.BaseStore),\n/* harmony export */   ERROR: () => (/* reexport safe */ _serde_types_js__WEBPACK_IMPORTED_MODULE_5__.ERROR),\n/* harmony export */   INTERRUPT: () => (/* reexport safe */ _serde_types_js__WEBPACK_IMPORTED_MODULE_5__.INTERRUPT),\n/* harmony export */   InMemoryStore: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.InMemoryStore),\n/* harmony export */   InvalidNamespaceError: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.InvalidNamespaceError),\n/* harmony export */   MemorySaver: () => (/* reexport safe */ _memory_js__WEBPACK_IMPORTED_MODULE_0__.MemorySaver),\n/* harmony export */   MemoryStore: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.MemoryStore),\n/* harmony export */   RESUME: () => (/* reexport safe */ _serde_types_js__WEBPACK_IMPORTED_MODULE_5__.RESUME),\n/* harmony export */   SCHEDULED: () => (/* reexport safe */ _serde_types_js__WEBPACK_IMPORTED_MODULE_5__.SCHEDULED),\n/* harmony export */   TASKS: () => (/* reexport safe */ _serde_types_js__WEBPACK_IMPORTED_MODULE_5__.TASKS),\n/* harmony export */   WRITES_IDX_MAP: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.WRITES_IDX_MAP),\n/* harmony export */   compareChannelVersions: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.compareChannelVersions),\n/* harmony export */   copyCheckpoint: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.copyCheckpoint),\n/* harmony export */   deepCopy: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.deepCopy),\n/* harmony export */   emptyCheckpoint: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.emptyCheckpoint),\n/* harmony export */   getCheckpointId: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.getCheckpointId),\n/* harmony export */   getTextAtPath: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.getTextAtPath),\n/* harmony export */   maxChannelVersion: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_1__.maxChannelVersion),\n/* harmony export */   tokenizePath: () => (/* reexport safe */ _store_index_js__WEBPACK_IMPORTED_MODULE_6__.tokenizePath),\n/* harmony export */   uuid5: () => (/* reexport safe */ _id_js__WEBPACK_IMPORTED_MODULE_2__.uuid5),\n/* harmony export */   uuid6: () => (/* reexport safe */ _id_js__WEBPACK_IMPORTED_MODULE_2__.uuid6)\n/* harmony export */ });\n/* harmony import */ var _memory_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./memory.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/memory.js\");\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/base.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/id.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/types.js\");\n/* harmony import */ var _serde_base_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./serde/base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/base.js\");\n/* harmony import */ var _serde_types_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./serde/types.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js\");\n/* harmony import */ var _store_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./store/index.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/index.js\");\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ2hCO0FBQ0Y7QUFDRztBQUNLO0FBQ0M7QUFDQTtBQUNqQyIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGFuZ2NoYWluK2xhbmdncmFwaC1jaGVja3BfNDZhYzc4YTFhOTMzNjcwMzlkNjk4ZWQ4MjJmZGJhNjVcXG5vZGVfbW9kdWxlc1xcQGxhbmdjaGFpblxcbGFuZ2dyYXBoLWNoZWNrcG9pbnRcXGRpc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE1lbW9yeVNhdmVyIH0gZnJvbSBcIi4vbWVtb3J5LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9iYXNlLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pZC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXMuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlL2Jhc2UuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlL3R5cGVzLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zdG9yZS9pbmRleC5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/memory.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/memory.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MemorySaver: () => (/* binding */ MemorySaver)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/base.js\");\n/* harmony import */ var _serde_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serde/types.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js\");\n\n\nfunction _generateKey(threadId, checkpointNamespace, checkpointId) {\n    return JSON.stringify([threadId, checkpointNamespace, checkpointId]);\n}\nclass MemorySaver extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseCheckpointSaver {\n    constructor(serde) {\n        super(serde);\n        // thread ID ->  checkpoint namespace -> checkpoint ID -> checkpoint mapping\n        Object.defineProperty(this, \"storage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n        Object.defineProperty(this, \"writes\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {}\n        });\n    }\n    async _getPendingSends(threadId, checkpointNs, parentCheckpointId) {\n        let pendingSends = [];\n        if (parentCheckpointId !== undefined) {\n            const key = _generateKey(threadId, checkpointNs, parentCheckpointId);\n            pendingSends = await Promise.all(Object.values(this.writes[key] || {})\n                ?.filter(([_taskId, channel]) => {\n                return channel === _serde_types_js__WEBPACK_IMPORTED_MODULE_1__.TASKS;\n            })\n                .map(([_taskId, _channel, writes]) => {\n                return this.serde.loadsTyped(\"json\", writes);\n            }) ?? []);\n        }\n        return pendingSends;\n    }\n    async getTuple(config) {\n        const thread_id = config.configurable?.thread_id;\n        const checkpoint_ns = config.configurable?.checkpoint_ns ?? \"\";\n        let checkpoint_id = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.getCheckpointId)(config);\n        if (checkpoint_id) {\n            const saved = this.storage[thread_id]?.[checkpoint_ns]?.[checkpoint_id];\n            if (saved !== undefined) {\n                const [checkpoint, metadata, parentCheckpointId] = saved;\n                const key = _generateKey(thread_id, checkpoint_ns, checkpoint_id);\n                const pending_sends = await this._getPendingSends(thread_id, checkpoint_ns, parentCheckpointId);\n                const deserializedCheckpoint = {\n                    ...(await this.serde.loadsTyped(\"json\", checkpoint)),\n                    pending_sends,\n                };\n                const pendingWrites = await Promise.all(Object.values(this.writes[key] || {}).map(async ([taskId, channel, value]) => {\n                    return [\n                        taskId,\n                        channel,\n                        await this.serde.loadsTyped(\"json\", value),\n                    ];\n                }));\n                const checkpointTuple = {\n                    config,\n                    checkpoint: deserializedCheckpoint,\n                    metadata: (await this.serde.loadsTyped(\"json\", metadata)),\n                    pendingWrites,\n                };\n                if (parentCheckpointId !== undefined) {\n                    checkpointTuple.parentConfig = {\n                        configurable: {\n                            thread_id,\n                            checkpoint_ns,\n                            checkpoint_id: parentCheckpointId,\n                        },\n                    };\n                }\n                return checkpointTuple;\n            }\n        }\n        else {\n            const checkpoints = this.storage[thread_id]?.[checkpoint_ns];\n            if (checkpoints !== undefined) {\n                // eslint-disable-next-line prefer-destructuring\n                checkpoint_id = Object.keys(checkpoints).sort((a, b) => b.localeCompare(a))[0];\n                const saved = checkpoints[checkpoint_id];\n                const [checkpoint, metadata, parentCheckpointId] = saved;\n                const key = _generateKey(thread_id, checkpoint_ns, checkpoint_id);\n                const pending_sends = await this._getPendingSends(thread_id, checkpoint_ns, parentCheckpointId);\n                const deserializedCheckpoint = {\n                    ...(await this.serde.loadsTyped(\"json\", checkpoint)),\n                    pending_sends,\n                };\n                const pendingWrites = await Promise.all(Object.values(this.writes[key] || {}).map(async ([taskId, channel, value]) => {\n                    return [\n                        taskId,\n                        channel,\n                        await this.serde.loadsTyped(\"json\", value),\n                    ];\n                }));\n                const checkpointTuple = {\n                    config: {\n                        configurable: {\n                            thread_id,\n                            checkpoint_id,\n                            checkpoint_ns,\n                        },\n                    },\n                    checkpoint: deserializedCheckpoint,\n                    metadata: (await this.serde.loadsTyped(\"json\", metadata)),\n                    pendingWrites,\n                };\n                if (parentCheckpointId !== undefined) {\n                    checkpointTuple.parentConfig = {\n                        configurable: {\n                            thread_id,\n                            checkpoint_ns,\n                            checkpoint_id: parentCheckpointId,\n                        },\n                    };\n                }\n                return checkpointTuple;\n            }\n        }\n        return undefined;\n    }\n    async *list(config, options) {\n        // eslint-disable-next-line prefer-const\n        let { before, limit, filter } = options ?? {};\n        const threadIds = config.configurable?.thread_id\n            ? [config.configurable?.thread_id]\n            : Object.keys(this.storage);\n        const configCheckpointNamespace = config.configurable?.checkpoint_ns;\n        const configCheckpointId = config.configurable?.checkpoint_id;\n        for (const threadId of threadIds) {\n            for (const checkpointNamespace of Object.keys(this.storage[threadId] ?? {})) {\n                if (configCheckpointNamespace !== undefined &&\n                    checkpointNamespace !== configCheckpointNamespace) {\n                    continue;\n                }\n                const checkpoints = this.storage[threadId]?.[checkpointNamespace] ?? {};\n                const sortedCheckpoints = Object.entries(checkpoints).sort((a, b) => b[0].localeCompare(a[0]));\n                for (const [checkpointId, [checkpoint, metadataStr, parentCheckpointId],] of sortedCheckpoints) {\n                    // Filter by checkpoint ID from config\n                    if (configCheckpointId && checkpointId !== configCheckpointId) {\n                        continue;\n                    }\n                    // Filter by checkpoint ID from before config\n                    if (before &&\n                        before.configurable?.checkpoint_id &&\n                        checkpointId >= before.configurable.checkpoint_id) {\n                        continue;\n                    }\n                    // Parse metadata\n                    const metadata = (await this.serde.loadsTyped(\"json\", metadataStr));\n                    if (filter &&\n                        !Object.entries(filter).every(([key, value]) => metadata[key] === value)) {\n                        continue;\n                    }\n                    // Limit search results\n                    if (limit !== undefined) {\n                        if (limit <= 0)\n                            break;\n                        limit -= 1;\n                    }\n                    const key = _generateKey(threadId, checkpointNamespace, checkpointId);\n                    const writes = Object.values(this.writes[key] || {});\n                    const pending_sends = await this._getPendingSends(threadId, checkpointNamespace, parentCheckpointId);\n                    const pendingWrites = await Promise.all(writes.map(async ([taskId, channel, value]) => {\n                        return [\n                            taskId,\n                            channel,\n                            await this.serde.loadsTyped(\"json\", value),\n                        ];\n                    }));\n                    const deserializedCheckpoint = {\n                        ...(await this.serde.loadsTyped(\"json\", checkpoint)),\n                        pending_sends,\n                    };\n                    const checkpointTuple = {\n                        config: {\n                            configurable: {\n                                thread_id: threadId,\n                                checkpoint_ns: checkpointNamespace,\n                                checkpoint_id: checkpointId,\n                            },\n                        },\n                        checkpoint: deserializedCheckpoint,\n                        metadata,\n                        pendingWrites,\n                    };\n                    if (parentCheckpointId !== undefined) {\n                        checkpointTuple.parentConfig = {\n                            configurable: {\n                                thread_id: threadId,\n                                checkpoint_ns: checkpointNamespace,\n                                checkpoint_id: parentCheckpointId,\n                            },\n                        };\n                    }\n                    yield checkpointTuple;\n                }\n            }\n        }\n    }\n    async put(config, checkpoint, metadata) {\n        const preparedCheckpoint = (0,_base_js__WEBPACK_IMPORTED_MODULE_0__.copyCheckpoint)(checkpoint);\n        delete preparedCheckpoint.pending_sends;\n        const threadId = config.configurable?.thread_id;\n        const checkpointNamespace = config.configurable?.checkpoint_ns ?? \"\";\n        if (threadId === undefined) {\n            throw new Error(`Failed to put checkpoint. The passed RunnableConfig is missing a required \"thread_id\" field in its \"configurable\" property.`);\n        }\n        if (!this.storage[threadId]) {\n            this.storage[threadId] = {};\n        }\n        if (!this.storage[threadId][checkpointNamespace]) {\n            this.storage[threadId][checkpointNamespace] = {};\n        }\n        const [, serializedCheckpoint] = this.serde.dumpsTyped(preparedCheckpoint);\n        const [, serializedMetadata] = this.serde.dumpsTyped(metadata);\n        this.storage[threadId][checkpointNamespace][checkpoint.id] = [\n            serializedCheckpoint,\n            serializedMetadata,\n            config.configurable?.checkpoint_id, // parent\n        ];\n        return {\n            configurable: {\n                thread_id: threadId,\n                checkpoint_ns: checkpointNamespace,\n                checkpoint_id: checkpoint.id,\n            },\n        };\n    }\n    async putWrites(config, writes, taskId) {\n        const threadId = config.configurable?.thread_id;\n        const checkpointNamespace = config.configurable?.checkpoint_ns;\n        const checkpointId = config.configurable?.checkpoint_id;\n        if (threadId === undefined) {\n            throw new Error(`Failed to put writes. The passed RunnableConfig is missing a required \"thread_id\" field in its \"configurable\" property`);\n        }\n        if (checkpointId === undefined) {\n            throw new Error(`Failed to put writes. The passed RunnableConfig is missing a required \"checkpoint_id\" field in its \"configurable\" property.`);\n        }\n        const outerKey = _generateKey(threadId, checkpointNamespace, checkpointId);\n        const outerWrites_ = this.writes[outerKey];\n        if (this.writes[outerKey] === undefined) {\n            this.writes[outerKey] = {};\n        }\n        writes.forEach(([channel, value], idx) => {\n            const [, serializedValue] = this.serde.dumpsTyped(value);\n            const innerKey = [\n                taskId,\n                _base_js__WEBPACK_IMPORTED_MODULE_0__.WRITES_IDX_MAP[channel] || idx,\n            ];\n            const innerKeyStr = `${innerKey[0]},${innerKey[1]}`;\n            if (innerKey[1] >= 0 && outerWrites_ && innerKeyStr in outerWrites_) {\n                return;\n            }\n            this.writes[outerKey][innerKeyStr] = [taskId, channel, serializedValue];\n        });\n    }\n}\n//# sourceMappingURL=memory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/memory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/base.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/base.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3Qvc2VyZGUvYmFzZS5qcyIsIm1hcHBpbmdzIjoiO0FBQVU7QUFDViIsInNvdXJjZXMiOlsiRzpcXFN0dWR5XFxQeXRob25cXGJhY2t1cFxcQWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGFuZ2NoYWluK2xhbmdncmFwaC1jaGVja3BfNDZhYzc4YTFhOTMzNjcwMzlkNjk4ZWQ4MjJmZGJhNjVcXG5vZGVfbW9kdWxlc1xcQGxhbmdjaGFpblxcbGFuZ2dyYXBoLWNoZWNrcG9pbnRcXGRpc3RcXHNlcmRlXFxiYXNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJhc2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/jsonplus.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/jsonplus.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonPlusSerializer: () => (/* binding */ JsonPlusSerializer)\n/* harmony export */ });\n/* harmony import */ var _langchain_core_load__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/core/load */ \"(ssr)/./node_modules/.pnpm/@langchain+core@0.3.56_openai@4.100.0_ws@8.18.2_zod@3.24.4_/node_modules/@langchain/core/load.js\");\n/* harmony import */ var _utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/fast-safe-stringify/index.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/utils/fast-safe-stringify/index.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable no-instanceof/no-instanceof */\n\n\nfunction isLangChainSerializedObject(value) {\n    return (value !== null &&\n        value.lc === 1 &&\n        value.type === \"constructor\" &&\n        Array.isArray(value.id));\n}\n/**\n * The replacer in stringify does not allow delegation to built-in LangChain\n * serialization methods, and instead immediately calls `.toJSON()` and\n * continues to stringify subfields.\n *\n * We therefore must start from the most nested elements in the input and\n * deserialize upwards rather than top-down.\n */\nasync function _reviver(value) {\n    if (value && typeof value === \"object\") {\n        if (Array.isArray(value)) {\n            const revivedArray = await Promise.all(value.map((item) => _reviver(item)));\n            return revivedArray;\n        }\n        else {\n            const revivedObj = {};\n            for (const [k, v] of Object.entries(value)) {\n                revivedObj[k] = await _reviver(v);\n            }\n            if (revivedObj.lc === 2 && revivedObj.type === \"undefined\") {\n                return undefined;\n            }\n            else if (revivedObj.lc === 2 &&\n                revivedObj.type === \"constructor\" &&\n                Array.isArray(revivedObj.id)) {\n                try {\n                    const constructorName = revivedObj.id[revivedObj.id.length - 1];\n                    let constructor;\n                    switch (constructorName) {\n                        case \"Set\":\n                            constructor = Set;\n                            break;\n                        case \"Map\":\n                            constructor = Map;\n                            break;\n                        case \"RegExp\":\n                            constructor = RegExp;\n                            break;\n                        case \"Error\":\n                            constructor = Error;\n                            break;\n                        default:\n                            return revivedObj;\n                    }\n                    if (revivedObj.method) {\n                        return constructor[revivedObj.method](...(revivedObj.args || []));\n                    }\n                    else {\n                        return new constructor(...(revivedObj.args || []));\n                    }\n                }\n                catch (error) {\n                    return revivedObj;\n                }\n            }\n            else if (isLangChainSerializedObject(revivedObj)) {\n                return (0,_langchain_core_load__WEBPACK_IMPORTED_MODULE_0__.load)(JSON.stringify(revivedObj));\n            }\n            return revivedObj;\n        }\n    }\n    return value;\n}\nfunction _encodeConstructorArgs(\n// eslint-disable-next-line @typescript-eslint/ban-types\nconstructor, method, args, kwargs) {\n    return {\n        lc: 2,\n        type: \"constructor\",\n        id: [constructor.name],\n        method: method ?? null,\n        args: args ?? [],\n        kwargs: kwargs ?? {},\n    };\n}\nfunction _default(obj) {\n    if (obj === undefined) {\n        return {\n            lc: 2,\n            type: \"undefined\",\n        };\n    }\n    else if (obj instanceof Set || obj instanceof Map) {\n        return _encodeConstructorArgs(obj.constructor, undefined, [\n            Array.from(obj),\n        ]);\n    }\n    else if (obj instanceof RegExp) {\n        return _encodeConstructorArgs(RegExp, undefined, [obj.source, obj.flags]);\n    }\n    else if (obj instanceof Error) {\n        return _encodeConstructorArgs(obj.constructor, undefined, [obj.message]);\n        // TODO: Remove special case\n    }\n    else if (obj?.lg_name === \"Send\") {\n        return {\n            node: obj.node,\n            args: obj.args,\n        };\n    }\n    else {\n        return obj;\n    }\n}\nclass JsonPlusSerializer {\n    _dumps(obj) {\n        const encoder = new TextEncoder();\n        return encoder.encode((0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_1__.stringify)(obj, (_, value) => {\n            return _default(value);\n        }));\n    }\n    dumpsTyped(obj) {\n        if (obj instanceof Uint8Array) {\n            return [\"bytes\", obj];\n        }\n        else {\n            return [\"json\", this._dumps(obj)];\n        }\n    }\n    async _loads(data) {\n        const parsed = JSON.parse(data);\n        return _reviver(parsed);\n    }\n    async loadsTyped(type, data) {\n        if (type === \"bytes\") {\n            return typeof data === \"string\" ? new TextEncoder().encode(data) : data;\n        }\n        else if (type === \"json\") {\n            return this._loads(typeof data === \"string\" ? data : new TextDecoder().decode(data));\n        }\n        else {\n            throw new Error(`Unknown serialization type: ${type}`);\n        }\n    }\n}\n//# sourceMappingURL=jsonplus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/jsonplus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   INTERRUPT: () => (/* binding */ INTERRUPT),\n/* harmony export */   RESUME: () => (/* binding */ RESUME),\n/* harmony export */   SCHEDULED: () => (/* binding */ SCHEDULED),\n/* harmony export */   TASKS: () => (/* binding */ TASKS)\n/* harmony export */ });\nconst TASKS = \"__pregel_tasks\";\nconst ERROR = \"__error__\";\nconst SCHEDULED = \"__scheduled__\";\nconst INTERRUPT = \"__interrupt__\";\nconst RESUME = \"__resume__\";\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3Qvc2VyZGUvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1AiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1XFxub2RlX21vZHVsZXNcXEBsYW5nY2hhaW5cXGxhbmdncmFwaC1jaGVja3BvaW50XFxkaXN0XFxzZXJkZVxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFRBU0tTID0gXCJfX3ByZWdlbF90YXNrc1wiO1xuZXhwb3J0IGNvbnN0IEVSUk9SID0gXCJfX2Vycm9yX19cIjtcbmV4cG9ydCBjb25zdCBTQ0hFRFVMRUQgPSBcIl9fc2NoZWR1bGVkX19cIjtcbmV4cG9ydCBjb25zdCBJTlRFUlJVUFQgPSBcIl9faW50ZXJydXB0X19cIjtcbmV4cG9ydCBjb25zdCBSRVNVTUUgPSBcIl9fcmVzdW1lX19cIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/utils/fast-safe-stringify/index.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/utils/fast-safe-stringify/index.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/* eslint-disable */\n// @ts-nocheck\n// Stringify that can handle circular references.\n// Inlined due to ESM import issues\n// Source: https://www.npmjs.com/package/fast-safe-stringify\nvar LIMIT_REPLACE_NODE = \"[...]\";\nvar CIRCULAR_REPLACE_NODE = \"[Circular]\";\nvar arr = [];\nvar replacerStack = [];\nfunction defaultOptions() {\n    return {\n        depthLimit: Number.MAX_SAFE_INTEGER,\n        edgesLimit: Number.MAX_SAFE_INTEGER,\n    };\n}\n// Regular stringify\nfunction stringify(obj, replacer, spacer, options) {\n    if (typeof options === \"undefined\") {\n        options = defaultOptions();\n    }\n    decirc(obj, \"\", 0, [], undefined, 0, options);\n    var res;\n    try {\n        if (replacerStack.length === 0) {\n            res = JSON.stringify(obj, replacer, spacer);\n        }\n        else {\n            res = JSON.stringify(obj, replaceGetterValues(replacer), spacer);\n        }\n    }\n    catch (_) {\n        return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\");\n    }\n    finally {\n        while (arr.length !== 0) {\n            var part = arr.pop();\n            if (part.length === 4) {\n                Object.defineProperty(part[0], part[1], part[3]);\n            }\n            else {\n                part[0][part[1]] = part[2];\n            }\n        }\n    }\n    return res;\n}\nfunction setReplace(replace, val, k, parent) {\n    var propertyDescriptor = Object.getOwnPropertyDescriptor(parent, k);\n    if (propertyDescriptor.get !== undefined) {\n        if (propertyDescriptor.configurable) {\n            Object.defineProperty(parent, k, { value: replace });\n            arr.push([parent, k, val, propertyDescriptor]);\n        }\n        else {\n            replacerStack.push([val, k, replace]);\n        }\n    }\n    else {\n        parent[k] = replace;\n        arr.push([parent, k, val]);\n    }\n}\nfunction decirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                decirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            var keys = Object.keys(val);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                decirc(val[key], key, i, stack, val, depth, options);\n            }\n        }\n        stack.pop();\n    }\n}\n// Stable-stringify\nfunction compareFunction(a, b) {\n    if (a < b) {\n        return -1;\n    }\n    if (a > b) {\n        return 1;\n    }\n    return 0;\n}\nfunction deterministicStringify(obj, replacer, spacer, options) {\n    if (typeof options === \"undefined\") {\n        options = defaultOptions();\n    }\n    var tmp = deterministicDecirc(obj, \"\", 0, [], undefined, 0, options) || obj;\n    var res;\n    try {\n        if (replacerStack.length === 0) {\n            res = JSON.stringify(tmp, replacer, spacer);\n        }\n        else {\n            res = JSON.stringify(tmp, replaceGetterValues(replacer), spacer);\n        }\n    }\n    catch (_) {\n        return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\");\n    }\n    finally {\n        // Ensure that we restore the object as it was.\n        while (arr.length !== 0) {\n            var part = arr.pop();\n            if (part.length === 4) {\n                Object.defineProperty(part[0], part[1], part[3]);\n            }\n            else {\n                part[0][part[1]] = part[2];\n            }\n        }\n    }\n    return res;\n}\nfunction deterministicDecirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        try {\n            if (typeof val.toJSON === \"function\") {\n                return;\n            }\n        }\n        catch (_) {\n            return;\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                deterministicDecirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            // Create a temporary object in the required way\n            var tmp = {};\n            var keys = Object.keys(val).sort(compareFunction);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                deterministicDecirc(val[key], key, i, stack, val, depth, options);\n                tmp[key] = val[key];\n            }\n            if (typeof parent !== \"undefined\") {\n                arr.push([parent, k, val]);\n                parent[k] = tmp;\n            }\n            else {\n                return tmp;\n            }\n        }\n        stack.pop();\n    }\n}\n// wraps replacer function to handle values we couldn't replace\n// and mark them as replaced value\nfunction replaceGetterValues(replacer) {\n    replacer =\n        typeof replacer !== \"undefined\"\n            ? replacer\n            : function (k, v) {\n                return v;\n            };\n    return function (key, val) {\n        if (replacerStack.length > 0) {\n            for (var i = 0; i < replacerStack.length; i++) {\n                var part = replacerStack[i];\n                if (part[1] === key && part[0] === val) {\n                    val = part[2];\n                    replacerStack.splice(i, 1);\n                    break;\n                }\n            }\n        }\n        return replacer.call(this, key, val);\n    };\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/serde/utils/fast-safe-stringify/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseStore: () => (/* binding */ BaseStore),\n/* harmony export */   InvalidNamespaceError: () => (/* binding */ InvalidNamespaceError),\n/* harmony export */   getTextAtPath: () => (/* binding */ getTextAtPath),\n/* harmony export */   tokenizePath: () => (/* binding */ tokenizePath)\n/* harmony export */ });\n/**\n * Error thrown when an invalid namespace is provided.\n */\nclass InvalidNamespaceError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"InvalidNamespaceError\";\n    }\n}\n/**\n * Validates the provided namespace.\n * @param namespace The namespace to validate.\n * @throws {InvalidNamespaceError} If the namespace is invalid.\n */\nfunction validateNamespace(namespace) {\n    if (namespace.length === 0) {\n        throw new InvalidNamespaceError(\"Namespace cannot be empty.\");\n    }\n    for (const label of namespace) {\n        if (typeof label !== \"string\") {\n            throw new InvalidNamespaceError(`Invalid namespace label '${label}' found in ${namespace}. Namespace labels ` +\n                `must be strings, but got ${typeof label}.`);\n        }\n        if (label.includes(\".\")) {\n            throw new InvalidNamespaceError(`Invalid namespace label '${label}' found in ${namespace}. Namespace labels cannot contain periods ('.').`);\n        }\n        if (label === \"\") {\n            throw new InvalidNamespaceError(`Namespace labels cannot be empty strings. Got ${label} in ${namespace}`);\n        }\n    }\n    if (namespace[0] === \"langgraph\") {\n        throw new InvalidNamespaceError(`Root label for namespace cannot be \"langgraph\". Got: ${namespace}`);\n    }\n}\n/**\n * Utility function to get text at a specific JSON path\n */\nfunction getTextAtPath(obj, path) {\n    const parts = path.split(\".\");\n    let current = obj;\n    for (const part of parts) {\n        if (part.includes(\"[\")) {\n            const [arrayName, indexStr] = part.split(\"[\");\n            const index = indexStr.replace(\"]\", \"\");\n            if (!current[arrayName])\n                return [];\n            if (index === \"*\") {\n                const results = [];\n                for (const item of current[arrayName]) {\n                    if (typeof item === \"string\")\n                        results.push(item);\n                }\n                return results;\n            }\n            const idx = parseInt(index, 10);\n            if (Number.isNaN(idx))\n                return [];\n            current = current[arrayName][idx];\n        }\n        else {\n            current = current[part];\n        }\n        if (current === undefined)\n            return [];\n    }\n    return typeof current === \"string\" ? [current] : [];\n}\n/**\n * Tokenizes a JSON path into parts\n */\nfunction tokenizePath(path) {\n    return path.split(\".\");\n}\n/**\n * Abstract base class for persistent key-value stores.\n *\n * Stores enable persistence and memory that can be shared across threads,\n * scoped to user IDs, assistant IDs, or other arbitrary namespaces.\n *\n * Features:\n * - Hierarchical namespaces for organization\n * - Key-value storage with metadata\n * - Vector similarity search (if configured)\n * - Filtering and pagination\n */\nclass BaseStore {\n    /**\n     * Retrieve a single item by its namespace and key.\n     *\n     * @param namespace Hierarchical path for the item\n     * @param key Unique identifier within the namespace\n     * @returns Promise resolving to the item or null if not found\n     */\n    async get(namespace, key) {\n        return (await this.batch([{ namespace, key }]))[0];\n    }\n    /**\n     * Search for items within a namespace prefix.\n     * Supports both metadata filtering and vector similarity search.\n     *\n     * @param namespacePrefix Hierarchical path prefix to search within\n     * @param options Search options for filtering and pagination\n     * @returns Promise resolving to list of matching items with relevance scores\n     *\n     * @example\n     * // Search with filters\n     * await store.search([\"documents\"], {\n     *   filter: { type: \"report\", status: \"active\" },\n     *   limit: 5,\n     *   offset: 10\n     * });\n     *\n     * // Vector similarity search\n     * await store.search([\"users\", \"content\"], {\n     *   query: \"technical documentation about APIs\",\n     *   limit: 20\n     * });\n     */\n    async search(namespacePrefix, options = {}) {\n        const { filter, limit = 10, offset = 0, query } = options;\n        return (await this.batch([\n            {\n                namespacePrefix,\n                filter,\n                limit,\n                offset,\n                query,\n            },\n        ]))[0];\n    }\n    /**\n     * Store or update an item.\n     *\n     * @param namespace Hierarchical path for the item\n     * @param key Unique identifier within the namespace\n     * @param value Object containing the item's data\n     * @param index Optional indexing configuration\n     *\n     * @example\n     * // Simple storage\n     * await store.put([\"docs\"], \"report\", { title: \"Annual Report\" });\n     *\n     * // With specific field indexing\n     * await store.put(\n     *   [\"docs\"],\n     *   \"report\",\n     *   {\n     *     title: \"Q4 Report\",\n     *     chapters: [{ content: \"...\" }, { content: \"...\" }]\n     *   },\n     *   [\"title\", \"chapters[*].content\"]\n     * );\n     */\n    async put(namespace, key, value, index) {\n        validateNamespace(namespace);\n        await this.batch([{ namespace, key, value, index }]);\n    }\n    /**\n     * Delete an item from the store.\n     *\n     * @param namespace Hierarchical path for the item\n     * @param key Unique identifier within the namespace\n     */\n    async delete(namespace, key) {\n        await this.batch([{ namespace, key, value: null }]);\n    }\n    /**\n     * List and filter namespaces in the store.\n     * Used to explore data organization and navigate the namespace hierarchy.\n     *\n     * @param options Options for listing namespaces\n     * @returns Promise resolving to list of namespace paths\n     *\n     * @example\n     * // List all namespaces under \"documents\"\n     * await store.listNamespaces({\n     *   prefix: [\"documents\"],\n     *   maxDepth: 2\n     * });\n     *\n     * // List namespaces ending with \"v1\"\n     * await store.listNamespaces({\n     *   suffix: [\"v1\"],\n     *   limit: 50\n     * });\n     */\n    async listNamespaces(options = {}) {\n        const { prefix, suffix, maxDepth, limit = 100, offset = 0 } = options;\n        const matchConditions = [];\n        if (prefix) {\n            matchConditions.push({ matchType: \"prefix\", path: prefix });\n        }\n        if (suffix) {\n            matchConditions.push({ matchType: \"suffix\", path: suffix });\n        }\n        return (await this.batch([\n            {\n                matchConditions: matchConditions.length ? matchConditions : undefined,\n                maxDepth,\n                limit,\n                offset,\n            },\n        ]))[0];\n    }\n    /**\n     * Start the store. Override if initialization is needed.\n     */\n    start() { }\n    /**\n     * Stop the store. Override if cleanup is needed.\n     */\n    stop() { }\n}\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/batch.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/batch.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncBatchedStore: () => (/* binding */ AsyncBatchedStore)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * Extracts and returns the underlying store from an `AsyncBatchedStore`,\n * or returns the input if it is not an `AsyncBatchedStore`.\n */\nconst extractStore = (input) => {\n    if (\"lg_name\" in input && input.lg_name === \"AsyncBatchedStore\") {\n        // @ts-expect-error is a protected property\n        return input.store;\n    }\n    return input;\n};\nclass AsyncBatchedStore extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseStore {\n    constructor(store) {\n        super();\n        Object.defineProperty(this, \"lg_name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"AsyncBatchedStore\"\n        });\n        Object.defineProperty(this, \"store\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Map()\n        });\n        Object.defineProperty(this, \"nextKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0\n        });\n        Object.defineProperty(this, \"running\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"processingTask\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: null\n        });\n        this.store = extractStore(store);\n    }\n    get isRunning() {\n        return this.running;\n    }\n    /**\n     * @ignore\n     * Batch is not implemented here as we're only extending `BaseStore`\n     * to allow it to be passed where `BaseStore` is expected, and implement\n     * the convenience methods (get, search, put, delete).\n     */\n    async batch(_operations) {\n        throw new Error(\"The `batch` method is not implemented on `AsyncBatchedStore`.\" +\n            \"\\n Instead, it calls the `batch` method on the wrapped store.\" +\n            \"\\n If you are seeing this error, something is wrong.\");\n    }\n    async get(namespace, key) {\n        return this.enqueueOperation({ namespace, key });\n    }\n    async search(namespacePrefix, options) {\n        const { filter, limit = 10, offset = 0, query } = options || {};\n        return this.enqueueOperation({\n            namespacePrefix,\n            filter,\n            limit,\n            offset,\n            query,\n        });\n    }\n    async put(namespace, key, value) {\n        return this.enqueueOperation({ namespace, key, value });\n    }\n    async delete(namespace, key) {\n        return this.enqueueOperation({\n            namespace,\n            key,\n            value: null,\n        });\n    }\n    start() {\n        if (!this.running) {\n            this.running = true;\n            this.processingTask = this.processBatchQueue();\n        }\n    }\n    async stop() {\n        this.running = false;\n        if (this.processingTask) {\n            await this.processingTask;\n        }\n    }\n    enqueueOperation(operation) {\n        return new Promise((resolve, reject) => {\n            const key = this.nextKey;\n            this.nextKey += 1;\n            this.queue.set(key, { operation, resolve, reject });\n        });\n    }\n    async processBatchQueue() {\n        while (this.running) {\n            await new Promise((resolve) => {\n                setTimeout(resolve, 0);\n            });\n            if (this.queue.size === 0)\n                continue;\n            const batch = new Map(this.queue);\n            this.queue.clear();\n            try {\n                const operations = Array.from(batch.values()).map(({ operation }) => operation);\n                const results = await this.store.batch(operations);\n                batch.forEach(({ resolve }, key) => {\n                    const index = Array.from(batch.keys()).indexOf(key);\n                    resolve(results[index]);\n                });\n            }\n            catch (e) {\n                batch.forEach(({ reject }) => {\n                    reject(e);\n                });\n            }\n        }\n    }\n    // AsyncBatchedStore is internal and gets passed as args into traced tasks\n    // some BaseStores contain circular references so just serialize without it\n    // as this causes warnings when tracing with LangSmith.\n    toJSON() {\n        return {\n            queue: this.queue,\n            nextKey: this.nextKey,\n            running: this.running,\n            store: \"[LangGraphStore]\",\n        };\n    }\n}\n//# sourceMappingURL=batch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3Qvc3RvcmUvYmF0Y2guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUN1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sZ0NBQWdDLCtDQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGdCQUFnQjtBQUN2RDtBQUNBO0FBQ0EsZ0JBQWdCLHdDQUF3QztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLHVDQUF1Qyx1QkFBdUI7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsNEJBQTRCO0FBQzlELFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsV0FBVztBQUNoRjtBQUNBLGlDQUFpQyxTQUFTO0FBQzFDO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlDQUFpQyxRQUFRO0FBQ3pDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsYW5nY2hhaW4rbGFuZ2dyYXBoLWNoZWNrcF80NmFjNzhhMWE5MzM2NzAzOWQ2OThlZDgyMmZkYmE2NVxcbm9kZV9tb2R1bGVzXFxAbGFuZ2NoYWluXFxsYW5nZ3JhcGgtY2hlY2twb2ludFxcZGlzdFxcc3RvcmVcXGJhdGNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnkgKi9cbmltcG9ydCB7IEJhc2VTdG9yZSwgfSBmcm9tIFwiLi9iYXNlLmpzXCI7XG4vKipcbiAqIEV4dHJhY3RzIGFuZCByZXR1cm5zIHRoZSB1bmRlcmx5aW5nIHN0b3JlIGZyb20gYW4gYEFzeW5jQmF0Y2hlZFN0b3JlYCxcbiAqIG9yIHJldHVybnMgdGhlIGlucHV0IGlmIGl0IGlzIG5vdCBhbiBgQXN5bmNCYXRjaGVkU3RvcmVgLlxuICovXG5jb25zdCBleHRyYWN0U3RvcmUgPSAoaW5wdXQpID0+IHtcbiAgICBpZiAoXCJsZ19uYW1lXCIgaW4gaW5wdXQgJiYgaW5wdXQubGdfbmFtZSA9PT0gXCJBc3luY0JhdGNoZWRTdG9yZVwiKSB7XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgaXMgYSBwcm90ZWN0ZWQgcHJvcGVydHlcbiAgICAgICAgcmV0dXJuIGlucHV0LnN0b3JlO1xuICAgIH1cbiAgICByZXR1cm4gaW5wdXQ7XG59O1xuZXhwb3J0IGNsYXNzIEFzeW5jQmF0Y2hlZFN0b3JlIGV4dGVuZHMgQmFzZVN0b3JlIHtcbiAgICBjb25zdHJ1Y3RvcihzdG9yZSkge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJsZ19uYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBcIkFzeW5jQmF0Y2hlZFN0b3JlXCJcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInN0b3JlXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInF1ZXVlXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBuZXcgTWFwKClcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm5leHRLZXlcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInJ1bm5pbmdcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJwcm9jZXNzaW5nVGFza1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5zdG9yZSA9IGV4dHJhY3RTdG9yZShzdG9yZSk7XG4gICAgfVxuICAgIGdldCBpc1J1bm5pbmcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnJ1bm5pbmc7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEBpZ25vcmVcbiAgICAgKiBCYXRjaCBpcyBub3QgaW1wbGVtZW50ZWQgaGVyZSBhcyB3ZSdyZSBvbmx5IGV4dGVuZGluZyBgQmFzZVN0b3JlYFxuICAgICAqIHRvIGFsbG93IGl0IHRvIGJlIHBhc3NlZCB3aGVyZSBgQmFzZVN0b3JlYCBpcyBleHBlY3RlZCwgYW5kIGltcGxlbWVudFxuICAgICAqIHRoZSBjb252ZW5pZW5jZSBtZXRob2RzIChnZXQsIHNlYXJjaCwgcHV0LCBkZWxldGUpLlxuICAgICAqL1xuICAgIGFzeW5jIGJhdGNoKF9vcGVyYXRpb25zKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoZSBgYmF0Y2hgIG1ldGhvZCBpcyBub3QgaW1wbGVtZW50ZWQgb24gYEFzeW5jQmF0Y2hlZFN0b3JlYC5cIiArXG4gICAgICAgICAgICBcIlxcbiBJbnN0ZWFkLCBpdCBjYWxscyB0aGUgYGJhdGNoYCBtZXRob2Qgb24gdGhlIHdyYXBwZWQgc3RvcmUuXCIgK1xuICAgICAgICAgICAgXCJcXG4gSWYgeW91IGFyZSBzZWVpbmcgdGhpcyBlcnJvciwgc29tZXRoaW5nIGlzIHdyb25nLlwiKTtcbiAgICB9XG4gICAgYXN5bmMgZ2V0KG5hbWVzcGFjZSwga2V5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmVucXVldWVPcGVyYXRpb24oeyBuYW1lc3BhY2UsIGtleSB9KTtcbiAgICB9XG4gICAgYXN5bmMgc2VhcmNoKG5hbWVzcGFjZVByZWZpeCwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCB7IGZpbHRlciwgbGltaXQgPSAxMCwgb2Zmc2V0ID0gMCwgcXVlcnkgfSA9IG9wdGlvbnMgfHwge307XG4gICAgICAgIHJldHVybiB0aGlzLmVucXVldWVPcGVyYXRpb24oe1xuICAgICAgICAgICAgbmFtZXNwYWNlUHJlZml4LFxuICAgICAgICAgICAgZmlsdGVyLFxuICAgICAgICAgICAgbGltaXQsXG4gICAgICAgICAgICBvZmZzZXQsXG4gICAgICAgICAgICBxdWVyeSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGFzeW5jIHB1dChuYW1lc3BhY2UsIGtleSwgdmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZW5xdWV1ZU9wZXJhdGlvbih7IG5hbWVzcGFjZSwga2V5LCB2YWx1ZSB9KTtcbiAgICB9XG4gICAgYXN5bmMgZGVsZXRlKG5hbWVzcGFjZSwga2V5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLmVucXVldWVPcGVyYXRpb24oe1xuICAgICAgICAgICAgbmFtZXNwYWNlLFxuICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgdmFsdWU6IG51bGwsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBzdGFydCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLnJ1bm5pbmcpIHtcbiAgICAgICAgICAgIHRoaXMucnVubmluZyA9IHRydWU7XG4gICAgICAgICAgICB0aGlzLnByb2Nlc3NpbmdUYXNrID0gdGhpcy5wcm9jZXNzQmF0Y2hRdWV1ZSgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFzeW5jIHN0b3AoKSB7XG4gICAgICAgIHRoaXMucnVubmluZyA9IGZhbHNlO1xuICAgICAgICBpZiAodGhpcy5wcm9jZXNzaW5nVGFzaykge1xuICAgICAgICAgICAgYXdhaXQgdGhpcy5wcm9jZXNzaW5nVGFzaztcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbnF1ZXVlT3BlcmF0aW9uKG9wZXJhdGlvbikge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5uZXh0S2V5O1xuICAgICAgICAgICAgdGhpcy5uZXh0S2V5ICs9IDE7XG4gICAgICAgICAgICB0aGlzLnF1ZXVlLnNldChrZXksIHsgb3BlcmF0aW9uLCByZXNvbHZlLCByZWplY3QgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBhc3luYyBwcm9jZXNzQmF0Y2hRdWV1ZSgpIHtcbiAgICAgICAgd2hpbGUgKHRoaXMucnVubmluZykge1xuICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KHJlc29sdmUsIDApO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAodGhpcy5xdWV1ZS5zaXplID09PSAwKVxuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY29uc3QgYmF0Y2ggPSBuZXcgTWFwKHRoaXMucXVldWUpO1xuICAgICAgICAgICAgdGhpcy5xdWV1ZS5jbGVhcigpO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBvcGVyYXRpb25zID0gQXJyYXkuZnJvbShiYXRjaC52YWx1ZXMoKSkubWFwKCh7IG9wZXJhdGlvbiB9KSA9PiBvcGVyYXRpb24pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCB0aGlzLnN0b3JlLmJhdGNoKG9wZXJhdGlvbnMpO1xuICAgICAgICAgICAgICAgIGJhdGNoLmZvckVhY2goKHsgcmVzb2x2ZSB9LCBrZXkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5kZXggPSBBcnJheS5mcm9tKGJhdGNoLmtleXMoKSkuaW5kZXhPZihrZXkpO1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJlc3VsdHNbaW5kZXhdKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgYmF0Y2guZm9yRWFjaCgoeyByZWplY3QgfSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QoZSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gQXN5bmNCYXRjaGVkU3RvcmUgaXMgaW50ZXJuYWwgYW5kIGdldHMgcGFzc2VkIGFzIGFyZ3MgaW50byB0cmFjZWQgdGFza3NcbiAgICAvLyBzb21lIEJhc2VTdG9yZXMgY29udGFpbiBjaXJjdWxhciByZWZlcmVuY2VzIHNvIGp1c3Qgc2VyaWFsaXplIHdpdGhvdXQgaXRcbiAgICAvLyBhcyB0aGlzIGNhdXNlcyB3YXJuaW5ncyB3aGVuIHRyYWNpbmcgd2l0aCBMYW5nU21pdGguXG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcXVldWU6IHRoaXMucXVldWUsXG4gICAgICAgICAgICBuZXh0S2V5OiB0aGlzLm5leHRLZXksXG4gICAgICAgICAgICBydW5uaW5nOiB0aGlzLnJ1bm5pbmcsXG4gICAgICAgICAgICBzdG9yZTogXCJbTGFuZ0dyYXBoU3RvcmVdXCIsXG4gICAgICAgIH07XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmF0Y2guanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/batch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/index.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/index.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncBatchedStore: () => (/* reexport safe */ _batch_js__WEBPACK_IMPORTED_MODULE_1__.AsyncBatchedStore),\n/* harmony export */   BaseStore: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseStore),\n/* harmony export */   InMemoryStore: () => (/* reexport safe */ _memory_js__WEBPACK_IMPORTED_MODULE_2__.InMemoryStore),\n/* harmony export */   InvalidNamespaceError: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_0__.InvalidNamespaceError),\n/* harmony export */   MemoryStore: () => (/* reexport safe */ _memory_js__WEBPACK_IMPORTED_MODULE_2__.MemoryStore),\n/* harmony export */   getTextAtPath: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_0__.getTextAtPath),\n/* harmony export */   tokenizePath: () => (/* reexport safe */ _base_js__WEBPACK_IMPORTED_MODULE_0__.tokenizePath)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js\");\n/* harmony import */ var _batch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./batch.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/batch.js\");\n/* harmony import */ var _memory_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./memory.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/memory.js\");\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3Qvc3RvcmUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNDO0FBQ0M7QUFDNUIiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1XFxub2RlX21vZHVsZXNcXEBsYW5nY2hhaW5cXGxhbmdncmFwaC1jaGVja3BvaW50XFxkaXN0XFxzdG9yZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vYmFzZS5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vYmF0Y2guanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL21lbW9yeS5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/memory.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/memory.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InMemoryStore: () => (/* binding */ InMemoryStore),\n/* harmony export */   MemoryStore: () => (/* binding */ MemoryStore)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/base.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/utils.js\");\n\n\n/**\n * In-memory key-value store with optional vector search.\n *\n * A lightweight store implementation using JavaScript Maps. Supports basic\n * key-value operations and vector search when configured with embeddings.\n *\n * @example\n * ```typescript\n * // Basic key-value storage\n * const store = new InMemoryStore();\n * await store.put([\"users\", \"123\"], \"prefs\", { theme: \"dark\" });\n * const item = await store.get([\"users\", \"123\"], \"prefs\");\n *\n * // Vector search with embeddings\n * import { OpenAIEmbeddings } from \"@langchain/openai\";\n * const store = new InMemoryStore({\n *   index: {\n *     dims: 1536,\n *     embeddings: new OpenAIEmbeddings({ modelName: \"text-embedding-3-small\" }),\n *   }\n * });\n *\n * // Store documents\n * await store.put([\"docs\"], \"doc1\", { text: \"Python tutorial\" });\n * await store.put([\"docs\"], \"doc2\", { text: \"TypeScript guide\" });\n *\n * // Search by similarity\n * const results = await store.search([\"docs\"], { query: \"python programming\" });\n * ```\n *\n * @warning This store keeps all data in memory. Data is lost when the process exits.\n * For persistence, use a database-backed store.\n */\nclass InMemoryStore extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseStore {\n    constructor(options) {\n        super();\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Map()\n        });\n        // Namespace -> Key -> Path/field -> Vector\n        Object.defineProperty(this, \"vectors\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Map()\n        });\n        Object.defineProperty(this, \"_indexConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        if (options?.index) {\n            this._indexConfig = {\n                ...options.index,\n                __tokenizedFields: (options.index.fields ?? [\"$\"]).map((p) => [\n                    p,\n                    p === \"$\" ? [p] : (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.tokenizePath)(p),\n                ]),\n            };\n        }\n    }\n    async batch(operations) {\n        const results = [];\n        const putOps = new Map();\n        const searchOps = new Map();\n        // First pass - handle gets and prepare search/put operations\n        for (let i = 0; i < operations.length; i += 1) {\n            const op = operations[i];\n            if (\"key\" in op && \"namespace\" in op && !(\"value\" in op)) {\n                // GetOperation\n                results.push(this.getOperation(op));\n            }\n            else if (\"namespacePrefix\" in op) {\n                // SearchOperation\n                const candidates = this.filterItems(op);\n                searchOps.set(i, [op, candidates]);\n                results.push(null);\n            }\n            else if (\"value\" in op) {\n                // PutOperation\n                const key = `${op.namespace.join(\":\")}:${op.key}`;\n                putOps.set(key, op);\n                results.push(null);\n            }\n            else if (\"matchConditions\" in op) {\n                // ListNamespacesOperation\n                results.push(this.listNamespacesOperation(op));\n            }\n        }\n        // Handle search operations with embeddings\n        if (searchOps.size > 0) {\n            if (this._indexConfig?.embeddings) {\n                const queries = new Set();\n                for (const [op] of searchOps.values()) {\n                    if (op.query)\n                        queries.add(op.query);\n                }\n                // Get embeddings for all queries\n                const queryEmbeddings = queries.size > 0\n                    ? await Promise.all(Array.from(queries).map((q) => this._indexConfig.embeddings.embedQuery(q)))\n                    : [];\n                const queryVectors = Object.fromEntries(Array.from(queries).map((q, i) => [q, queryEmbeddings[i]]));\n                // Process each search operation\n                for (const [i, [op, candidates]] of searchOps.entries()) {\n                    if (op.query && queryVectors[op.query]) {\n                        const queryVector = queryVectors[op.query];\n                        const scoredResults = this.scoreResults(candidates, queryVector, op.offset ?? 0, op.limit ?? 10);\n                        results[i] = scoredResults;\n                    }\n                    else {\n                        results[i] = this.paginateResults(candidates.map((item) => ({ ...item, score: undefined })), op.offset ?? 0, op.limit ?? 10);\n                    }\n                }\n            }\n            else {\n                // No embeddings - just paginate the filtered results\n                for (const [i, [op, candidates]] of searchOps.entries()) {\n                    results[i] = this.paginateResults(candidates.map((item) => ({ ...item, score: undefined })), op.offset ?? 0, op.limit ?? 10);\n                }\n            }\n        }\n        // Handle put operations with embeddings\n        if (putOps.size > 0 && this._indexConfig?.embeddings) {\n            const toEmbed = this.extractTexts(Array.from(putOps.values()));\n            if (Object.keys(toEmbed).length > 0) {\n                const embeddings = await this._indexConfig.embeddings.embedDocuments(Object.keys(toEmbed));\n                this.insertVectors(toEmbed, embeddings);\n            }\n        }\n        // Apply all put operations\n        for (const op of putOps.values()) {\n            this.putOperation(op);\n        }\n        return results;\n    }\n    getOperation(op) {\n        const namespaceKey = op.namespace.join(\":\");\n        const item = this.data.get(namespaceKey)?.get(op.key);\n        return item ?? null;\n    }\n    putOperation(op) {\n        const namespaceKey = op.namespace.join(\":\");\n        if (!this.data.has(namespaceKey)) {\n            this.data.set(namespaceKey, new Map());\n        }\n        const namespaceMap = this.data.get(namespaceKey);\n        if (op.value === null) {\n            namespaceMap.delete(op.key);\n        }\n        else {\n            const now = new Date();\n            if (namespaceMap.has(op.key)) {\n                const item = namespaceMap.get(op.key);\n                item.value = op.value;\n                item.updatedAt = now;\n            }\n            else {\n                namespaceMap.set(op.key, {\n                    value: op.value,\n                    key: op.key,\n                    namespace: op.namespace,\n                    createdAt: now,\n                    updatedAt: now,\n                });\n            }\n        }\n    }\n    listNamespacesOperation(op) {\n        const allNamespaces = Array.from(this.data.keys()).map((ns) => ns.split(\":\"));\n        let namespaces = allNamespaces;\n        if (op.matchConditions && op.matchConditions.length > 0) {\n            namespaces = namespaces.filter((ns) => op.matchConditions.every((condition) => this.doesMatch(condition, ns)));\n        }\n        if (op.maxDepth !== undefined) {\n            namespaces = Array.from(new Set(namespaces.map((ns) => ns.slice(0, op.maxDepth).join(\":\")))).map((ns) => ns.split(\":\"));\n        }\n        namespaces.sort((a, b) => a.join(\":\").localeCompare(b.join(\":\")));\n        return namespaces.slice(op.offset ?? 0, (op.offset ?? 0) + (op.limit ?? namespaces.length));\n    }\n    doesMatch(matchCondition, key) {\n        const { matchType, path } = matchCondition;\n        if (matchType === \"prefix\") {\n            if (path.length > key.length)\n                return false;\n            return path.every((pElem, index) => {\n                const kElem = key[index];\n                return pElem === \"*\" || kElem === pElem;\n            });\n        }\n        else if (matchType === \"suffix\") {\n            if (path.length > key.length)\n                return false;\n            return path.every((pElem, index) => {\n                const kElem = key[key.length - path.length + index];\n                return pElem === \"*\" || kElem === pElem;\n            });\n        }\n        throw new Error(`Unsupported match type: ${matchType}`);\n    }\n    filterItems(op) {\n        const candidates = [];\n        for (const [namespace, items] of this.data.entries()) {\n            if (namespace.startsWith(op.namespacePrefix.join(\":\"))) {\n                candidates.push(...items.values());\n            }\n        }\n        let filteredCandidates = candidates;\n        if (op.filter) {\n            filteredCandidates = candidates.filter((item) => Object.entries(op.filter).every(([key, value]) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.compareValues)(item.value[key], value)));\n        }\n        return filteredCandidates;\n    }\n    scoreResults(candidates, queryVector, offset = 0, limit = 10) {\n        const flatItems = [];\n        const flatVectors = [];\n        const scoreless = [];\n        for (const item of candidates) {\n            const vectors = this.getVectors(item);\n            if (vectors.length) {\n                for (const vector of vectors) {\n                    flatItems.push(item);\n                    flatVectors.push(vector);\n                }\n            }\n            else {\n                scoreless.push(item);\n            }\n        }\n        const scores = this.cosineSimilarity(queryVector, flatVectors);\n        const sortedResults = scores\n            .map((score, i) => [score, flatItems[i]])\n            .sort((a, b) => b[0] - a[0]);\n        const seen = new Set();\n        const kept = [];\n        for (const [score, item] of sortedResults) {\n            const key = `${item.namespace.join(\":\")}:${item.key}`;\n            if (seen.has(key))\n                continue;\n            const ix = seen.size;\n            if (ix >= offset + limit)\n                break;\n            if (ix < offset) {\n                seen.add(key);\n                continue;\n            }\n            seen.add(key);\n            kept.push([score, item]);\n        }\n        if (scoreless.length && kept.length < limit) {\n            for (const item of scoreless.slice(0, limit - kept.length)) {\n                const key = `${item.namespace.join(\":\")}:${item.key}`;\n                if (!seen.has(key)) {\n                    seen.add(key);\n                    kept.push([undefined, item]);\n                }\n            }\n        }\n        return kept.map(([score, item]) => ({\n            ...item,\n            score,\n        }));\n    }\n    paginateResults(results, offset, limit) {\n        return results.slice(offset, offset + limit);\n    }\n    extractTexts(ops) {\n        if (!ops.length || !this._indexConfig) {\n            return {};\n        }\n        const toEmbed = {};\n        for (const op of ops) {\n            if (op.value !== null && op.index !== false) {\n                const paths = op.index === null || op.index === undefined\n                    ? this._indexConfig.__tokenizedFields ?? []\n                    : op.index.map((ix) => [ix, (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.tokenizePath)(ix)]);\n                for (const [path, field] of paths) {\n                    const texts = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getTextAtPath)(op.value, field);\n                    if (texts.length) {\n                        if (texts.length > 1) {\n                            texts.forEach((text, i) => {\n                                if (!toEmbed[text])\n                                    toEmbed[text] = [];\n                                toEmbed[text].push([op.namespace, op.key, `${path}.${i}`]);\n                            });\n                        }\n                        else {\n                            if (!toEmbed[texts[0]])\n                                toEmbed[texts[0]] = [];\n                            toEmbed[texts[0]].push([op.namespace, op.key, path]);\n                        }\n                    }\n                }\n            }\n        }\n        return toEmbed;\n    }\n    insertVectors(texts, embeddings) {\n        for (const [text, metadata] of Object.entries(texts)) {\n            const embedding = embeddings.shift();\n            if (!embedding) {\n                throw new Error(`No embedding found for text: ${text}`);\n            }\n            for (const [namespace, key, field] of metadata) {\n                const namespaceKey = namespace.join(\":\");\n                if (!this.vectors.has(namespaceKey)) {\n                    this.vectors.set(namespaceKey, new Map());\n                }\n                const namespaceMap = this.vectors.get(namespaceKey);\n                if (!namespaceMap.has(key)) {\n                    namespaceMap.set(key, new Map());\n                }\n                const itemMap = namespaceMap.get(key);\n                itemMap.set(field, embedding);\n            }\n        }\n    }\n    getVectors(item) {\n        const namespaceKey = item.namespace.join(\":\");\n        const itemKey = item.key;\n        if (!this.vectors.has(namespaceKey)) {\n            return [];\n        }\n        const namespaceMap = this.vectors.get(namespaceKey);\n        if (!namespaceMap.has(itemKey)) {\n            return [];\n        }\n        const itemMap = namespaceMap.get(itemKey);\n        const vectors = Array.from(itemMap.values());\n        if (!vectors.length) {\n            return [];\n        }\n        return vectors;\n    }\n    cosineSimilarity(X, Y) {\n        if (!Y.length)\n            return [];\n        // Calculate dot products for all vectors at once\n        const dotProducts = Y.map((vector) => vector.reduce((acc, val, i) => acc + val * X[i], 0));\n        // Calculate magnitudes\n        const magnitude1 = Math.sqrt(X.reduce((acc, val) => acc + val * val, 0));\n        const magnitudes2 = Y.map((vector) => Math.sqrt(vector.reduce((acc, val) => acc + val * val, 0)));\n        // Calculate similarities\n        return dotProducts.map((dot, i) => {\n            const magnitude2 = magnitudes2[i];\n            return magnitude1 && magnitude2 ? dot / (magnitude1 * magnitude2) : 0;\n        });\n    }\n    get indexConfig() {\n        return this._indexConfig;\n    }\n}\n/** @deprecated Alias for InMemoryStore */\nclass MemoryStore extends InMemoryStore {\n}\n//# sourceMappingURL=memory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/memory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/utils.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/utils.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compareValues: () => (/* binding */ compareValues),\n/* harmony export */   cosineSimilarity: () => (/* binding */ cosineSimilarity),\n/* harmony export */   getTextAtPath: () => (/* binding */ getTextAtPath),\n/* harmony export */   tokenizePath: () => (/* binding */ tokenizePath)\n/* harmony export */ });\n/**\n * Tokenize a JSON path into parts.\n * @example\n * tokenizePath(\"metadata.title\") // -> [\"metadata\", \"title\"]\n * tokenizePath(\"chapters[*].content\") // -> [\"chapters[*]\", \"content\"]\n */\nfunction tokenizePath(path) {\n    if (!path) {\n        return [];\n    }\n    const tokens = [];\n    let current = [];\n    let i = 0;\n    while (i < path.length) {\n        const char = path[i];\n        if (char === \"[\") {\n            // Handle array index\n            if (current.length) {\n                tokens.push(current.join(\"\"));\n                current = [];\n            }\n            let bracketCount = 1;\n            const indexChars = [\"[\"];\n            i += 1;\n            while (i < path.length && bracketCount > 0) {\n                if (path[i] === \"[\") {\n                    bracketCount += 1;\n                }\n                else if (path[i] === \"]\") {\n                    bracketCount -= 1;\n                }\n                indexChars.push(path[i]);\n                i += 1;\n            }\n            tokens.push(indexChars.join(\"\"));\n            continue;\n        }\n        else if (char === \"{\") {\n            // Handle multi-field selection\n            if (current.length) {\n                tokens.push(current.join(\"\"));\n                current = [];\n            }\n            let braceCount = 1;\n            const fieldChars = [\"{\"];\n            i += 1;\n            while (i < path.length && braceCount > 0) {\n                if (path[i] === \"{\") {\n                    braceCount += 1;\n                }\n                else if (path[i] === \"}\") {\n                    braceCount -= 1;\n                }\n                fieldChars.push(path[i]);\n                i += 1;\n            }\n            tokens.push(fieldChars.join(\"\"));\n            continue;\n        }\n        else if (char === \".\") {\n            // Handle regular field\n            if (current.length) {\n                tokens.push(current.join(\"\"));\n                current = [];\n            }\n        }\n        else {\n            current.push(char);\n        }\n        i += 1;\n    }\n    if (current.length) {\n        tokens.push(current.join(\"\"));\n    }\n    return tokens;\n}\n/**\n * Type guard to check if an object is a FilterOperators\n */\nfunction isFilterOperators(obj) {\n    return (typeof obj === \"object\" &&\n        obj !== null &&\n        Object.keys(obj).every((key) => key === \"$eq\" ||\n            key === \"$ne\" ||\n            key === \"$gt\" ||\n            key === \"$gte\" ||\n            key === \"$lt\" ||\n            key === \"$lte\" ||\n            key === \"$in\" ||\n            key === \"$nin\"));\n}\n/**\n * Compare values for filtering, supporting operator-based comparisons.\n */\nfunction compareValues(itemValue, filterValue) {\n    if (isFilterOperators(filterValue)) {\n        const operators = Object.keys(filterValue).filter((k) => k.startsWith(\"$\"));\n        return operators.every((op) => {\n            const value = filterValue[op];\n            switch (op) {\n                case \"$eq\":\n                    return itemValue === value;\n                case \"$ne\":\n                    return itemValue !== value;\n                case \"$gt\":\n                    return Number(itemValue) > Number(value);\n                case \"$gte\":\n                    return Number(itemValue) >= Number(value);\n                case \"$lt\":\n                    return Number(itemValue) < Number(value);\n                case \"$lte\":\n                    return Number(itemValue) <= Number(value);\n                case \"$in\":\n                    return Array.isArray(value) ? value.includes(itemValue) : false;\n                case \"$nin\":\n                    return Array.isArray(value) ? !value.includes(itemValue) : true;\n                default:\n                    return false;\n            }\n        });\n    }\n    // If no operators, do a direct comparison\n    return itemValue === filterValue;\n}\n/**\n * Extract text from a value at a specific JSON path.\n *\n * Supports:\n * - Simple paths: \"field1.field2\"\n * - Array indexing: \"[0]\", \"[*]\", \"[-1]\"\n * - Wildcards: \"*\"\n * - Multi-field selection: \"{field1,field2}\"\n * - Nested paths in multi-field: \"{field1,nested.field2}\"\n */\nfunction getTextAtPath(obj, path) {\n    if (!path || path === \"$\") {\n        return [JSON.stringify(obj, null, 2)];\n    }\n    const tokens = Array.isArray(path) ? path : tokenizePath(path);\n    function extractFromObj(obj, tokens, pos) {\n        if (pos >= tokens.length) {\n            if (typeof obj === \"string\" ||\n                typeof obj === \"number\" ||\n                typeof obj === \"boolean\") {\n                return [String(obj)];\n            }\n            if (obj === null || obj === undefined) {\n                return [];\n            }\n            if (Array.isArray(obj) || typeof obj === \"object\") {\n                return [JSON.stringify(obj, null, 2)];\n            }\n            return [];\n        }\n        const token = tokens[pos];\n        const results = [];\n        if (pos === 0 && token === \"$\") {\n            results.push(JSON.stringify(obj, null, 2));\n        }\n        if (token.startsWith(\"[\") && token.endsWith(\"]\")) {\n            if (!Array.isArray(obj))\n                return [];\n            const index = token.slice(1, -1);\n            if (index === \"*\") {\n                for (const item of obj) {\n                    results.push(...extractFromObj(item, tokens, pos + 1));\n                }\n            }\n            else {\n                try {\n                    let idx = parseInt(index, 10);\n                    if (idx < 0) {\n                        idx = obj.length + idx;\n                    }\n                    if (idx >= 0 && idx < obj.length) {\n                        results.push(...extractFromObj(obj[idx], tokens, pos + 1));\n                    }\n                }\n                catch {\n                    return [];\n                }\n            }\n        }\n        else if (token.startsWith(\"{\") && token.endsWith(\"}\")) {\n            if (typeof obj !== \"object\" || obj === null)\n                return [];\n            const fields = token\n                .slice(1, -1)\n                .split(\",\")\n                .map((f) => f.trim());\n            for (const field of fields) {\n                const nestedTokens = tokenizePath(field);\n                if (nestedTokens.length) {\n                    let currentObj = obj;\n                    for (const nestedToken of nestedTokens) {\n                        if (currentObj &&\n                            typeof currentObj === \"object\" &&\n                            nestedToken in currentObj) {\n                            currentObj = currentObj[nestedToken];\n                        }\n                        else {\n                            currentObj = undefined;\n                            break;\n                        }\n                    }\n                    if (currentObj !== undefined) {\n                        if (typeof currentObj === \"string\" ||\n                            typeof currentObj === \"number\" ||\n                            typeof currentObj === \"boolean\") {\n                            results.push(String(currentObj));\n                        }\n                        else if (Array.isArray(currentObj) ||\n                            typeof currentObj === \"object\") {\n                            results.push(JSON.stringify(currentObj, null, 2));\n                        }\n                    }\n                }\n            }\n        }\n        else if (token === \"*\") {\n            if (Array.isArray(obj)) {\n                for (const item of obj) {\n                    results.push(...extractFromObj(item, tokens, pos + 1));\n                }\n            }\n            else if (typeof obj === \"object\" && obj !== null) {\n                for (const value of Object.values(obj)) {\n                    results.push(...extractFromObj(value, tokens, pos + 1));\n                }\n            }\n        }\n        else {\n            if (typeof obj === \"object\" && obj !== null && token in obj) {\n                results.push(...extractFromObj(obj[token], tokens, pos + 1));\n            }\n        }\n        return results;\n    }\n    return extractFromObj(obj, tokens, 0);\n}\n/**\n * Calculate cosine similarity between two vectors.\n */\nfunction cosineSimilarity(vector1, vector2) {\n    if (vector1.length !== vector2.length) {\n        throw new Error(\"Vectors must have the same length\");\n    }\n    const dotProduct = vector1.reduce((acc, val, i) => acc + val * vector2[i], 0);\n    const magnitude1 = Math.sqrt(vector1.reduce((acc, val) => acc + val * val, 0));\n    const magnitude2 = Math.sqrt(vector2.reduce((acc, val) => acc + val * val, 0));\n    if (magnitude1 === 0 || magnitude2 === 0)\n        return 0;\n    return dotProduct / (magnitude1 * magnitude2);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/store/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/types.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/types.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1jaGVja3BvaW50L2Rpc3QvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkc6XFxTdHVkeVxcUHl0aG9uXFxiYWNrdXBcXEFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxhbmdjaGFpbitsYW5nZ3JhcGgtY2hlY2twXzQ2YWM3OGExYTkzMzY3MDM5ZDY5OGVkODIyZmRiYTY1XFxub2RlX21vZHVsZXNcXEBsYW5nY2hhaW5cXGxhbmdncmFwaC1jaGVja3BvaW50XFxkaXN0XFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/index.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AsyncBatchedStore: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.AsyncBatchedStore),
/* harmony export */   BaseCheckpointSaver: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.BaseCheckpointSaver),
/* harmony export */   BaseStore: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.BaseStore),
/* harmony export */   ERROR: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.ERROR),
/* harmony export */   INTERRUPT: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.INTERRUPT),
/* harmony export */   InMemoryStore: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.InMemoryStore),
/* harmony export */   InvalidNamespaceError: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.InvalidNamespaceError),
/* harmony export */   MemorySaver: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.MemorySaver),
/* harmony export */   MemoryStore: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.MemoryStore),
/* harmony export */   RESUME: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.RESUME),
/* harmony export */   SCHEDULED: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.SCHEDULED),
/* harmony export */   TASKS: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.TASKS),
/* harmony export */   WRITES_IDX_MAP: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.WRITES_IDX_MAP),
/* harmony export */   compareChannelVersions: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.compareChannelVersions),
/* harmony export */   copyCheckpoint: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.copyCheckpoint),
/* harmony export */   deepCopy: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.deepCopy),
/* harmony export */   emptyCheckpoint: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.emptyCheckpoint),
/* harmony export */   getCheckpointId: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.getCheckpointId),
/* harmony export */   getTextAtPath: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.getTextAtPath),
/* harmony export */   maxChannelVersion: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.maxChannelVersion),
/* harmony export */   tokenizePath: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.tokenizePath),
/* harmony export */   uuid5: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.uuid5),
/* harmony export */   uuid6: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.uuid6)
/* harmony export */ });
/* harmony import */ var _dist_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/index.js */ "(ssr)/./node_modules/.pnpm/@langchain+langgraph-checkp_46ac78a1a93367039d698ed822fdba65/node_modules/@langchain/langgraph-checkpoint/dist/index.js");


/***/ })

};
;