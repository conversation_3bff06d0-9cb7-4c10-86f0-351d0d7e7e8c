"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_kumir"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kumir.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kumir.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = kumir\nkumir.displayName = 'kumir'\nkumir.aliases = ['kum']\nfunction kumir(Prism) {\n  /* eslint-disable regexp/no-dupe-characters-character-class */\n  ;(function (Prism) {\n    /**\n     * Regular expression for characters that are not allowed in identifiers.\n     *\n     * @type {string}\n     */\n    var nonId = /\\s\\x00-\\x1f\\x22-\\x2f\\x3a-\\x3f\\x5b-\\x5e\\x60\\x7b-\\x7e/.source\n    /**\n     * Surround a regular expression for IDs with patterns for non-ID sequences.\n     *\n     * @param {string} pattern A regular expression for identifiers.\n     * @param {string} [flags] The regular expression flags.\n     * @returns {RegExp} A wrapped regular expression for identifiers.\n     */\n    function wrapId(pattern, flags) {\n      return RegExp(pattern.replace(/<nonId>/g, nonId), flags)\n    }\n    Prism.languages.kumir = {\n      comment: {\n        pattern: /\\|.*/\n      },\n      prolog: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\\n\\r\"]*\"|'[^\\n\\r']*'/,\n        greedy: true\n      },\n      boolean: {\n        pattern: wrapId(/(^|[<nonId>])(?:да|нет)(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      'operator-word': {\n        pattern: wrapId(/(^|[<nonId>])(?:и|или|не)(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'system-variable': {\n        pattern: wrapId(/(^|[<nonId>])знач(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      type: [\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:вещ|лит|лог|сим|цел)(?:\\x20*таб)?(?=[<nonId>]|$)/\n              .source\n          ),\n          lookbehind: true,\n          alias: 'builtin'\n        },\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:компл|сканкод|файл|цвет)(?=[<nonId>]|$)/.source\n          ),\n          lookbehind: true,\n          alias: 'important'\n        }\n      ],\n      /**\n       * Should be performed after searching for type names because of \"таб\".\n       * \"таб\" is a reserved word, but never used without a preceding type name.\n       * \"НАЗНАЧИТЬ\", \"Фввод\", and \"Фвывод\" are not reserved words.\n       */\n      keyword: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:алг|арг(?:\\x20*рез)?|ввод|ВКЛЮЧИТЬ|вс[её]|выбор|вывод|выход|дано|для|до|дс|если|иначе|исп|использовать|кон(?:(?:\\x20+|_)исп)?|кц(?:(?:\\x20+|_)при)?|надо|нач|нс|нц|от|пауза|пока|при|раза?|рез|стоп|таб|то|утв|шаг)(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for reserved words. */\n      name: {\n        // eslint-disable-next-line regexp/no-super-linear-backtracking\n        pattern: wrapId(\n          /(^|[<nonId>])[^\\d<nonId>][^<nonId>]*(?:\\x20+[^<nonId>]+)*(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for names. */\n      number: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:\\B\\$[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?=[<nonId>]|$)/\n            .source,\n          'i'\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for words. */\n      punctuation: /:=|[(),:;\\[\\]]/,\n      /**\n       * Should be performed after searching for\n       * - numeric constants (because of \"+\" and \"-\");\n       * - punctuation marks (because of \":=\" and \"=\").\n       */\n      'operator-char': {\n        pattern: /\\*\\*?|<[=>]?|>=?|[-+/=]/,\n        alias: 'operator'\n      }\n    }\n    Prism.languages.kum = Prism.languages.kumir\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kumir.js\n"));

/***/ })

}]);