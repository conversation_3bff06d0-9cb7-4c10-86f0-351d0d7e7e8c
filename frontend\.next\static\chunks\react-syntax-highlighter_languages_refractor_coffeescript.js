"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_coffeescript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coffeescript.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coffeescript.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = coffeescript\ncoffeescript.displayName = 'coffeescript'\ncoffeescript.aliases = ['coffee']\nfunction coffeescript(Prism) {\n  ;(function (Prism) {\n    // Ignore comments starting with { to privilege string interpolation highlighting\n    var comment = /#(?!\\{).+/\n    var interpolation = {\n      pattern: /#\\{[^}]+\\}/,\n      alias: 'variable'\n    }\n    Prism.languages.coffeescript = Prism.languages.extend('javascript', {\n      comment: comment,\n      string: [\n        // Strings are multiline\n        {\n          pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n          greedy: true\n        },\n        {\n          // Strings are multiline\n          pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n          greedy: true,\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ],\n      keyword:\n        /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\n      'class-member': {\n        pattern: /@(?!\\d)\\w+/,\n        alias: 'variable'\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'comment', {\n      'multiline-comment': {\n        pattern: /###[\\s\\S]+?###/,\n        alias: 'comment'\n      },\n      // Block regexp can contain comments and interpolation\n      'block-regex': {\n        pattern: /\\/{3}[\\s\\S]*?\\/{3}/,\n        alias: 'regex',\n        inside: {\n          comment: comment,\n          interpolation: interpolation\n        }\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'string', {\n      'inline-javascript': {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        inside: {\n          delimiter: {\n            pattern: /^`|`$/,\n            alias: 'punctuation'\n          },\n          script: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-javascript',\n            inside: Prism.languages.javascript\n          }\n        }\n      },\n      // Block strings\n      'multiline-string': [\n        {\n          pattern: /'''[\\s\\S]*?'''/,\n          greedy: true,\n          alias: 'string'\n        },\n        {\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ]\n    })\n    Prism.languages.insertBefore('coffeescript', 'keyword', {\n      // Object property\n      property: /(?!\\d)\\w+(?=\\s*:(?!:))/\n    })\n    delete Prism.languages.coffeescript['template-string']\n    Prism.languages.coffee = Prism.languages.coffeescript\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWZyYWN0b3JAMy42LjAvbm9kZV9tb2R1bGVzL3JlZnJhY3Rvci9sYW5nL2NvZmZlZXNjcmlwdC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCx1Q0FBdUM7QUFDdkMseUJBQXlCO0FBQ3pCO0FBQ0EsbUJBQW1CLEdBQUcsSUFBSTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxxQkFBcUIsRUFBRSxXQUFXLEVBQUU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJHOlxcU3R1ZHlcXFB5dGhvblxcYmFja3VwXFxBZ2VudFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZnJhY3RvckAzLjYuMFxcbm9kZV9tb2R1bGVzXFxyZWZyYWN0b3JcXGxhbmdcXGNvZmZlZXNjcmlwdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBjb2ZmZWVzY3JpcHRcbmNvZmZlZXNjcmlwdC5kaXNwbGF5TmFtZSA9ICdjb2ZmZWVzY3JpcHQnXG5jb2ZmZWVzY3JpcHQuYWxpYXNlcyA9IFsnY29mZmVlJ11cbmZ1bmN0aW9uIGNvZmZlZXNjcmlwdChQcmlzbSkge1xuICA7KGZ1bmN0aW9uIChQcmlzbSkge1xuICAgIC8vIElnbm9yZSBjb21tZW50cyBzdGFydGluZyB3aXRoIHsgdG8gcHJpdmlsZWdlIHN0cmluZyBpbnRlcnBvbGF0aW9uIGhpZ2hsaWdodGluZ1xuICAgIHZhciBjb21tZW50ID0gLyMoPyFcXHspLisvXG4gICAgdmFyIGludGVycG9sYXRpb24gPSB7XG4gICAgICBwYXR0ZXJuOiAvI1xce1tefV0rXFx9LyxcbiAgICAgIGFsaWFzOiAndmFyaWFibGUnXG4gICAgfVxuICAgIFByaXNtLmxhbmd1YWdlcy5jb2ZmZWVzY3JpcHQgPSBQcmlzbS5sYW5ndWFnZXMuZXh0ZW5kKCdqYXZhc2NyaXB0Jywge1xuICAgICAgY29tbWVudDogY29tbWVudCxcbiAgICAgIHN0cmluZzogW1xuICAgICAgICAvLyBTdHJpbmdzIGFyZSBtdWx0aWxpbmVcbiAgICAgICAge1xuICAgICAgICAgIHBhdHRlcm46IC8nKD86XFxcXFtcXHNcXFNdfFteXFxcXCddKSonLyxcbiAgICAgICAgICBncmVlZHk6IHRydWVcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIC8vIFN0cmluZ3MgYXJlIG11bHRpbGluZVxuICAgICAgICAgIHBhdHRlcm46IC9cIig/OlxcXFxbXFxzXFxTXXxbXlxcXFxcIl0pKlwiLyxcbiAgICAgICAgICBncmVlZHk6IHRydWUsXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICBpbnRlcnBvbGF0aW9uOiBpbnRlcnBvbGF0aW9uXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAga2V5d29yZDpcbiAgICAgICAgL1xcYig/OmFuZHxicmVha3xieXxjYXRjaHxjbGFzc3xjb250aW51ZXxkZWJ1Z2dlcnxkZWxldGV8ZG98ZWFjaHxlbHNlfGV4dGVuZHxleHRlbmRzfGZhbHNlfGZpbmFsbHl8Zm9yfGlmfGlufGluc3RhbmNlb2Z8aXN8aXNudHxsZXR8bG9vcHxuYW1lc3BhY2V8bmV3fG5vfG5vdHxudWxsfG9mfG9mZnxvbnxvcnxvd258cmV0dXJufHN1cGVyfHN3aXRjaHx0aGVufHRoaXN8dGhyb3d8dHJ1ZXx0cnl8dHlwZW9mfHVuZGVmaW5lZHx1bmxlc3N8dW50aWx8d2hlbnx3aGlsZXx3aW5kb3d8d2l0aHx5ZXN8eWllbGQpXFxiLyxcbiAgICAgICdjbGFzcy1tZW1iZXInOiB7XG4gICAgICAgIHBhdHRlcm46IC9AKD8hXFxkKVxcdysvLFxuICAgICAgICBhbGlhczogJ3ZhcmlhYmxlJ1xuICAgICAgfVxuICAgIH0pXG4gICAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnY29mZmVlc2NyaXB0JywgJ2NvbW1lbnQnLCB7XG4gICAgICAnbXVsdGlsaW5lLWNvbW1lbnQnOiB7XG4gICAgICAgIHBhdHRlcm46IC8jIyNbXFxzXFxTXSs/IyMjLyxcbiAgICAgICAgYWxpYXM6ICdjb21tZW50J1xuICAgICAgfSxcbiAgICAgIC8vIEJsb2NrIHJlZ2V4cCBjYW4gY29udGFpbiBjb21tZW50cyBhbmQgaW50ZXJwb2xhdGlvblxuICAgICAgJ2Jsb2NrLXJlZ2V4Jzoge1xuICAgICAgICBwYXR0ZXJuOiAvXFwvezN9W1xcc1xcU10qP1xcL3szfS8sXG4gICAgICAgIGFsaWFzOiAncmVnZXgnLFxuICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICBjb21tZW50OiBjb21tZW50LFxuICAgICAgICAgIGludGVycG9sYXRpb246IGludGVycG9sYXRpb25cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG4gICAgUHJpc20ubGFuZ3VhZ2VzLmluc2VydEJlZm9yZSgnY29mZmVlc2NyaXB0JywgJ3N0cmluZycsIHtcbiAgICAgICdpbmxpbmUtamF2YXNjcmlwdCc6IHtcbiAgICAgICAgcGF0dGVybjogL2AoPzpcXFxcW1xcc1xcU118W15cXFxcYF0pKmAvLFxuICAgICAgICBpbnNpZGU6IHtcbiAgICAgICAgICBkZWxpbWl0ZXI6IHtcbiAgICAgICAgICAgIHBhdHRlcm46IC9eYHxgJC8sXG4gICAgICAgICAgICBhbGlhczogJ3B1bmN0dWF0aW9uJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgc2NyaXB0OiB7XG4gICAgICAgICAgICBwYXR0ZXJuOiAvW1xcc1xcU10rLyxcbiAgICAgICAgICAgIGFsaWFzOiAnbGFuZ3VhZ2UtamF2YXNjcmlwdCcsXG4gICAgICAgICAgICBpbnNpZGU6IFByaXNtLmxhbmd1YWdlcy5qYXZhc2NyaXB0XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgLy8gQmxvY2sgc3RyaW5nc1xuICAgICAgJ211bHRpbGluZS1zdHJpbmcnOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBwYXR0ZXJuOiAvJycnW1xcc1xcU10qPycnJy8sXG4gICAgICAgICAgZ3JlZWR5OiB0cnVlLFxuICAgICAgICAgIGFsaWFzOiAnc3RyaW5nJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgcGF0dGVybjogL1wiXCJcIltcXHNcXFNdKj9cIlwiXCIvLFxuICAgICAgICAgIGdyZWVkeTogdHJ1ZSxcbiAgICAgICAgICBhbGlhczogJ3N0cmluZycsXG4gICAgICAgICAgaW5zaWRlOiB7XG4gICAgICAgICAgICBpbnRlcnBvbGF0aW9uOiBpbnRlcnBvbGF0aW9uXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdXG4gICAgfSlcbiAgICBQcmlzbS5sYW5ndWFnZXMuaW5zZXJ0QmVmb3JlKCdjb2ZmZWVzY3JpcHQnLCAna2V5d29yZCcsIHtcbiAgICAgIC8vIE9iamVjdCBwcm9wZXJ0eVxuICAgICAgcHJvcGVydHk6IC8oPyFcXGQpXFx3Kyg/PVxccyo6KD8hOikpL1xuICAgIH0pXG4gICAgZGVsZXRlIFByaXNtLmxhbmd1YWdlcy5jb2ZmZWVzY3JpcHRbJ3RlbXBsYXRlLXN0cmluZyddXG4gICAgUHJpc20ubGFuZ3VhZ2VzLmNvZmZlZSA9IFByaXNtLmxhbmd1YWdlcy5jb2ZmZWVzY3JpcHRcbiAgfSkoUHJpc20pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/coffeescript.js\n"));

/***/ })

}]);