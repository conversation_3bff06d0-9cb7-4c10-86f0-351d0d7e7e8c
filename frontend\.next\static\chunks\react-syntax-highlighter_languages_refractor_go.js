"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_go"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/go.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/go.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = go\ngo.displayName = 'go'\ngo.aliases = []\nfunction go(Prism) {\n  Prism.languages.go = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n    boolean: /\\b(?:_|false|iota|nil|true)\\b/,\n    number: [\n      // binary and octal integers\n      /\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i, // hexadecimal integers and floats\n      /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i, // decimal integers and floats\n      /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i\n    ],\n    operator:\n      /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n    builtin:\n      /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n  })\n  Prism.languages.insertBefore('go', 'string', {\n    char: {\n      pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n      greedy: true\n    }\n  })\n  delete Prism.languages.go['class-name']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/go.js\n"));

/***/ })

}]);