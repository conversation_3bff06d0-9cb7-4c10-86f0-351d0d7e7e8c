"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_rip"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/rip.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/rip.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = rip\nrip.displayName = 'rip'\nrip.aliases = []\nfunction rip(Prism) {\n  Prism.languages.rip = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\B`[^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]\\b/,\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    regex: {\n      pattern:\n        /(^|[^/])\\/(?!\\/)(?:\\[[^\\n\\r\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/(?=\\s*(?:$|[\\r\\n,.;})]))/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /(?:=>|->)|\\b(?:case|catch|class|else|exit|finally|if|raise|return|switch|try)\\b/,\n    builtin: /@|\\bSystem\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    date: /\\b\\d{4}-\\d{2}-\\d{2}\\b/,\n    time: /\\b\\d{2}:\\d{2}:\\d{2}\\b/,\n    datetime: /\\b\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\b/,\n    symbol: /:[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/,\n    number: /[+-]?\\b(?:\\d+\\.\\d+|\\d+)\\b/,\n    punctuation: /(?:\\.{2,3})|[`,.:;=\\/\\\\()<>\\[\\]{}]/,\n    reference: /[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/rip.js\n"));

/***/ })

}]);