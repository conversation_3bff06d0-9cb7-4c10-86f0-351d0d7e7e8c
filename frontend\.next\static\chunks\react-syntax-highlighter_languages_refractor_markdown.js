"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_markdown"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markdown.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markdown.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = markdown\nmarkdown.displayName = 'markdown'\nmarkdown.aliases = ['md']\nfunction markdown(Prism) {\n  ;(function (Prism) {\n    // Allow only one line break\n    var inner = /(?:\\\\.|[^\\\\\\n\\r]|(?:\\n|\\r\\n?)(?![\\r\\n]))/.source\n    /**\n     * This function is intended for the creation of the bold or italic pattern.\n     *\n     * This also adds a lookbehind group to the given pattern to ensure that the pattern is not backslash-escaped.\n     *\n     * _Note:_ Keep in mind that this adds a capturing group.\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function createInline(pattern) {\n      pattern = pattern.replace(/<inner>/g, function () {\n        return inner\n      })\n      return RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + '(?:' + pattern + ')')\n    }\n    var tableCell = /(?:\\\\.|``(?:[^`\\r\\n]|`(?!`))+``|`[^`\\r\\n]+`|[^\\\\|\\r\\n`])+/\n      .source\n    var tableRow =\n      /\\|?__(?:\\|__)+\\|?(?:(?:\\n|\\r\\n?)|(?![\\s\\S]))/.source.replace(\n        /__/g,\n        function () {\n          return tableCell\n        }\n      )\n    var tableLine =\n      /\\|?[ \\t]*:?-{3,}:?[ \\t]*(?:\\|[ \\t]*:?-{3,}:?[ \\t]*)+\\|?(?:\\n|\\r\\n?)/\n        .source\n    Prism.languages.markdown = Prism.languages.extend('markup', {})\n    Prism.languages.insertBefore('markdown', 'prolog', {\n      'front-matter-block': {\n        pattern: /(^(?:\\s*[\\r\\n])?)---(?!.)[\\s\\S]*?[\\r\\n]---(?!.)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^---|---$/,\n          'front-matter': {\n            pattern: /\\S+(?:\\s+\\S+)*/,\n            alias: ['yaml', 'language-yaml'],\n            inside: Prism.languages.yaml\n          }\n        }\n      },\n      blockquote: {\n        // > ...\n        pattern: /^>(?:[\\t ]*>)*/m,\n        alias: 'punctuation'\n      },\n      table: {\n        pattern: RegExp(\n          '^' + tableRow + tableLine + '(?:' + tableRow + ')*',\n          'm'\n        ),\n        inside: {\n          'table-data-rows': {\n            pattern: RegExp(\n              '^(' + tableRow + tableLine + ')(?:' + tableRow + ')*$'\n            ),\n            lookbehind: true,\n            inside: {\n              'table-data': {\n                pattern: RegExp(tableCell),\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          },\n          'table-line': {\n            pattern: RegExp('^(' + tableRow + ')' + tableLine + '$'),\n            lookbehind: true,\n            inside: {\n              punctuation: /\\||:?-{3,}:?/\n            }\n          },\n          'table-header-row': {\n            pattern: RegExp('^' + tableRow + '$'),\n            inside: {\n              'table-header': {\n                pattern: RegExp(tableCell),\n                alias: 'important',\n                inside: Prism.languages.markdown\n              },\n              punctuation: /\\|/\n            }\n          }\n        }\n      },\n      code: [\n        {\n          // Prefixed by 4 spaces or 1 tab and preceded by an empty line\n          pattern:\n            /((?:^|\\n)[ \\t]*\\n|(?:^|\\r\\n?)[ \\t]*\\r\\n?)(?: {4}|\\t).+(?:(?:\\n|\\r\\n?)(?: {4}|\\t).+)*/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        {\n          // ```optional language\n          // code block\n          // ```\n          pattern: /^```[\\s\\S]*?^```$/m,\n          greedy: true,\n          inside: {\n            'code-block': {\n              pattern: /^(```.*(?:\\n|\\r\\n?))[\\s\\S]+?(?=(?:\\n|\\r\\n?)^```$)/m,\n              lookbehind: true\n            },\n            'code-language': {\n              pattern: /^(```).+/,\n              lookbehind: true\n            },\n            punctuation: /```/\n          }\n        }\n      ],\n      title: [\n        {\n          // title 1\n          // =======\n          // title 2\n          // -------\n          pattern: /\\S.*(?:\\n|\\r\\n?)(?:==+|--+)(?=[ \\t]*$)/m,\n          alias: 'important',\n          inside: {\n            punctuation: /==+$|--+$/\n          }\n        },\n        {\n          // # title 1\n          // ###### title 6\n          pattern: /(^\\s*)#.+/m,\n          lookbehind: true,\n          alias: 'important',\n          inside: {\n            punctuation: /^#+|#+$/\n          }\n        }\n      ],\n      hr: {\n        // ***\n        // ---\n        // * * *\n        // -----------\n        pattern: /(^\\s*)([*-])(?:[\\t ]*\\2){2,}(?=\\s*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      list: {\n        // * item\n        // + item\n        // - item\n        // 1. item\n        pattern: /(^\\s*)(?:[*+-]|\\d+\\.)(?=[\\t ].)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      'url-reference': {\n        // [id]: http://example.com \"Optional title\"\n        // [id]: http://example.com 'Optional title'\n        // [id]: http://example.com (Optional title)\n        // [id]: <http://example.com> \"Optional title\"\n        pattern:\n          /!?\\[[^\\]]+\\]:[\\t ]+(?:\\S+|<(?:\\\\.|[^>\\\\])+>)(?:[\\t ]+(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\)))?/,\n        inside: {\n          variable: {\n            pattern: /^(!?\\[)[^\\]]+/,\n            lookbehind: true\n          },\n          string:\n            /(?:\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|\\((?:\\\\.|[^)\\\\])*\\))$/,\n          punctuation: /^[\\[\\]!:]|[<>]/\n        },\n        alias: 'url'\n      },\n      bold: {\n        // **strong**\n        // __strong__\n        // allow one nested instance of italic text using the same delimiter\n        pattern: createInline(\n          /\\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\\b|\\*\\*(?:(?!\\*)<inner>|\\*(?:(?!\\*)<inner>)+\\*)+\\*\\*/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^..)[\\s\\S]+(?=..$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /\\*\\*|__/\n        }\n      },\n      italic: {\n        // *em*\n        // _em_\n        // allow one nested instance of bold text using the same delimiter\n        pattern: createInline(\n          /\\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\\b|\\*(?:(?!\\*)<inner>|\\*\\*(?:(?!\\*)<inner>)+\\*\\*)+\\*/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^.)[\\s\\S]+(?=.$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /[*_]/\n        }\n      },\n      strike: {\n        // ~~strike through~~\n        // ~strike~\n        // eslint-disable-next-line regexp/strict\n        pattern: createInline(/(~~?)(?:(?!~)<inner>)+\\2/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /(^~~?)[\\s\\S]+(?=\\1$)/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          punctuation: /~~?/\n        }\n      },\n      'code-snippet': {\n        // `code`\n        // ``code``\n        pattern:\n          /(^|[^\\\\`])(?:``[^`\\r\\n]+(?:`[^`\\r\\n]+)*``(?!`)|`[^`\\r\\n]+`(?!`))/,\n        lookbehind: true,\n        greedy: true,\n        alias: ['code', 'keyword']\n      },\n      url: {\n        // [example](http://example.com \"Optional title\")\n        // [example][id]\n        // [example] [id]\n        pattern: createInline(\n          /!?\\[(?:(?!\\])<inner>)+\\](?:\\([^\\s)]+(?:[\\t ]+\"(?:\\\\.|[^\"\\\\])*\")?\\)|[ \\t]?\\[(?:(?!\\])<inner>)+\\])/\n            .source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          operator: /^!/,\n          content: {\n            pattern: /(^\\[)[^\\]]+(?=\\])/,\n            lookbehind: true,\n            inside: {} // see below\n          },\n          variable: {\n            pattern: /(^\\][ \\t]?\\[)[^\\]]+(?=\\]$)/,\n            lookbehind: true\n          },\n          url: {\n            pattern: /(^\\]\\()[^\\s)]+/,\n            lookbehind: true\n          },\n          string: {\n            pattern: /(^[ \\t]+)\"(?:\\\\.|[^\"\\\\])*\"(?=\\)$)/,\n            lookbehind: true\n          }\n        }\n      }\n    })\n    ;['url', 'bold', 'italic', 'strike'].forEach(function (token) {\n      ;['url', 'bold', 'italic', 'strike', 'code-snippet'].forEach(function (\n        inside\n      ) {\n        if (token !== inside) {\n          Prism.languages.markdown[token].inside.content.inside[inside] =\n            Prism.languages.markdown[inside]\n        }\n      })\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'markdown' && env.language !== 'md') {\n        return\n      }\n      function walkTokens(tokens) {\n        if (!tokens || typeof tokens === 'string') {\n          return\n        }\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i]\n          if (token.type !== 'code') {\n            walkTokens(token.content)\n            continue\n          }\n          /*\n           * Add the correct `language-xxxx` class to this code block. Keep in mind that the `code-language` token\n           * is optional. But the grammar is defined so that there is only one case we have to handle:\n           *\n           * token.content = [\n           *     <span class=\"punctuation\">```</span>,\n           *     <span class=\"code-language\">xxxx</span>,\n           *     '\\n', // exactly one new lines (\\r or \\n or \\r\\n)\n           *     <span class=\"code-block\">...</span>,\n           *     '\\n', // exactly one new lines again\n           *     <span class=\"punctuation\">```</span>\n           * ];\n           */\n          var codeLang = token.content[1]\n          var codeBlock = token.content[3]\n          if (\n            codeLang &&\n            codeBlock &&\n            codeLang.type === 'code-language' &&\n            codeBlock.type === 'code-block' &&\n            typeof codeLang.content === 'string'\n          ) {\n            // this might be a language that Prism does not support\n            // do some replacements to support C++, C#, and F#\n            var lang = codeLang.content\n              .replace(/\\b#/g, 'sharp')\n              .replace(/\\b\\+\\+/g, 'pp') // only use the first word\n            lang = (/[a-z][\\w-]*/i.exec(lang) || [''])[0].toLowerCase()\n            var alias = 'language-' + lang // add alias\n            if (!codeBlock.alias) {\n              codeBlock.alias = [alias]\n            } else if (typeof codeBlock.alias === 'string') {\n              codeBlock.alias = [codeBlock.alias, alias]\n            } else {\n              codeBlock.alias.push(alias)\n            }\n          }\n        }\n      }\n      walkTokens(env.tokens)\n    })\n    Prism.hooks.add('wrap', function (env) {\n      if (env.type !== 'code-block') {\n        return\n      }\n      var codeLang = ''\n      for (var i = 0, l = env.classes.length; i < l; i++) {\n        var cls = env.classes[i]\n        var match = /language-(.+)/.exec(cls)\n        if (match) {\n          codeLang = match[1]\n          break\n        }\n      }\n      var grammar = Prism.languages[codeLang]\n      if (!grammar) {\n        if (codeLang && codeLang !== 'none' && Prism.plugins.autoloader) {\n          var id =\n            'md-' +\n            new Date().valueOf() +\n            '-' +\n            Math.floor(Math.random() * 1e16)\n          env.attributes['id'] = id\n          Prism.plugins.autoloader.loadLanguages(codeLang, function () {\n            var ele = document.getElementById(id)\n            if (ele) {\n              ele.innerHTML = Prism.highlight(\n                ele.textContent,\n                Prism.languages[codeLang],\n                codeLang\n              )\n            }\n          })\n        }\n      } else {\n        env.content = Prism.highlight(\n          textContent(env.content.value),\n          grammar,\n          codeLang\n        )\n      }\n    })\n    var tagPattern = RegExp(Prism.languages.markup.tag.pattern.source, 'gi')\n    /**\n     * A list of known entity names.\n     *\n     * This will always be incomplete to save space. The current list is the one used by lowdash's unescape function.\n     *\n     * @see {@link https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/unescape.js#L2}\n     */\n    var KNOWN_ENTITY_NAMES = {\n      amp: '&',\n      lt: '<',\n      gt: '>',\n      quot: '\"'\n    } // IE 11 doesn't support `String.fromCodePoint`\n    var fromCodePoint = String.fromCodePoint || String.fromCharCode\n    /**\n     * Returns the text content of a given HTML source code string.\n     *\n     * @param {string} html\n     * @returns {string}\n     */\n    function textContent(html) {\n      // remove all tags\n      var text = html.replace(tagPattern, '') // decode known entities\n      text = text.replace(/&(\\w{1,8}|#x?[\\da-f]{1,8});/gi, function (m, code) {\n        code = code.toLowerCase()\n        if (code[0] === '#') {\n          var value\n          if (code[1] === 'x') {\n            value = parseInt(code.slice(2), 16)\n          } else {\n            value = Number(code.slice(1))\n          }\n          return fromCodePoint(value)\n        } else {\n          var known = KNOWN_ENTITY_NAMES[code]\n          if (known) {\n            return known\n          } // unable to decode\n          return m\n        }\n      })\n      return text\n    }\n    Prism.languages.md = Prism.languages.markdown\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/markdown.js\n"));

/***/ })

}]);