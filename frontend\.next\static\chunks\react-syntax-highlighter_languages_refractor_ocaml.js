"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_ocaml"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ocaml.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ocaml.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = ocaml\nocaml.displayName = 'ocaml'\nocaml.aliases = []\nfunction ocaml(Prism) {\n  // https://ocaml.org/manual/lex.html\n  Prism.languages.ocaml = {\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)/,\n      greedy: true\n    },\n    char: {\n      pattern: /'(?:[^\\\\\\r\\n']|\\\\(?:.|[ox]?[0-9a-f]{1,3}))'/i,\n      greedy: true\n    },\n    string: [\n      {\n        pattern: /\"(?:\\\\(?:[\\s\\S]|\\r\\n)|[^\\\\\\r\\n\"])*\"/,\n        greedy: true\n      },\n      {\n        pattern: /\\{([a-z_]*)\\|[\\s\\S]*?\\|\\1\\}/,\n        greedy: true\n      }\n    ],\n    number: [\n      // binary and octal\n      /\\b(?:0b[01][01_]*|0o[0-7][0-7_]*)\\b/i, // hexadecimal\n      /\\b0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]*)?(?:p[+-]?\\d[\\d_]*)?(?!\\w)/i, // decimal\n      /\\b\\d[\\d_]*(?:\\.[\\d_]*)?(?:e[+-]?\\d[\\d_]*)?(?!\\w)/i\n    ],\n    directive: {\n      pattern: /\\B#\\w+/,\n      alias: 'property'\n    },\n    label: {\n      pattern: /\\B~\\w+/,\n      alias: 'property'\n    },\n    'type-variable': {\n      pattern: /\\B'\\w+/,\n      alias: 'function'\n    },\n    variant: {\n      pattern: /`\\w+/,\n      alias: 'symbol'\n    },\n    // For the list of keywords and operators,\n    // see: http://caml.inria.fr/pub/docs/manual-ocaml/lex.html#sec84\n    keyword:\n      /\\b(?:as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|match|method|module|mutable|new|nonrec|object|of|open|private|rec|sig|struct|then|to|try|type|val|value|virtual|when|where|while|with)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    'operator-like-punctuation': {\n      pattern: /\\[[<>|]|[>|]\\]|\\{<|>\\}/,\n      alias: 'punctuation'\n    },\n    // Custom operators are allowed\n    operator:\n      /\\.[.~]|:[=>]|[=<>@^|&+\\-*\\/$%!?~][!$%&*+\\-.\\/:<=>?@^|~]*|\\b(?:and|asr|land|lor|lsl|lsr|lxor|mod|or)\\b/,\n    punctuation: /;;|::|[(){}\\[\\].,:;#]|\\b_\\b/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/ocaml.js\n"));

/***/ })

}]);