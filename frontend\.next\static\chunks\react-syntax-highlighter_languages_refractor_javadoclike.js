"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_javadoclike"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = javadoclike\njavadoclike.displayName = 'javadoclike'\njavadoclike.aliases = []\nfunction javadoclike(Prism) {\n  ;(function (Prism) {\n    var javaDocLike = (Prism.languages.javadoclike = {\n      parameter: {\n        pattern:\n          /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    })\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment'\n      var grammar = Prism.languages[lang]\n      if (!grammar) {\n        return\n      }\n      var token = grammar[tokenName]\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {}\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        }\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition)\n        token = grammar[tokenName]\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        }\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            }\n          }\n          callback(token[i])\n        }\n      } else {\n        callback(token)\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages]\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {}\n          }\n          pattern.inside.rest = docLanguage\n        })\n      })\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    })\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike)\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/javadoclike.js\n"));

/***/ })

}]);