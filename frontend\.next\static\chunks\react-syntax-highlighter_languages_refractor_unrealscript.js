"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_unrealscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/unrealscript.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/unrealscript.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = unrealscript\nunrealscript.displayName = 'unrealscript'\nunrealscript.aliases = ['uc', 'uscript']\nfunction unrealscript(Prism) {\n  Prism.languages.unrealscript = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    category: {\n      pattern:\n        /(\\b(?:(?:autoexpand|hide|show)categories|var)\\s*\\()[^()]+(?=\\))/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /(\\w\\s*)<\\s*\\w+\\s*=[^<>|=\\r\\n]+(?:\\|\\s*\\w+\\s*=[^<>|=\\r\\n]+)*>/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        property: /\\b\\w+(?=\\s*=)/,\n        operator: /=/,\n        punctuation: /[<>|]/\n      }\n    },\n    macro: {\n      pattern: /`\\w+/,\n      alias: 'property'\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:class|enum|extends|interface|state(?:\\(\\))?|struct|within)\\s+)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:abstract|actor|array|auto|autoexpandcategories|bool|break|byte|case|class|classgroup|client|coerce|collapsecategories|config|const|continue|default|defaultproperties|delegate|dependson|deprecated|do|dontcollapsecategories|editconst|editinlinenew|else|enum|event|exec|export|extends|final|float|for|forcescriptorder|foreach|function|goto|guid|hidecategories|hidedropdown|if|ignores|implements|inherits|input|int|interface|iterator|latent|local|material|name|native|nativereplication|noexport|nontransient|noteditinlinenew|notplaceable|operator|optional|out|pawn|perobjectconfig|perobjectlocalized|placeable|postoperator|preoperator|private|protected|reliable|replication|return|server|showcategories|simulated|singular|state|static|string|struct|structdefault|structdefaultproperties|switch|texture|transient|travel|unreliable|until|var|vector|while|within)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    // https://docs.unrealengine.com/udk/Three/UnrealScriptExpressions.html\n    operator:\n      />>|<<|--|\\+\\+|\\*\\*|[-+*/~!=<>$@]=?|&&?|\\|\\|?|\\^\\^?|[?:%]|\\b(?:ClockwiseFrom|Cross|Dot)\\b/,\n    punctuation: /[()[\\]{};,.]/\n  }\n  Prism.languages.uc = Prism.languages.uscript = Prism.languages.unrealscript\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/unrealscript.js\n"));

/***/ })

}]);