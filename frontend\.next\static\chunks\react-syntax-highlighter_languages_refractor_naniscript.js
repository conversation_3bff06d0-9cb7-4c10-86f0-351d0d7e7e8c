"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_naniscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/naniscript.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/naniscript.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = naniscript\nnaniscript.displayName = 'naniscript'\nnaniscript.aliases = []\nfunction naniscript(Prism) {\n  ;(function (Prism) {\n    var expressionDef = /\\{[^\\r\\n\\[\\]{}]*\\}/\n    var params = {\n      'quoted-string': {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        alias: 'operator'\n      },\n      'command-param-id': {\n        pattern: /(\\s)\\w+:/,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'command-param-value': [\n        {\n          pattern: expressionDef,\n          alias: 'selector'\n        },\n        {\n          pattern: /([\\t ])\\S+/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'operator'\n        },\n        {\n          pattern: /\\S(?:.*\\S)?/,\n          alias: 'operator'\n        }\n      ]\n    }\n    Prism.languages.naniscript = {\n      // ; ...\n      comment: {\n        pattern: /^([\\t ]*);.*/m,\n        lookbehind: true\n      },\n      // > ...\n      // Define is a control line starting with '>' followed by a word, a space and a text.\n      define: {\n        pattern: /^>.+/m,\n        alias: 'tag',\n        inside: {\n          value: {\n            pattern: /(^>\\w+[\\t ]+)(?!\\s)[^{}\\r\\n]+/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          key: {\n            pattern: /(^>)\\w+/,\n            lookbehind: true\n          }\n        }\n      },\n      // # ...\n      label: {\n        pattern: /^([\\t ]*)#[\\t ]*\\w+[\\t ]*$/m,\n        lookbehind: true,\n        alias: 'regex'\n      },\n      command: {\n        pattern: /^([\\t ]*)@\\w+(?=[\\t ]|$).*/m,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          'command-name': /^@\\w+/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'command-params': {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            inside: params\n          }\n        }\n      },\n      // Generic is any line that doesn't start with operators: ;>#@\n      'generic-text': {\n        pattern: /(^[ \\t]*)[^#@>;\\s].*/m,\n        lookbehind: true,\n        alias: 'punctuation',\n        inside: {\n          // \\{ ... \\} ... \\[ ... \\] ... \\\"\n          'escaped-char': /\\\\[{}\\[\\]\"]/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'inline-command': {\n            pattern: /\\[[\\t ]*\\w[^\\r\\n\\[\\]]*\\]/,\n            greedy: true,\n            alias: 'function',\n            inside: {\n              'command-params': {\n                pattern: /(^\\[[\\t ]*\\w+\\b)[\\s\\S]+(?=\\]$)/,\n                lookbehind: true,\n                inside: params\n              },\n              'command-param-name': {\n                pattern: /^(\\[[\\t ]*)\\w+/,\n                lookbehind: true,\n                alias: 'name'\n              },\n              'start-stop-char': /[\\[\\]]/\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.nani = Prism.languages['naniscript']\n    /** @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token */\n    /**\n     * This hook is used to validate generic-text tokens for balanced brackets.\n     * Mark token as bad-line when contains not balanced brackets: {},[]\n     */\n    Prism.hooks.add('after-tokenize', function (env) {\n      /** @type {(Token | string)[]} */\n      var tokens = env.tokens\n      tokens.forEach(function (token) {\n        if (typeof token !== 'string' && token.type === 'generic-text') {\n          var content = getTextContent(token)\n          if (!isBracketsBalanced(content)) {\n            token.type = 'bad-line'\n            token.content = content\n          }\n        }\n      })\n    })\n    /**\n     * @param {string} input\n     * @returns {boolean}\n     */\n    function isBracketsBalanced(input) {\n      var brackets = '[]{}'\n      var stack = []\n      for (var i = 0; i < input.length; i++) {\n        var bracket = input[i]\n        var bracketsIndex = brackets.indexOf(bracket)\n        if (bracketsIndex !== -1) {\n          if (bracketsIndex % 2 === 0) {\n            stack.push(bracketsIndex + 1)\n          } else if (stack.pop() !== bracketsIndex) {\n            return false\n          }\n        }\n      }\n      return stack.length === 0\n    }\n    /**\n     * @param {string | Token | (string | Token)[]} token\n     * @returns {string}\n     */\n    function getTextContent(token) {\n      if (typeof token === 'string') {\n        return token\n      } else if (Array.isArray(token)) {\n        return token.map(getTextContent).join('')\n      } else {\n        return getTextContent(token.content)\n      }\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/naniscript.js\n"));

/***/ })

}]);