"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_neon"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/neon.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/neon.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = neon\nneon.displayName = 'neon'\nneon.aliases = []\nfunction neon(Prism) {\n  Prism.languages.neon = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    datetime: {\n      pattern:\n        /(^|[[{(=:,\\s])\\d\\d\\d\\d-\\d\\d?-\\d\\d?(?:(?:[Tt]| +)\\d\\d?:\\d\\d:\\d\\d(?:\\.\\d*)? *(?:Z|[-+]\\d\\d?(?::?\\d\\d)?)?)?(?=$|[\\]}),\\s])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    key: {\n      pattern: /(^|[[{(,\\s])[^,:=[\\]{}()'\"\\s]+(?=\\s*:(?:$|[\\]}),\\s])|\\s*=)/,\n      lookbehind: true,\n      alias: 'atrule'\n    },\n    number: {\n      pattern:\n        /(^|[[{(=:,\\s])[+-]?(?:0x[\\da-fA-F]+|0o[0-7]+|0b[01]+|(?:\\d+(?:\\.\\d*)?|\\.?\\d+)(?:[eE][+-]?\\d+)?)(?=$|[\\]}),:=\\s])/,\n      lookbehind: true\n    },\n    boolean: {\n      pattern: /(^|[[{(=:,\\s])(?:false|no|true|yes)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true\n    },\n    null: {\n      pattern: /(^|[[{(=:,\\s])(?:null)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    string: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:('''|\"\"\")\\r?\\n(?:(?:[^\\r\\n]|\\r?\\n(?![\\t ]*\\2))*\\r?\\n)?[\\t ]*\\2|'[^'\\r\\n]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    literal: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:[^#\"',:=[\\]{}()\\s`-]|[:-][^\"',=[\\]{}()\\s])(?:[^,:=\\]})(\\s]|:(?![\\s,\\]})]|$)|[ \\t]+[^#,:=\\]})(\\s])*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    punctuation: /[,:=[\\]{}()-]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/neon.js\n"));

/***/ })

}]);