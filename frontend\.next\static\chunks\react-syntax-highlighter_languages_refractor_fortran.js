"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_fortran"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fortran.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fortran.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = fortran\nfortran.displayName = 'fortran'\nfortran.aliases = []\nfunction fortran(Prism) {\n  Prism.languages.fortran = {\n    'quoted-number': {\n      pattern: /[BOZ](['\"])[A-F0-9]+\\1/i,\n      alias: 'number'\n    },\n    string: {\n      pattern:\n        /(?:\\b\\w+_)?(['\"])(?:\\1\\1|&(?:\\r\\n?|\\n)(?:[ \\t]*!.*(?:\\r\\n?|\\n)|(?![ \\t]*!))|(?!\\1).)*(?:\\1|&)/,\n      inside: {\n        comment: {\n          pattern: /(&(?:\\r\\n?|\\n)\\s*)!.*/,\n          lookbehind: true\n        }\n      }\n    },\n    comment: {\n      pattern: /!.*/,\n      greedy: true\n    },\n    boolean: /\\.(?:FALSE|TRUE)\\.(?:_\\w+)?/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[ED][+-]?\\d+)?(?:_\\w+)?/i,\n    keyword: [\n      // Types\n      /\\b(?:CHARACTER|COMPLEX|DOUBLE ?PRECISION|INTEGER|LOGICAL|REAL)\\b/i, // END statements\n      /\\b(?:END ?)?(?:BLOCK ?DATA|DO|FILE|FORALL|FUNCTION|IF|INTERFACE|MODULE(?! PROCEDURE)|PROGRAM|SELECT|SUBROUTINE|TYPE|WHERE)\\b/i, // Statements\n      /\\b(?:ALLOCATABLE|ALLOCATE|BACKSPACE|CALL|CASE|CLOSE|COMMON|CONTAINS|CONTINUE|CYCLE|DATA|DEALLOCATE|DIMENSION|DO|END|EQUIVALENCE|EXIT|EXTERNAL|FORMAT|GO ?TO|IMPLICIT(?: NONE)?|INQUIRE|INTENT|INTRINSIC|MODULE PROCEDURE|NAMELIST|NULLIFY|OPEN|OPTIONAL|PARAMETER|POINTER|PRINT|PRIVATE|PUBLIC|READ|RETURN|REWIND|SAVE|SELECT|STOP|TARGET|WHILE|WRITE)\\b/i, // Others\n      /\\b(?:ASSIGNMENT|DEFAULT|ELEMENTAL|ELSE|ELSEIF|ELSEWHERE|ENTRY|IN|INCLUDE|INOUT|KIND|NULL|ONLY|OPERATOR|OUT|PURE|RECURSIVE|RESULT|SEQUENCE|STAT|THEN|USE)\\b/i\n    ],\n    operator: [\n      /\\*\\*|\\/\\/|=>|[=\\/]=|[<>]=?|::|[+\\-*=%]|\\.[A-Z]+\\./i,\n      {\n        // Use lookbehind to prevent confusion with (/ /)\n        pattern: /(^|(?!\\().)\\/(?!\\))/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\/|\\/\\)|[(),;:&]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/fortran.js\n"));

/***/ })

}]);