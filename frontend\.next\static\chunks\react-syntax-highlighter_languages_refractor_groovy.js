"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_groovy"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/groovy.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/groovy.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = groovy\ngroovy.displayName = 'groovy'\ngroovy.aliases = []\nfunction groovy(Prism) {\n  Prism.languages.groovy = Prism.languages.extend('clike', {\n    string: [\n      {\n        // https://groovy-lang.org/syntax.html#_dollar_slashy_string\n        pattern:\n          /(\"\"\"|''')(?:[^\\\\]|\\\\[\\s\\S])*?\\1|\\$\\/(?:[^/$]|\\$(?:[/$]|(?![/$]))|\\/(?!\\$))*\\/\\$/,\n        greedy: true\n      },\n      {\n        // TODO: Slash strings (e.g. /foo/) can contain line breaks but this will cause a lot of trouble with\n        // simple division (see JS regex), so find a fix maybe?\n        pattern: /([\"'/])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|trait|transient|try|void|volatile|while)\\b/,\n    number:\n      /\\b(?:0b[01_]+|0x[\\da-f_]+(?:\\.[\\da-f_p\\-]+)?|[\\d_]+(?:\\.[\\d_]+)?(?:e[+-]?\\d+)?)[glidf]?\\b/i,\n    operator: {\n      pattern:\n        /(^|[^.])(?:~|==?~?|\\?[.:]?|\\*(?:[.=]|\\*=?)?|\\.[@&]|\\.\\.<|\\.\\.(?!\\.)|-[-=>]?|\\+[+=]?|!=?|<(?:<=?|=>?)?|>(?:>>?=?|=)?|&[&=]?|\\|[|=]?|\\/=?|\\^=?|%=?)/,\n      lookbehind: true\n    },\n    punctuation: /\\.+|[{}[\\];(),:$]/\n  })\n  Prism.languages.insertBefore('groovy', 'string', {\n    shebang: {\n      pattern: /#!.+/,\n      alias: 'comment'\n    }\n  })\n  Prism.languages.insertBefore('groovy', 'punctuation', {\n    'spock-block': /\\b(?:and|cleanup|expect|given|setup|then|when|where):/\n  })\n  Prism.languages.insertBefore('groovy', 'function', {\n    annotation: {\n      pattern: /(^|[^.])@\\w+/,\n      lookbehind: true,\n      alias: 'punctuation'\n    }\n  }) // Handle string interpolation\n  Prism.hooks.add('wrap', function (env) {\n    if (env.language === 'groovy' && env.type === 'string') {\n      var delimiter = env.content.value[0]\n      if (delimiter != \"'\") {\n        var pattern = /([^\\\\])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        if (delimiter === '$') {\n          pattern = /([^\\$])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        } // To prevent double HTML-encoding we have to decode env.content first\n        env.content.value = env.content.value\n          .replace(/&lt;/g, '<')\n          .replace(/&amp;/g, '&')\n        env.content = Prism.highlight(env.content.value, {\n          expression: {\n            pattern: pattern,\n            lookbehind: true,\n            inside: Prism.languages.groovy\n          }\n        })\n        env.classes.push(delimiter === '/' ? 'regex' : 'gstring')\n      }\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/groovy.js\n"));

/***/ })

}]);