"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_asmatmel"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/asmatmel.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/asmatmel.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = asmatmel\nasmatmel.displayName = 'asmatmel'\nasmatmel.aliases = []\nfunction asmatmel(Prism) {\n  Prism.languages.asmatmel = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    constant: /\\b(?:PORT[A-Z]|DDR[A-Z]|(?:DD|P)[A-Z](?:\\d|[0-2]\\d|3[01]))\\b/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    'r-register': {\n      pattern: /\\br(?:\\d|[12]\\d|3[01])\\b/,\n      alias: 'variable'\n    },\n    'op-code': {\n      pattern:\n        /\\b(?:ADC|ADD|ADIW|AND|ANDI|ASR|BCLR|BLD|BRBC|BRBS|BRCC|BRCS|BREAK|BREQ|BRGE|BRHC|BRHS|BRID|BRIE|BRLO|BRLT|BRMI|BRNE|BRPL|BRSH|BRTC|BRTS|BRVC|BRVS|BSET|BST|CALL|CBI|CBR|CLC|CLH|CLI|CLN|CLR|CLS|CLT|CLV|CLZ|COM|CP|CPC|CPI|CPSE|DEC|DES|EICALL|EIJMP|ELPM|EOR|FMUL|FMULS|FMULSU|ICALL|IJMP|IN|INC|JMP|LAC|LAS|LAT|LD|LD[A-Za-z0-9]|LPM|LSL|LSR|MOV|MOVW|MUL|MULS|MULSU|NEG|NOP|OR|ORI|OUT|POP|PUSH|RCALL|RET|RETI|RJMP|ROL|ROR|SBC|SBCI|SBI|SBIC|SBIS|SBIW|SBR|SBRC|SBRS|SEC|SEH|SEI|SEN|SER|SES|SET|SEV|SEZ|SLEEP|SPM|ST|ST[A-Z0-9]|SUB|SUBI|SWAP|TST|WDR|XCH|adc|add|adiw|and|andi|asr|bclr|bld|brbc|brbs|brcc|brcs|break|breq|brge|brhc|brhs|brid|brie|brlo|brlt|brmi|brne|brpl|brsh|brtc|brts|brvc|brvs|bset|bst|call|cbi|cbr|clc|clh|cli|cln|clr|cls|clt|clv|clz|com|cp|cpc|cpi|cpse|dec|des|eicall|eijmp|elpm|eor|fmul|fmuls|fmulsu|icall|ijmp|in|inc|jmp|lac|las|lat|ld|ld[a-z0-9]|lpm|lsl|lsr|mov|movw|mul|muls|mulsu|neg|nop|or|ori|out|pop|push|rcall|ret|reti|rjmp|rol|ror|sbc|sbci|sbi|sbic|sbis|sbiw|sbr|sbrc|sbrs|sec|seh|sei|sen|ser|ses|set|sev|sez|sleep|spm|st|st[a-zA-Z0-9]|sub|subi|swap|tst|wdr|xch)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{2,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[acznvshtixy]\\b/i,\n      alias: 'variable'\n    },\n    operator: />>=?|<<=?|&&?|\\|\\|?|[-+*/%&|^!=<>?]=?/,\n    punctuation: /[(),:]/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/asmatmel.js\n"));

/***/ })

}]);