"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_maxscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/maxscript.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/maxscript.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = maxscript\nmaxscript.displayName = 'maxscript'\nmaxscript.aliases = []\nfunction maxscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:about|and|animate|as|at|attributes|by|case|catch|collect|continue|coordsys|do|else|exit|fn|for|from|function|global|if|in|local|macroscript|mapped|max|not|of|off|on|or|parameters|persistent|plugin|rcmenu|return|rollout|set|struct|then|throw|to|tool|try|undo|utility|when|where|while|with)\\b/i\n    Prism.languages.maxscript = {\n      comment: {\n        pattern: /\\/\\*[\\s\\S]*?(?:\\*\\/|$)|--.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /(^|[^\"\\\\@])(?:\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|@\"[^\"]*\")/,\n        lookbehind: true,\n        greedy: true\n      },\n      path: {\n        pattern: /\\$(?:[\\w/\\\\.*?]|'[^']*')*/,\n        greedy: true,\n        alias: 'string'\n      },\n      'function-call': {\n        pattern: RegExp(\n          '((?:' + // start of line\n            (/^/.source +\n              '|' + // operators and other language constructs\n              /[;=<>+\\-*/^({\\[]/.source +\n              '|' + // keywords as part of statements\n              /\\b(?:and|by|case|catch|collect|do|else|if|in|not|or|return|then|to|try|where|while|with)\\b/\n                .source) +\n            ')[ \\t]*)' +\n            '(?!' +\n            keywords.source +\n            ')' +\n            /[a-z_]\\w*\\b/.source +\n            '(?=[ \\t]*(?:' + // variable\n            ('(?!' +\n              keywords.source +\n              ')' +\n              /[a-z_]/.source +\n              '|' + // number\n              /\\d|-\\.?\\d/.source +\n              '|' + // other expressions or literals\n              /[({'\"$@#?]/.source) +\n            '))',\n          'im'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'function-definition': {\n        pattern: /(\\b(?:fn|function)\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      argument: {\n        pattern: /\\b[a-z_]\\w*(?=:)/i,\n        alias: 'attr-name'\n      },\n      keyword: keywords,\n      boolean: /\\b(?:false|true)\\b/,\n      time: {\n        pattern:\n          /(^|[^\\w.])(?:(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?[msft])+|\\d+:\\d+(?:\\.\\d*)?)(?![\\w.:])/,\n        lookbehind: true,\n        alias: 'number'\n      },\n      number: [\n        {\n          pattern:\n            /(^|[^\\w.])(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?|0x[a-fA-F0-9]+)(?![\\w.:])/,\n          lookbehind: true\n        },\n        /\\b(?:e|pi)\\b/\n      ],\n      constant: /\\b(?:dontcollect|ok|silentValue|undefined|unsupplied)\\b/,\n      color: {\n        pattern: /\\b(?:black|blue|brown|gray|green|orange|red|white|yellow)\\b/i,\n        alias: 'constant'\n      },\n      operator: /[-+*/<>=!]=?|[&^?]|#(?!\\()/,\n      punctuation: /[()\\[\\]{}.:,;]|#(?=\\()|\\\\$/m\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/maxscript.js\n"));

/***/ })

}]);