"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_avroIdl"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avro-idl.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avro-idl.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = avroIdl\navroIdl.displayName = 'avroIdl'\navroIdl.aliases = []\nfunction avroIdl(Prism) {\n  // GitHub: https://github.com/apache/avro\n  // Docs: https://avro.apache.org/docs/current/idl.html\n  Prism.languages['avro-idl'] = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    annotation: {\n      pattern: /@(?:[$\\w.-]|`[^\\r\\n`]+`)+/,\n      greedy: true,\n      alias: 'function'\n    },\n    'function-identifier': {\n      pattern: /`[^\\r\\n`]+`(?=\\s*\\()/,\n      greedy: true,\n      alias: 'function'\n    },\n    identifier: {\n      pattern: /`[^\\r\\n`]+`/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:enum|error|protocol|record|throws)\\b\\s+)[$\\w]+/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:array|boolean|bytes|date|decimal|double|enum|error|false|fixed|float|idl|import|int|local_timestamp_ms|long|map|null|oneway|protocol|record|schema|string|throws|time_ms|timestamp_ms|true|union|uuid|void)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: [\n      {\n        pattern:\n          /(^|[^\\w.])-?(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|0x(?:[a-f0-9]+(?:\\.[a-f0-9]*)?|\\.[a-f0-9]+)(?:p[+-]?\\d+)?)[dfl]?(?![\\w.])/i,\n        lookbehind: true\n      },\n      /-?\\b(?:Infinity|NaN)\\b/\n    ],\n    operator: /=/,\n    punctuation: /[()\\[\\]{}<>.:,;-]/\n  }\n  Prism.languages.avdl = Prism.languages['avro-idl']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/avro-idl.js\n"));

/***/ })

}]);