"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_actionscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/actionscript.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/actionscript.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = actionscript\nactionscript.displayName = 'actionscript'\nactionscript.aliases = []\nfunction actionscript(Prism) {\n  Prism.languages.actionscript = Prism.languages.extend('javascript', {\n    keyword:\n      /\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/,\n    operator: /\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/\n  })\n  Prism.languages.actionscript['class-name'].alias = 'function' // doesn't work with AS because AS is too complex\n  delete Prism.languages.actionscript['parameter']\n  delete Prism.languages.actionscript['literal-property']\n  if (Prism.languages.markup) {\n    Prism.languages.insertBefore('actionscript', 'string', {\n      xml: {\n        pattern:\n          /(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      }\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/actionscript.js\n"));

/***/ })

}]);