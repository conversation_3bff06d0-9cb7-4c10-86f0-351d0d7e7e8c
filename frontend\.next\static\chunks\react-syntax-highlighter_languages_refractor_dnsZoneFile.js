"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_dnsZoneFile"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dns-zone-file.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dns-zone-file.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = dnsZoneFile\ndnsZoneFile.displayName = 'dnsZoneFile'\ndnsZoneFile.aliases = []\nfunction dnsZoneFile(Prism) {\n  Prism.languages['dns-zone-file'] = {\n    comment: /;.*/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(^\\$ORIGIN[ \\t]+)\\S+/m,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|\\s)@(?=\\s|$)/,\n        lookbehind: true\n      }\n    ],\n    keyword: /^\\$(?:INCLUDE|ORIGIN|TTL)(?=\\s|$)/m,\n    class: {\n      // https://tools.ietf.org/html/rfc1035#page-13\n      pattern: /(^|\\s)(?:CH|CS|HS|IN)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      // https://en.wikipedia.org/wiki/List_of_DNS_record_types\n      pattern:\n        /(^|\\s)(?:A|A6|AAAA|AFSDB|APL|ATMA|CAA|CDNSKEY|CDS|CERT|CNAME|DHCID|DLV|DNAME|DNSKEY|DS|EID|GID|GPOS|HINFO|HIP|IPSECKEY|ISDN|KEY|KX|LOC|MAILA|MAILB|MB|MD|MF|MG|MINFO|MR|MX|NAPTR|NB|NBSTAT|NIMLOC|NINFO|NS|NSAP|NSAP-PTR|NSEC|NSEC3|NSEC3PARAM|NULL|NXT|OPENPGPKEY|PTR|PX|RKEY|RP|RRSIG|RT|SIG|SINK|SMIMEA|SOA|SPF|SRV|SSHFP|TA|TKEY|TLSA|TSIG|TXT|UID|UINFO|UNSPEC|URI|WKS|X25)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    punctuation: /[()]/\n  }\n  Prism.languages['dns-zone'] = Prism.languages['dns-zone-file']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/dns-zone-file.js\n"));

/***/ })

}]);