"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_qsharp"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qsharp.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qsharp.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = qsharp\nqsharp.displayName = 'qsharp'\nqsharp.aliases = ['qs']\nfunction qsharp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')'\n      })\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]')\n    } // https://docs.microsoft.com/en-us/azure/quantum/user-guide/language/typesystem/\n    // https://github.com/microsoft/qsharp-language/tree/main/Specifications/Language/5_Grammar\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'Adj BigInt Bool Ctl Double false Int One Pauli PauliI PauliX PauliY PauliZ Qubit Range Result String true Unit Zero',\n      // all other keywords\n      other:\n        'Adjoint adjoint apply as auto body borrow borrowing Controlled controlled distribute elif else fail fixup for function if in internal intrinsic invert is let mutable namespace new newtype open operation repeat return self set until use using while within'\n    } // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b'\n    }\n    var keywords = RegExp(\n      keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.other)\n    ) // types\n    var identifier = /\\b[A-Za-z_]\\w*\\b/.source\n    var qualifiedName = replace(/<<0>>(?:\\s*\\.\\s*<<0>>)*/.source, [identifier])\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    } // strings\n    var regularString = /\"(?:\\\\.|[^\\\\\"])*\"/.source\n    Prism.languages.qsharp = Prism.languages.extend('clike', {\n      comment: /\\/\\/.*/,\n      string: [\n        {\n          pattern: re(/(^|[^$\\\\])<<0>>/.source, [regularString]),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'class-name': [\n        {\n          // open Microsoft.Quantum.Canon;\n          // open Microsoft.Quantum.Canon as CN;\n          pattern: re(/(\\b(?:as|open)\\s+)<<0>>(?=\\s*(?:;|as\\b))/.source, [\n            qualifiedName\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // namespace Quantum.App1;\n          pattern: re(/(\\bnamespace\\s+)<<0>>(?=\\s*\\{)/.source, [qualifiedName]),\n          lookbehind: true,\n          inside: typeInside\n        }\n      ],\n      keyword: keywords,\n      number:\n        /(?:\\b0(?:x[\\da-f]+|b[01]+|o[0-7]+)|(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d*)?)(?:e[-+]?\\d+)?)l?\\b/i,\n      operator:\n        /\\band=|\\bor=|\\band\\b|\\bnot\\b|\\bor\\b|<[-=]|[-=]>|>>>=?|<<<=?|\\^\\^\\^=?|\\|\\|\\|=?|&&&=?|w\\/=?|~~~|[*\\/+\\-^=!%]=?/,\n      punctuation: /::|[{}[\\];(),.:]/\n    })\n    Prism.languages.insertBefore('qsharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    }) // single line\n    var interpolationExpr = nested(\n      replace(/\\{(?:[^\"{}]|<<0>>|<<self>>)*\\}/.source, [regularString]),\n      2\n    )\n    Prism.languages.insertBefore('qsharp', 'string', {\n      'interpolation-string': {\n        pattern: re(/\\$\"(?:\\\\.|<<0>>|[^\\\\\"{])*\"/.source, [interpolationExpr]),\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: re(/((?:^|[^\\\\])(?:\\\\\\\\)*)<<0>>/.source, [\n              interpolationExpr\n            ]),\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\{|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                alias: 'language-qsharp',\n                inside: Prism.languages.qsharp\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    })\n  })(Prism)\n  Prism.languages.qs = Prism.languages.qsharp\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qsharp.js\n"));

/***/ })

}]);