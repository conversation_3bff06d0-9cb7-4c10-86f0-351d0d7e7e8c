"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_kotlin"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kotlin.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kotlin.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = kotlin\nkotlin.displayName = 'kotlin'\nkotlin.aliases = ['kt', 'kts']\nfunction kotlin(Prism) {\n  ;(function (Prism) {\n    Prism.languages.kotlin = Prism.languages.extend('clike', {\n      keyword: {\n        // The lookbehind prevents wrong highlighting of e.g. kotlin.properties.get\n        pattern:\n          /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,\n        lookbehind: true\n      },\n      function: [\n        {\n          pattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,\n          greedy: true\n        },\n        {\n          pattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      number:\n        /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,\n      operator:\n        /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/\n    })\n    delete Prism.languages.kotlin['class-name']\n    var interpolationInside = {\n      'interpolation-punctuation': {\n        pattern: /^\\$\\{?|\\}$/,\n        alias: 'punctuation'\n      },\n      expression: {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.kotlin\n      }\n    }\n    Prism.languages.insertBefore('kotlin', 'string', {\n      // https://kotlinlang.org/spec/expressions.html#string-interpolation-expressions\n      'string-literal': [\n        {\n          pattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,\n          alias: 'multiline',\n          inside: {\n            interpolation: {\n              pattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,\n          alias: 'singleline',\n          inside: {\n            interpolation: {\n              pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              lookbehind: true,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        }\n      ],\n      char: {\n        // https://kotlinlang.org/spec/expressions.html#character-literals\n        pattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,\n        greedy: true\n      }\n    })\n    delete Prism.languages.kotlin['string']\n    Prism.languages.insertBefore('kotlin', 'keyword', {\n      annotation: {\n        pattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,\n        alias: 'builtin'\n      }\n    })\n    Prism.languages.insertBefore('kotlin', 'function', {\n      label: {\n        pattern: /\\b\\w+@|@\\w+\\b/,\n        alias: 'symbol'\n      }\n    })\n    Prism.languages.kt = Prism.languages.kotlin\n    Prism.languages.kts = Prism.languages.kotlin\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/kotlin.js\n"));

/***/ })

}]);