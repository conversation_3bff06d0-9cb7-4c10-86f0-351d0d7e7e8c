import { NextRequest } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8001';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const filePath = searchParams.get('file_path');
  if (!filePath) {
    return new Response('缺少 file_path 参数', { status: 400 });
  }

  const backendUrl = `${BACKEND_URL}/api/word/download?file_path=${encodeURIComponent(filePath)}`;

  try {
    const res = await fetch(backendUrl);
    if (!res.ok) {
      const text = await res.text();
      return new Response(text || '后端下载失败', { status: res.status });
    }

    const contentType = res.headers.get('content-type') || 'application/octet-stream';
    const contentDisposition = res.headers.get('content-disposition') || undefined;
    const body = await res.arrayBuffer();

    const headers = new Headers();
    headers.set('Content-Type', contentType);
    if (contentDisposition) headers.set('Content-Disposition', contentDisposition);
    headers.set('Cache-Control', 'no-store');

    return new Response(body, { status: 200, headers });
  } catch (e) {
    const msg = e instanceof Error ? e.message : '下载代理失败';
    return new Response(msg, { status: 500 });
  }
}



