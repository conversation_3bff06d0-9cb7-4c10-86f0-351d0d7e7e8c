"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_squirrel"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/squirrel.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/squirrel.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = squirrel\nsquirrel.displayName = 'squirrel'\nsquirrel.aliases = []\nfunction squirrel(Prism) {\n  Prism.languages.squirrel = Prism.languages.extend('clike', {\n    comment: [\n      Prism.languages.clike['comment'][0],\n      {\n        pattern: /(^|[^\\\\:])(?:\\/\\/|#).*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(^|[^\\\\\"'@])(?:@\"(?:[^\"]|\"\")*\"(?!\")|\"(?:[^\\\\\\r\\n\"]|\\\\.)*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|enum|extends|instanceof)\\s+)\\w+(?:\\.\\w+)*/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:__FILE__|__LINE__|base|break|case|catch|class|clone|const|constructor|continue|default|delete|else|enum|extends|for|foreach|function|if|in|instanceof|local|null|resume|return|static|switch|this|throw|try|typeof|while|yield)\\b/,\n    number: /\\b(?:0x[0-9a-fA-F]+|\\d+(?:\\.(?:\\d+|[eE][+-]?\\d+))?)\\b/,\n    operator: /\\+\\+|--|<=>|<[-<]|>>>?|&&?|\\|\\|?|[-+*/%!=<>]=?|[~^]|::?/,\n    punctuation: /[(){}\\[\\],;.]/\n  })\n  Prism.languages.insertBefore('squirrel', 'string', {\n    char: {\n      pattern: /(^|[^\\\\\"'])'(?:[^\\\\']|\\\\(?:[xuU][0-9a-fA-F]{0,8}|[\\s\\S]))'/,\n      lookbehind: true,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('squirrel', 'operator', {\n    'attribute-punctuation': {\n      pattern: /<\\/|\\/>/,\n      alias: 'important'\n    },\n    lambda: {\n      pattern: /@(?=\\()/,\n      alias: 'operator'\n    }\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/squirrel.js\n"));

/***/ })

}]);