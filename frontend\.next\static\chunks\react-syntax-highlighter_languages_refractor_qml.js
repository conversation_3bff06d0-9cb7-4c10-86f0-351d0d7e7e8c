"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_qml"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qml.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qml.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = qml\nqml.displayName = 'qml'\nqml.aliases = []\nfunction qml(Prism) {\n  ;(function (Prism) {\n    var jsString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|'(?:\\\\.|[^\\\\'\\r\\n])*'/.source\n    var jsComment = /\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\//.source\n    var jsExpr =\n      /(?:[^\\\\()[\\]{}\"'/]|<string>|\\/(?![*/])|<comment>|\\(<expr>*\\)|\\[<expr>*\\]|\\{<expr>*\\}|\\\\[\\s\\S])/.source\n        .replace(/<string>/g, function () {\n          return jsString\n        })\n        .replace(/<comment>/g, function () {\n          return jsComment\n        }) // the pattern will blow up, so only a few iterations\n    for (var i = 0; i < 2; i++) {\n      jsExpr = jsExpr.replace(/<expr>/g, function () {\n        return jsExpr\n      })\n    }\n    jsExpr = jsExpr.replace(/<expr>/g, '[^\\\\s\\\\S]')\n    Prism.languages.qml = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      'javascript-function': {\n        pattern: RegExp(\n          /((?:^|;)[ \\t]*)function\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*\\(<js>*\\)\\s*\\{<js>*\\}/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      'class-name': {\n        pattern: /((?:^|[:;])[ \\t]*)(?!\\d)\\w+(?=[ \\t]*\\{|[ \\t]+on\\b)/m,\n        lookbehind: true\n      },\n      property: [\n        {\n          pattern: /((?:^|[;{])[ \\t]*)(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /((?:^|[;{])[ \\t]*)property[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^property/,\n            property: /\\w+(?:\\.\\w+)*/\n          }\n        }\n      ],\n      'javascript-expression': {\n        pattern: RegExp(\n          /(:[ \\t]*)(?![\\s;}[])(?:(?!$|[;}])<js>)+/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      keyword: /\\b(?:as|import|on)\\b/,\n      punctuation: /[{}[\\]:;,]/\n    }\n  })(Prism)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/qml.js\n"));

/***/ })

}]);