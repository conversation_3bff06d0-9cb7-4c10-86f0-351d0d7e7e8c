"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_erlang"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/erlang.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/erlang.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = erlang\nerlang.displayName = 'erlang'\nerlang.aliases = []\nfunction erlang(Prism) {\n  Prism.languages.erlang = {\n    comment: /%.+/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    'quoted-function': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'(?=\\()/,\n      alias: 'function'\n    },\n    'quoted-atom': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'/,\n      alias: 'atom'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: /\\b(?:after|case|catch|end|fun|if|of|receive|try|when)\\b/,\n    number: [\n      /\\$\\\\?./,\n      /\\b\\d+#[a-z0-9]+/i,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    ],\n    function: /\\b[a-z][\\w@]*(?=\\()/,\n    variable: {\n      // Look-behind is used to prevent wrong highlighting of atoms containing \"@\"\n      pattern: /(^|[^@])(?:\\b|\\?)[A-Z_][\\w@]*/,\n      lookbehind: true\n    },\n    operator: [\n      /[=\\/<>:]=|=[:\\/]=|\\+\\+?|--?|[=*\\/!]|\\b(?:and|andalso|band|bnot|bor|bsl|bsr|bxor|div|not|or|orelse|rem|xor)\\b/,\n      {\n        // We don't want to match <<\n        pattern: /(^|[^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /(^|[^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    atom: /\\b[a-z][\\w@]*/,\n    punctuation: /[()[\\]{}:;,.#|]|<<|>>/\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/erlang.js\n"));

/***/ })

}]);