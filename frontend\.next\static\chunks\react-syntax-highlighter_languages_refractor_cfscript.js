"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["react-syntax-highlighter_languages_refractor_cfscript"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cfscript.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cfscript.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nmodule.exports = cfscript\ncfscript.displayName = 'cfscript'\ncfscript.aliases = []\nfunction cfscript(Prism) {\n  // https://cfdocs.org/script\n  Prism.languages.cfscript = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n        lookbehind: true,\n        inside: {\n          annotation: {\n            pattern: /(?:^|[^.])@[\\w\\.]+/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|break|catch|component|continue|default|do|else|extends|final|finally|for|function|if|in|include|package|private|property|public|remote|required|rethrow|return|static|switch|throw|try|var|while|xml)\\b(?!\\s*=)/,\n    operator: [\n      /\\+\\+|--|&&|\\|\\||::|=>|[!=]==|<=?|>=?|[-+*/%&|^!=<>]=?|\\?(?:\\.|:)?|[?:]/,\n      /\\b(?:and|contains|eq|equal|eqv|gt|gte|imp|is|lt|lte|mod|not|or|xor)\\b/\n    ],\n    scope: {\n      pattern:\n        /\\b(?:application|arguments|cgi|client|cookie|local|session|super|this|variables)\\b/,\n      alias: 'global'\n    },\n    type: {\n      pattern:\n        /\\b(?:any|array|binary|boolean|date|guid|numeric|query|string|struct|uuid|void|xml)\\b/,\n      alias: 'builtin'\n    }\n  })\n  Prism.languages.insertBefore('cfscript', 'keyword', {\n    // This must be declared before keyword because we use \"function\" inside the lookahead\n    'function-variable': {\n      pattern:\n        /[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n      alias: 'function'\n    }\n  })\n  delete Prism.languages.cfscript['class-name']\n  Prism.languages.cfc = Prism.languages['cfscript']\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/lang/cfscript.js\n"));

/***/ })

}]);